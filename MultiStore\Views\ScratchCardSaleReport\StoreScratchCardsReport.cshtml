﻿@model IEnumerable<MultiStore.Persistance.StoreScratchCardSales>

@{
    ViewData["Title"] = "Scratch Card Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed


    // Grouping the data by Store and ScratchCard and calculating totals
    var groupedData = Model
        .GroupBy(x => new { StoreName = x.Store.Name, ScratchCardName = x.ScratchCard.Name })
        .Select(g => new
        {
            StoreName = g.Key.StoreName,
            ScratchCardName = g.Key.ScratchCardName,
            TotalSalesCount = g.Sum(x => x.SalesCount),
            TotalActivationCount = g.Sum(x => x.ActivationCount),
            // Get the latest closing count for each product in the store
            LastClosingCount = g.OrderByDescending(x => x.Day).FirstOrDefault()?.CloseCount,
            SalesAmount = g.Sum(x => x.ScratchCard.Price * x.SalesCount)
        })
        .ToList();

    // Calculate totals for the entire period
    var totalSaleCount = groupedData.Sum(x => x.TotalSalesCount);
    var totalActivationCount = groupedData.Sum(x => x.TotalActivationCount);
    var totalSalesAmount = groupedData.Sum(x => x.SalesAmount);
}

<h2>Scratch Card Report</h2>

<div>
    <p><strong>Store:</strong> @ViewData["StoreName"]</p>
    <p><strong>Company:</strong> @ViewData["CompanyName"]</p>
    <p><strong>From:</strong> @ViewData["StartDate"] <strong>To:</strong> @ViewData["EndDate"]</p>
</div>

<table class="table">
    <thead>
        <tr>
            <th>Store</th>
            <th>Product</th>
            <th>Total Sales Count</th>
            <th>Total Activation Count</th>
            <th>Last Closing Count</th>
            <th>SalesAmount</th>

        </tr>
    </thead>
    <tbody>
        @foreach (var item in groupedData)
        {
            <tr>
                <td>@item.StoreName</td>
                <td>@item.ScratchCardName</td>
                <td>@item.TotalSalesCount</td>
                <td>@item.TotalActivationCount</td>
                <td>@item.LastClosingCount</td>
                <td>@item.SalesAmount</td>
            </tr>
        }
        <tr>
            <td colspan="2"><strong>Total</strong></td>
            <td><strong>@totalSaleCount</strong></td>
            <td><strong>@totalActivationCount</strong></td>
            <td></td>
            <td><strong>@totalSalesAmount</strong></td>
        </tr>
    </tbody>
</table>
