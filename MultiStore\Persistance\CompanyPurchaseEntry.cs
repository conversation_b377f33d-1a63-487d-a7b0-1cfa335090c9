﻿namespace MultiStore.Persistance
{
    public class CompanyPurchaseEntry
    {
        public int Id { get; set; }

        public Company Company { get; set; }

        public int CompanyId { get; set; }

        public string InvoiceNumber { get; set; }

        public Supplier Supplier { get; set; }

        public int SupplierId { get; set; }


        public decimal Gross { get; set; }

        public decimal Vat { get; set; }

        public decimal Net { get; set; }

        public DateTime Date { get; set; }
    }
}
