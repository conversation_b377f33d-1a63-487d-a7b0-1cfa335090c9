﻿using MultiStore.Persistance;

namespace MultiStore.Models
{
    public class CommisionReportSelectViewModel
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? SingleDate { get; set; }
        public string Period { get; set; }

        public List<Store> Stores { get; set; }

        public List<Company> Companies { get; set; }

        public int SelectedStoreId { get; set; }
        public int SelectedCompanyId { get; set; }

        public string TransactionType { get; set; } = "both";
    }


    public class CommisionReportDisplayViewModel
    {
        public string CompnayName { get; set; }

        public string StoreName { get; set; }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public List<CommisionEntryDTO> CommisionEntries { get; set; }

        public string TransactionType { get; set; }

    }
}
