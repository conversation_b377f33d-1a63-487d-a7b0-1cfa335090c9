﻿@model IEnumerable<MultiStore.Persistance.PurchaseEntry>
>

@{
    ViewData["Title"] = "Purchase Entries";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2>Purchase Entries</h2>

<table class="table">
    <thead>
        <tr>
            <th>
                Store
            </th>
             <th>
                  Invoice Number
            </th>
            <th>
                Supplier
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Gross)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Vat)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Net)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Date)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.Store.Name)
                </td>
                 <td>
                    @Html.DisplayFor(modelItem => item.InvoiceNumber)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Supplier.Name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Gross)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Vat)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Net)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Date)
                </td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@item.Id">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
