﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{

    //purchase report controller
    public class ReportsController : Controller
    {
        private readonly Data _context;

        public ReportsController(Data context)
        {
            _context = context;
        }


        public IActionResult Index()
        {
            ReportViewModel reportViewModel = new ReportViewModel();
            reportViewModel.Companies = _context.Companies.ToList();
            reportViewModel.Stores = _context.Stores.ToList();

            return View(reportViewModel);
        }

        [HttpPost]
        public IActionResult Submit(ReportViewModel model)
        {
            try
            {
                bool DueDate = false;

                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;
                PurchaseReportViewModel p = new();

                if (model.StartDate != null && model.EndDate != null)
                {
                    if (model.StartDate > model.EndDate)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDate;
                        endDate = (DateTime)model.EndDate;
                    }
                }
                else if (model.SingleDate != null)
                {


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {
                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDate.Value.AddMonths(-2);
                            endDate = model.SingleDate.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }

                }
                else if (model.StartDateDue != null && model.EndDateDue != null)
                {
                    DueDate = true;


                    if (model.StartDateDue > model.EndDateDue)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDateDue;
                        endDate = (DateTime)model.EndDateDue;
                    }

                }
                else if (model.SingleDateDue != null)
                {
                    DueDate = true;


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {


                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDateDue.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDateDue.Value.Year, model.SingleDateDue.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDateDue.Value.AddMonths(-2);
                            endDate = model.SingleDateDue.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDateDue.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDateDue.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }
                }

                if (model.SelectedStoreId != 0)
                {

                    List<PurchaseEntry>? purchaseEntries = null;

                    if (DueDate == false)
                    {
                        purchaseEntries = _context.PurchaseEntries
                            .Include(p => p.Supplier)
                         .Where(p => p.StoreId == model.SelectedStoreId && p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                         .ToList();
                    }
                    else
                    {
                        purchaseEntries = _context.PurchaseEntries
                            .Include(p => p.Supplier)
                         .Where(p => p.StoreId == model.SelectedStoreId && p.DueDate.Date >= startDate.Date && p.DueDate.Date <= endDate.Date)
                         .ToList();
                    }

                    p.StartDate = startDate;
                    p.EndDate = endDate;

                    p.StoreName = _context.Stores
                     .Where(s => s.Id == model.SelectedStoreId)
                     .FirstOrDefault().Name;

                    p.CompnayName = _context.Companies
                     .Where(c => c.Id == _context.Stores
                     .Where(s => s.Id == model.SelectedStoreId)
                     .FirstOrDefault().CompanyId)
                     .FirstOrDefault().Name;

                    List<PurchaseEntryDTO> purchaseEntryDtos = new List<PurchaseEntryDTO>();

                    foreach (var item in purchaseEntries)
                    {
                        purchaseEntryDtos.Add(new PurchaseEntryDTO()
                        {
                            StoreName = p.StoreName
                            ,
                            InvoiceNumber = item.InvoiceNumber
                            ,
                            Date = item.Date
                            ,
                            SupplierName = item.Supplier.Name
                            ,
                            Gross = item.Gross,
                            Vat = item.Vat
                            ,
                            Net = item.Net,
                             DueDate = item.DueDate
                        });
                    }


                    p.PurchaseEntries = purchaseEntryDtos;


                    return View("PurchaseReport", p);

                }
                else if (model.SelectedCompanyId != 0)
                {

                    List<PurchaseEntry> purchaseEntries = new();

                    var stores = _context.Stores.Where(s => s.CompanyId == model.SelectedCompanyId)
                        .ToList();

                    if (DueDate == false)
                    {
                        foreach (var store in stores)
                        {
                            purchaseEntries.AddRange(_context.PurchaseEntries
                            .Include(p => p.Supplier)
                         .Where(p => p.StoreId == store.Id && p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                            .ToList());

                        }
                    }
                    else
                    {
                        foreach (var store in stores)
                        {
                            purchaseEntries.AddRange(_context.PurchaseEntries
                            .Include(p => p.Supplier)
                         .Where(p => p.StoreId == store.Id && p.DueDate.Date >= startDate.Date && p.DueDate.Date <= endDate.Date)
                            .ToList());

                        }
                    }



                    p.StartDate = startDate;
                    p.EndDate = endDate;

                    p.StoreName = "All";
                    p.CompnayName = _context.Companies.FirstOrDefault(c => c.Id == model.SelectedCompanyId).Name;

                    List<PurchaseEntryDTO> purchaseEntryDtos = new List<PurchaseEntryDTO>();

                    foreach (var item in purchaseEntries)
                    {
                        purchaseEntryDtos.Add(new PurchaseEntryDTO()
                        {
                            StoreName = p.StoreName
                            ,
                            InvoiceNumber = item.InvoiceNumber
                            ,
                            Date = item.Date
                            ,
                            SupplierName = item.Supplier.Name
                            ,
                            Gross = item.Gross,
                            Vat = item.Vat
                            ,
                            Net = item.Net,
                            DueDate = item.DueDate

                        });
                    }


                    p.PurchaseEntries = purchaseEntryDtos;


                    return View("PurchaseReport", p);
                }
                else //both compnay id and store id =0 
                {
                    List<PurchaseEntry> purchaseEntries = new();

                    if (DueDate == false)
                    {
                        purchaseEntries.AddRange(_context.PurchaseEntries
                            .Include(p => p.Supplier).Include(p => p.Store)
                            .Where(p => p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                            .ToList());
                    }
                    else
                    {
                        purchaseEntries.AddRange(_context.PurchaseEntries
                          .Include(p => p.Supplier).Include(p => p.Store)
                          .Where(p => p.DueDate.Date >= startDate.Date && p.DueDate.Date <= endDate.Date)
                          .ToList());
                    }



                    p.StartDate = startDate;
                    p.EndDate = endDate;

                    p.StoreName = "All";
                    p.CompnayName = "All";

                    List<PurchaseEntryDTO> purchaseEntryDtos = new List<PurchaseEntryDTO>();

                    foreach (var item in purchaseEntries)
                    {
                        purchaseEntryDtos.Add(new PurchaseEntryDTO()
                        {
                            StoreName = item.Store.Name
                            ,
                            Date = item.Date
                            ,
                            InvoiceNumber = item.InvoiceNumber

                            ,
                            SupplierName = item.Supplier.Name
                            ,
                            Gross = item.Gross,
                            Vat = item.Vat
                            ,
                            Net = item.Net,
                            DueDate = item.DueDate

                        });
                    }


                    p.PurchaseEntries = purchaseEntryDtos;


                    return View("PurchaseReport", p);
                }


            }
            catch (Exception ex)
            {

                return View("Index");
            }
        }

        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }
    }
}
