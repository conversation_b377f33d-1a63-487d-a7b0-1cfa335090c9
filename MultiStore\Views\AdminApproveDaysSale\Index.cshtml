﻿@model IEnumerable<dynamic>

@{
    ViewData["Title"] = "Finalize";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<table class="table table-bordered">
    <thead>
        <tr>
            <th>Store Name</th>
            <th>Day</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    <a asp-controller="StoreDashboard" asp-action="Sections" asp-route-storeId="@item.storeId">
                        @item.store
                    </a>
                </td>
                <td>@item.Day.ToString("yyyy-MM-dd")</td>
            </tr>
        }
    </tbody>
</table>
