﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;


namespace MultiStore.Controllers
{
    public class AdminBackOfficeCardPaymentEntryController : Controller
    {

        private readonly Data _context;

        public AdminBackOfficeCardPaymentEntryController(Data context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var BackOfficeCardPaymentEntries = _context.BackOfficeCardPaymentEntries
                .Include(c => c.Store)
                .Include(c => c.CardType);
            return View(await BackOfficeCardPaymentEntries.ToListAsync());
        }

        // GET: AdminCommisionEntry/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var BackOfficeCardPaymentEntry = await _context.BackOfficeCardPaymentEntries.FindAsync(id);
            if (BackOfficeCardPaymentEntry == null)
            {
                return NotFound();
            }

            ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", BackOfficeCardPaymentEntry.StoreId);
            ViewData["CardTypeId"] = new SelectList(_context.CardTypes, "Id", "Name", BackOfficeCardPaymentEntry.CardTypeId);

            return View(BackOfficeCardPaymentEntry);
        }

        // POST: AdminCommisionEntry/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,StoreId,CardTypeId,Amount,Date")] BackOfficeCardPaymentEntry backOfficeCardPaymentEntry)
        {
            if (id != backOfficeCardPaymentEntry.Id)
            {
                return NotFound();
            }

            // if (ModelState.IsValid)
            // {
            try
            {
                _context.Update(backOfficeCardPaymentEntry);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!BackOfficeCardPaymentEntryExists(backOfficeCardPaymentEntry.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Index));
            // }

            //  ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", commisionEntry.StoreId);
            //  ViewData["SupplierId"] = new SelectList(_context.Suppliers, "Id", "Name", commisionEntry.SupplierId);

            //   return View(commisionEntry);
        }

        // GET: AdminCommisionEntry/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var item = await _context.BackOfficeCardPaymentEntries
                .Include(c => c.Store)
                .Include(c => c.CardType)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (item == null)
            {
                return NotFound();
            }

            return View(item);
        }

        // POST: AdminCommisionEntry/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var item = await _context.BackOfficeCardPaymentEntries.FindAsync(id);
            _context.BackOfficeCardPaymentEntries.Remove(item);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool BackOfficeCardPaymentEntryExists(int id)
        {
            return _context.BackOfficeCardPaymentEntries.Any(e => e.Id == id);
        }

    }
}
