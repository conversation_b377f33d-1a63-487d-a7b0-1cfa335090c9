﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;
using System.Linq;
using System.Threading.Tasks;

namespace MultiStore.Controllers
{
    [TypeFilter(typeof(AdminAuthorizationFilter))]
    public class PaymentCardController : Controller
    {
        private readonly Data _context;

        public PaymentCardController(Data context)
        {
            _context = context;
        }

        // GET: PaymentCard
        public async Task<IActionResult> Index()
        {
            var paymentCards = _context.PaymentCards
                .Include(pc => pc.CardType);

            return View(await paymentCards.ToListAsync());
        }

        // GET: PaymentCard/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var paymentCard = await _context.PaymentCards
                .FirstOrDefaultAsync(m => m.Id == id);
            if (paymentCard == null)
            {
                return NotFound();
            }

            return View(paymentCard);
        }

        // GET: PaymentCard/Create
        public IActionResult Create()
        {
            ViewData["CardTypeId"] = new SelectList(_context.CardTypes, "Id", "Name");

            return View();
        }

        // POST: PaymentCard/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Name,CardTypeId")] PaymentCard paymentCard)
        {
            //  if (ModelState.IsValid)
            //  {
            _context.Add(paymentCard);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
            //  }
            //  return View(paymentCard);
        }

        // GET: PaymentCard/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var paymentCard = await _context.PaymentCards.FindAsync(id);
            if (paymentCard == null)
            {
                return NotFound();
            }
            ViewData["CardTypeId"] = new SelectList(_context.CardTypes, "Id", "Name");

            return View(paymentCard);
        }

        // POST: PaymentCard/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,CardTypeId")] PaymentCard paymentCard)
        {
            if (id != paymentCard.Id)
            {
                return NotFound();
            }

            //  if (ModelState.IsValid)
            // {
            try
            {
                _context.Update(paymentCard);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!PaymentCardExists(paymentCard.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Index));
            //  }
            // return View(paymentCard);
        }

        // GET: PaymentCard/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var paymentCard = await _context.PaymentCards
                .FirstOrDefaultAsync(m => m.Id == id);
            if (paymentCard == null)
            {
                return NotFound();
            }

            return View(paymentCard);
        }

        // POST: PaymentCard/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var paymentCard = await _context.PaymentCards.FindAsync(id);
            _context.PaymentCards.Remove(paymentCard);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool PaymentCardExists(int id)
        {
            return _context.PaymentCards.Any(e => e.Id == id);
        }
    }
}
