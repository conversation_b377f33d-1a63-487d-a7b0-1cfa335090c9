﻿using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using Newtonsoft.Json;
using System.Diagnostics.Eventing.Reader;
using System.Linq;
using System.Net.Mail;
using System.Net;
using System.Runtime.CompilerServices;

namespace MultiStore.Controllers
{
    [TypeFilter(typeof(StoreManagerAuthorizationFilter))]
    public class StoreDashboardController : Controller
    {
        private readonly Data _context;
        private readonly ICompositeViewEngine _viewEngine;
        private readonly IUtilityService _utilityService;


        public StoreDashboardController(Data context, ICompositeViewEngine viewEngine, IUtilityService utilityService)
        {



            _context = context;
            _viewEngine = viewEngine;
            _utilityService = utilityService;
            var stores = _context.Stores.ToList();

        }

        public IActionResult Index()
        {
            List<Store> stores = new List<Store>();

            if (HttpContext.Session.GetString("UserType") != null && HttpContext.Session.GetString("UserType") == "Admin")
            {
                stores = _context.Stores.ToList();
            }

            else if (HttpContext.Session.GetString("UserType") != null && HttpContext.Session.GetString("UserType") == "StoreManager")
            {
                var storeId = HttpContext.Session.GetInt32("UserStoreId");
                stores = _context.Stores.Where(s => s.Id == storeId && s.Active == true).ToList();


                if (stores.Count() == 0) return Content("Sorry, No Stores");
            }


            return View(stores);
        }


        //AcceptStorePaidIn
        public IActionResult AcceptStorePaidIn(int storePaidInId)
        {
            var storePaidIn = _context.StorePaidIns.FirstOrDefault(f => f.Id == storePaidInId);

            if (storePaidIn != null)
            {
                storePaidIn.StorePaidInState = StorePaidInState.Accepted;
                _context.SaveChanges();
            }

            return RedirectToAction("Sections", new { storeId = storePaidIn.TransferToStoreId });
        }


        //RejectStorePaidIn
        public IActionResult RejectStorePaidIn(int storePaidInId)
        {
            var storePaidIn = _context.StorePaidIns.FirstOrDefault(f => f.Id == storePaidInId);

            if (storePaidIn != null)
            {
                storePaidIn.StorePaidInState = StorePaidInState.Rejected;
                _context.SaveChanges();
            }

            //because the transferred from store have only one value for transferedamount (not a sum)
            //simply get the store day from store paid in , set amount to 0 and transfered to store id as 0
            var storeDay = _context.StoreDays.First(s => s.Id == storePaidIn.TransferredFromStoreDayId);
            storeDay.StoreTransferAmount = 0;
            storeDay.TransferredStoreId = 0;
            _context.SaveChanges();

            

            return RedirectToAction("Sections", new { storeId = storePaidIn.TransferToStoreId });
        }

        public IActionResult AcceptFinalize(int storeId)
        {

            var store = _context.StoreDays.First(s => s.StoreId == storeId &&
               s.FinalizeApplied == true && s.FinalizeAccepted == false);

            store.FinalizeAccepted = true;

            _context.SaveChanges();

            var inputDate = _context.StoreDays.Include(s => s.Store)
                  .Where(s => s.StoreId == storeId && s.IsFinalised == false).FirstOrDefault().Day.Date;





            decimal NextDayCarryFwd = 0;

            {





                var storeDay = _context.StoreDays
              .Where(sd => sd.StoreId == storeId && sd.Day == inputDate)
              .FirstOrDefault();

                var storePaymentMethods = _context.StorePaymentMethods
                .Where(spm => spm.StoreId == storeId && spm.Date.Date == inputDate.Date)
                .ToList();

                StoreBalanceViewModel storeBalanceViewModel = new();
                storeBalanceViewModel.StoreId = storeId;
                storeBalanceViewModel.PaidIn = storeDay.PaidIn;
                storeBalanceViewModel.Atm = storeDay.Atm;
                storeBalanceViewModel.BankDeposit = storeDay.BankDeposit;
                storeBalanceViewModel.CarryForward = storeDay.CarryForward;
                storeBalanceViewModel.AtmRemark = storeDay.AtmRemark;
                storeBalanceViewModel.BankDepositRemark = storeDay.BankDepositRemark;
                storeBalanceViewModel.PaidInRemark = storeDay.PaidInRemark;
                storeBalanceViewModel.CashInHand = storeDay.CashInHand;
                storeBalanceViewModel.Variance = storeDay.Variance;
                storeBalanceViewModel.Stores = _context.Stores.ToList();
                storeBalanceViewModel.StoreTransferAmount = storeDay.StoreTransferAmount;
                storeBalanceViewModel.TransferredStore = _context.Stores.Find(storeDay.TransferredStoreId);

                foreach (var item in storePaymentMethods)
                {
                    storeBalanceViewModel.Card += item.Amount;

                }


                //calculate all sales amount

                var storeFuelSalesAmount = _context.StoreFuelSales
                .Where(sfs => sfs.StoreId == storeId && sfs.Day.Date == inputDate.Date)
                .Sum(sfs => sfs.SaleAmount);


                var storeProductSales = _context.StoreProductSales
                .Where(sps => sps.StoreId == storeId && sps.Day.Date == inputDate.Date)
                 .Sum(sfs => sfs.SaleAmount);

                var scracthCardSaleList = _context.StoreScratchCardSales.Include(s => s.ScratchCard)
                    .Where(s => s.StoreId == storeId && s.Day.Date == inputDate.Date).ToList();

                decimal storeScratchCardSalesAmount = 0;

                foreach (var item in scracthCardSaleList)
                {

                    storeScratchCardSalesAmount += item.SalesCount * item.ScratchCard.Price;
                }

                var storeServiceSalesList = _context.StoreServiceSales.Include(s => s.Service)
                    .Where(s => s.StoreId == storeId && s.Day.Date == inputDate.Date).ToList();


                decimal storeServiceSalesAmount = 0;

                foreach (var item in storeServiceSalesList)
                {
                    storeServiceSalesAmount += item.SalesCount * item.Service.Price;
                }

                decimal AllSalesAmount = storeFuelSalesAmount + storeProductSales + storeScratchCardSalesAmount + storeServiceSalesAmount;


                storeBalanceViewModel.Cash = AllSalesAmount - storeBalanceViewModel.Card;


                var storeMgrPayOuts = _context.StoreMgrPayOuts
               .Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date)
               .ToList();

                foreach (var item in storeMgrPayOuts)
                {
                    storeBalanceViewModel.PaidOut += item.Amount;
                }

                var specificPayOuts = _context.SpecificPayOuts
                .Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date)
                .ToList();

                foreach (var item in specificPayOuts)
                {
                    storeBalanceViewModel.PaidOut += item.Amount;
                }

                //var balance = carryFwd + card + cash - paidOut + paidIn - atm - bankDeposit;

                NextDayCarryFwd = storeDay.CarryForward + storeBalanceViewModel.Cash
                   - storeBalanceViewModel.PaidOut + storeBalanceViewModel.PaidIn
                   - storeBalanceViewModel.Atm - storeBalanceViewModel.BankDeposit
                   + storeBalanceViewModel.Variance - storeBalanceViewModel.StoreTransferAmount;

            }


            var dayToBeFinalied = _context.StoreDays.Include(s => s.Store).Where(s => s.StoreId == storeId && s.IsFinalised == false).FirstOrDefault();
            dayToBeFinalied.IsFinalised = true;
            _context.SaveChanges();

            var newInputDay = dayToBeFinalied.Day.AddDays(1);


            _context.StoreDays.Add(new StoreDay
            {
                StoreId = storeId,
                Day = newInputDay,
                IsFinalised = false,
                CarryForward = NextDayCarryFwd
            });
            _context.SaveChanges();

            _utilityService.UpdateStoreDaysToReflectChanges(storeId);




            SendEmails(inputDate, storeId);

            return RedirectToAction("Index", "Admin");


        }

        public IActionResult RejectFinalize(int storeId)
        {
            var store = _context.StoreDays.First(s => s.StoreId == storeId &&
               s.FinalizeApplied == true && s.FinalizeAccepted == false);

            store.FinalizeRejected = true;
            store.FinalizeApplied = false;

            _context.SaveChanges();


            return RedirectToAction("Index", "Admin");
        }




        [HttpGet]
        [TypeFilter(typeof(StoreManagerAuthorizationFilter))]
        public IActionResult PaymentMethods(int storeId)
        {

            if (InActiveAndNotAdmin(storeId))
            {
                return Content("Sorry, No Store");
            }

            var items = JsonConvert.DeserializeObject<Dictionary<int, decimal?>>(TempData["cardPayments"].ToString());


            List<PaymentCardAmount> paymentCardAmounts = new List<PaymentCardAmount>();


            foreach (var item in items)
            {
                paymentCardAmounts.Add(new PaymentCardAmount
                {
                    PaymentCardId = item.Key,
                    Amount = item.Value ?? 0m,
                    PaymentCardName = _context.PaymentCards.FirstOrDefault(pc => pc.Id == item.Key).Name
                });
            }


            var viewModel = new PaymentsViewModel
            {



                PaymentCards = _context.PaymentCards.ToList(),
                PaymentCardAmounts = paymentCardAmounts,
                StoreId = storeId,
            };

            return View(viewModel);
        }

        [HttpGet]
        [TypeFilter(typeof(StoreManagerAuthorizationFilter))]
        public IActionResult ScratchCards(int storeId)
        {
            var store = _context.Stores.Include(s => s.StoreScratchCards).ThenInclude(s => s.ScratchCard).FirstOrDefault(s => s.Id == storeId);
            var inputDate = _context.StoreDays.Include(s => s.Store).Where(s => s.StoreId == storeId && s.IsFinalised == false).FirstOrDefault().Day.Date;
            //inputdate එකට අදාළ මේ store එකේ තියන සේරම sale records
            var storeScratchCardSales = _context.StoreScratchCardSales.Where(s => s.StoreId == storeId && s.Day.Date == inputDate).ToList();

            ScratchCardSalesViewModel scratchCardSalesViewModel = new();
            scratchCardSalesViewModel.StoreId = storeId;

            foreach (var scratchCard in store.StoreScratchCards)
            {
                //දැනට බලන scratchCard එකට අදාළ මේ දවසේ possible record එක
                var scratchCardSale = storeScratchCardSales.FirstOrDefault(s => s.ScratchCardId == scratchCard.ScratchCardId);

                if (scratchCardSale == null) //මෙහෙම වෙනවා කියන්නේ මේ දවසේදී මේ store එකේ මේ card එකට අදාලව sale record එක දාල නැහැ/
                {
                    //අන්තිමට තියන , මේ store එකේ මේ card එකට අදාළ record එක ගන්නවා
                    var lastScratchCardSale = _context.StoreScratchCardSales
                        .Where(s => s.ScratchCardId == scratchCard.ScratchCardId && s.StoreId == storeId).OrderBy(s => s.Id)
                        .LastOrDefault();

                    if (lastScratchCardSale == null) //මෙහෙම වෙනවා කියන්නේ දැන් අපි දාන්න යන්නේ මේ store එකේ මේ card එකට අදාලව දාන්න යන පලවෙනි sale reocrd එක
                    {
                        int initialOpen = _context.StoreScratchCards.First(s => s.StoreId == storeId && s.ScratchCardId == scratchCard.ScratchCardId).InitialOpen;


                        scratchCardSalesViewModel.ScratchCardsSales.Add(new ScratchCardsSale()
                        {
                            ScratchCardId = scratchCard.ScratchCardId,
                            Activate = 0,
                            Closing = 0,
                            Price = scratchCard.ScratchCard.Price,
                            Number = scratchCard.ScratchCard.Name,
                            Opening = initialOpen

                        });
                    }
                    else
                    {


                        scratchCardSalesViewModel.ScratchCardsSales.Add(new ScratchCardsSale()
                        {
                            ScratchCardId = scratchCard.ScratchCardId,
                            Activate = 0,
                            Closing = 0,
                            Price = scratchCard.ScratchCard.Price,
                            Number = scratchCard.ScratchCard.Name,
                            Opening = lastScratchCardSale.CloseCount

                        });

                    }

                }
                else // මෙතනට එනවා කියන්නේ මේ දවසේ මේ store එකේ මේ card එකට අදාලව sale record එක කලින් දාල තියනවා
                {
                    StoreScratchCardSales lastScratchCardSale = null;

                    //අන්තිමට තියන , මේ store එකේ මේ card එකට අදාළ record එක ගන්නවා
                    if (_context.StoreScratchCardSales
                      .Where(s => s.StoreId == storeId && s.ScratchCardId == scratchCard.ScratchCardId).Count() > 1)
                    {
                        var count = _context.StoreScratchCardSales
                       .Where(s => s.StoreId == storeId && s.ScratchCardId == scratchCard.ScratchCardId).Count();

                        lastScratchCardSale = _context.StoreScratchCardSales
                         .Where(s => s.StoreId == storeId && s.ScratchCardId == scratchCard.ScratchCardId).ToList()[count - 2];
                    }


                    int initialOpen = _context.StoreScratchCards.First(s => s.StoreId == storeId && s.ScratchCardId == scratchCard.ScratchCardId).InitialOpen;


                    scratchCardSalesViewModel.ScratchCardsSales.Add(new ScratchCardsSale()
                    {
                        ScratchCardId = scratchCard.ScratchCardId,
                        Activate = scratchCardSale.ActivationCount,
                        Closing = scratchCardSale.CloseCount,
                        Price = scratchCard.ScratchCard.Price,
                        Number = scratchCard.ScratchCard.Name,
                        Opening = lastScratchCardSale == null ? initialOpen : lastScratchCardSale.CloseCount

                    });

                }


            }

            return View(scratchCardSalesViewModel);
        }

        // POST: ScratchCardSales/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult ScratchCards(ScratchCardSalesViewModel viewModel)
        {

            if (viewModel.ScratchCardsSales.Any(s => s.Activate < 0 || s.Closing < 0 || s.Opening + s.Activate < s.Closing))
            {
                return RedirectToAction(nameof(ScratchCards), "StoreDashboard", new { storeId = viewModel.StoreId });

            }

            var inputDate = _context.StoreDays.Include(s => s.Store).Where(s => s.StoreId == viewModel.StoreId && s.IsFinalised == false).FirstOrDefault().Day.Date;


            _context.StoreScratchCardSales.Where(s => s.StoreId == viewModel.StoreId && s.Day.Date == inputDate).ToList().ForEach(p => _context.StoreScratchCardSales.Remove(p));
            _context.SaveChanges();


            foreach (var scratchCardSale in viewModel.ScratchCardsSales)
            {
                _context.StoreScratchCardSales.Add(new StoreScratchCardSales()
                {
                    StoreId = viewModel.StoreId,
                    ScratchCardId = scratchCardSale.ScratchCardId,
                    SalesCount = scratchCardSale.Opening + scratchCardSale.Activate - scratchCardSale.Closing,
                    Day = inputDate,
                    ActivationCount = scratchCardSale.Activate,
                    CloseCount = scratchCardSale.Closing
                });

                _context.StoreScratchCards.Where(s => s.ScratchCardId == scratchCardSale.ScratchCardId && s.StoreId == viewModel.StoreId).FirstOrDefault().Activation = scratchCardSale.Activate;
            }
            _context.SaveChanges();


            return RedirectToAction("Sections", new { storeId = viewModel.StoreId });

        }


        [HttpPost]
        public IActionResult PaymentMethods(PaymentsViewModel viewModel)
        {

            var inputDate = _context.StoreDays.Include(s => s.Store).Where(s => s.StoreId == viewModel.StoreId && s.IsFinalised == false).FirstOrDefault().Day.Date;


            if (viewModel.PaymentCardAmounts.Any(p => p.Amount < 0))
            {

                var ExistingPaymentMethodsForTheDay = _context.StorePaymentMethods.Include(p => p.PaymentCard).Where(s => s.StoreId == viewModel.StoreId && s.Date.Date == inputDate.Date).ToList();
                var PaymentCards = _context.PaymentCards.ToList();


                Dictionary<int, decimal> cardPayments = new Dictionary<int, decimal>();



                foreach (var card in PaymentCards)
                {
                    var amount = ExistingPaymentMethodsForTheDay.FirstOrDefault(e => e.PaymentCardId == card.Id)?.Amount;
                    if (amount != null)
                    {
                        cardPayments.Add(card.Id, amount.Value);
                    }
                    else
                    {
                        cardPayments.Add(card.Id, 0);
                    }
                }


                TempData["cardPayments"] = JsonConvert.SerializeObject(cardPayments);



                return RedirectToAction(nameof(PaymentMethods), "StoreDashboard", new { storeId = viewModel.StoreId, cardPayments = cardPayments });
            }



            _context.StorePaymentMethods.RemoveRange(_context.StorePaymentMethods.Where(p => p.StoreId == viewModel.StoreId && p.Date.Date == inputDate));
            _context.SaveChanges();


            foreach (var item in viewModel.PaymentCardAmounts)
            {
                if (item.Amount > 0)
                {
                    _context.StorePaymentMethods.Add(new StorePaymentMethod
                    {
                        StoreId = viewModel.StoreId,
                        PaymentCardId = item.PaymentCardId,
                        Amount = item.Amount,
                        Date = inputDate,
                    });
                }

            }




            _context.SaveChanges();


            return RedirectToAction("Sections", new { storeId = viewModel.StoreId });


        }



        [HttpGet]
        [TypeFilter(typeof(StoreManagerAuthorizationFilter))]
        public IActionResult SpecificPayOut(int storeId)
        {

            if (InActiveAndNotAdmin(storeId))
            {
                return Content("Sorry, No Store");
            }


            var inputDate = _context.StoreDays.Include(s => s.Store)
                .Where(s => s.StoreId == storeId && s.IsFinalised == false).FirstOrDefault().Day.Date;


            var ExistingSpecificPayoutsForTheDay = _context.SpecificPayOuts
                .Include(p => p.PayOutParty)
                .Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date).ToList();

            List<PayOutRow> payOutRows = new List<PayOutRow>();


            foreach (var item in ExistingSpecificPayoutsForTheDay)
            {
                payOutRows.Add(new PayOutRow
                {
                    Amount = item.Amount,
                    PayOutPartyId = item.PayOutPartyId
                });
            }



            var model = new SpecificPayOutViewModal
            {
                PayOutRows = payOutRows,
                StoreId = storeId
            };

            ViewBag.PayOutParties = _context.PayOutParties.ToList(); // Get list of PayOutParties from database or other source
            return View(model);
        }

        [HttpPost]
        public IActionResult SpecificPayOut(SpecificPayOutViewModal model)
        {

            if (model.PayOutRows.Any(p => p.Amount < 0))
            {
                return RedirectToAction(nameof(SpecificPayOut), "StoreDashboard", new { storeId = model.StoreId });

            }


            var inputDate = _context.StoreDays.Include(s => s.Store).Where(s => s.StoreId == model.StoreId && s.IsFinalised == false).FirstOrDefault().Day.Date;


            _context.SpecificPayOuts.RemoveRange(_context.SpecificPayOuts.Where(p => p.StoreId == model.StoreId && p.Date.Date == inputDate.Date));
            _context.SaveChanges();


            foreach (var payOutRow in model.PayOutRows)
            {
                if (payOutRow.Amount > 0 && payOutRow.PayOutPartyId > 0)
                {
                    _context.SpecificPayOuts.Add(new Persistance.SpecificPayOut() { Amount = payOutRow.Amount, Date = inputDate, PayOutPartyId = payOutRow.PayOutPartyId, StoreId = model.StoreId });
                }
            }

            _context.SaveChanges();
            return RedirectToAction("Sections", new { storeId = model.StoreId });


            // if (ModelState.IsValid)
            // {
            // Handle the submitted data
            // Save to the database or perform other actions
            // }

            // ViewBag.PayOutParties = GetPayOutParties(); // Get list of PayOutParties from database or other source
            // return View(model);
        }


        [HttpPost]
        public IActionResult Atm(StoreAtmViewModel storeAtmViewModel)
        {

            var StoreDay = _context.StoreDays.Include(s => s.Store)
           .Where(s => s.StoreId == storeAtmViewModel.StoreId && s.IsFinalised == false).FirstOrDefault();

            StoreDay.AtmWithdrawl = storeAtmViewModel.withdraw;

            _context.SaveChanges();

            return RedirectToAction("Sections", new { storeId = storeAtmViewModel.StoreId });

        }


        [HttpGet]
        public IActionResult Atm(int storeId)
        {
            if (InActiveAndNotAdmin(storeId))
            {
                return Content("Sorry, No Store");
            }

            var StoreDay = _context.StoreDays.Include(s => s.Store)
            .Where(s => s.StoreId == storeId && s.IsFinalised == false).FirstOrDefault();

            StoreAtmViewModel storeAtmViewModel = new();

            var store = _context.Stores.Find(storeId);

            storeAtmViewModel.open = store.AtmInitialOpenValue + _context.StoreDays.Where(s => s.StoreId == storeId && s.Day.Date != StoreDay.Day.Date).Sum(s => s.Atm)
                - _context.StoreDays.Where(s => s.StoreId == storeId && s.Day.Date != StoreDay.Day.Date).Sum(s => s.AtmWithdrawl);

            storeAtmViewModel.deposit = StoreDay.Atm;
            storeAtmViewModel.withdraw = StoreDay.AtmWithdrawl;

            storeAtmViewModel.close = storeAtmViewModel.open
                + storeAtmViewModel.deposit
                - storeAtmViewModel.withdraw;

            storeAtmViewModel.StoreId = storeId;




            return View(storeAtmViewModel);
        }


        public class StoreAtmViewModel
        {
            public int StoreId { get; set; }

            public decimal open { get; set; }
            public decimal deposit { get; set; }
            public decimal withdraw { get; set; }
            public decimal close { get; set; }

        }

        [HttpGet]
        public IActionResult Balance(int storeId)
        {
            if (InActiveAndNotAdmin(storeId))
            {
                return Content("Sorry, No Store");
            }


            var inputDate = _context.StoreDays
              .Include(s => s.Store)
              .Where(s => s.StoreId == storeId && s.IsFinalised == false).FirstOrDefault().Day.Date;


            var storeDay = _context.StoreDays
          .Where(sd => sd.StoreId == storeId && sd.Day == inputDate)
          .FirstOrDefault();

            var storePaymentMethods = _context.StorePaymentMethods
            .Where(spm => spm.StoreId == storeId && spm.Date.Date == inputDate.Date)
            .ToList();

            StoreBalanceViewModel storeBalanceViewModel = new();
            storeBalanceViewModel.StoreId = storeId;
            storeBalanceViewModel.PaidIn = storeDay.PaidIn;
            storeBalanceViewModel.Atm = storeDay.Atm;
            storeBalanceViewModel.BankDeposit = storeDay.BankDeposit;
            storeBalanceViewModel.CarryForward = storeDay.CarryForward;
            storeBalanceViewModel.AtmRemark = storeDay.AtmRemark;
            storeBalanceViewModel.BankDepositRemark = storeDay.BankDepositRemark;
            storeBalanceViewModel.PaidInRemark = storeDay.PaidInRemark;
            storeBalanceViewModel.CashInHand = storeDay.CashInHand;
            storeBalanceViewModel.Variance = storeDay.Variance;
            storeBalanceViewModel.Stores = _context.Stores.ToList();
            storeBalanceViewModel.StoreTransferAmount = storeDay.StoreTransferAmount;
            storeBalanceViewModel.TransferredStore = _context.Stores.Find(storeDay.TransferredStoreId);

            foreach (var item in storePaymentMethods)
            {
                storeBalanceViewModel.Card += item.Amount;

            }


            //calculate all sales amount

            var storeFuelSalesAmount = _context.StoreFuelSales
            .Where(sfs => sfs.StoreId == storeId && sfs.Day.Date == inputDate.Date)
            .Sum(sfs => sfs.SaleAmount);


            var storeProductSales = _context.StoreProductSales
            .Where(sps => sps.StoreId == storeId && sps.Day.Date == inputDate.Date)
             .Sum(sfs => sfs.SaleAmount);

            var scracthCardSaleList = _context.StoreScratchCardSales.Include(s => s.ScratchCard)
                .Where(s => s.StoreId == storeId && s.Day.Date == inputDate.Date).ToList();

            decimal storeScratchCardSalesAmount = 0;

            foreach (var item in scracthCardSaleList)
            {

                storeScratchCardSalesAmount += item.SalesCount * item.ScratchCard.Price;
            }

            var storeServiceSalesList = _context.StoreServiceSales.Include(s => s.Service)
                .Where(s => s.StoreId == storeId && s.Day.Date == inputDate.Date).ToList();


            decimal storeServiceSalesAmount = 0;

            foreach (var item in storeServiceSalesList)
            {
                storeServiceSalesAmount += item.SalesCount * (item.Service.Price-item.Service.Discount);
            }

            decimal AllSalesAmount = storeFuelSalesAmount + storeProductSales + storeScratchCardSalesAmount + storeServiceSalesAmount;


            storeBalanceViewModel.Cash = AllSalesAmount - storeBalanceViewModel.Card;


            var storeMgrPayOuts = _context.StoreMgrPayOuts
           .Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date)
           .ToList();

            foreach (var item in storeMgrPayOuts)
            {
                storeBalanceViewModel.PaidOut += item.Amount;
            }

            var specificPayOuts = _context.SpecificPayOuts
            .Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date)
            .ToList();

            foreach (var item in specificPayOuts)
            {
                storeBalanceViewModel.PaidOut += item.Amount;
            }


            // Fetch the list of stores (replace with your actual method to get stores)
            var stores = _context.Stores.ToList();

            // Pass stores to the view using ViewBag
            ViewBag.StoreList = new SelectList(stores, "Id", "Name");

            return View(storeBalanceViewModel);
        }


        [HttpPost]
        public IActionResult Balance(StoreBalanceViewModel storeBalanceViewModel)
        {
            if (storeBalanceViewModel.PaidIn < 0 || storeBalanceViewModel.BankDeposit < 0 || storeBalanceViewModel.Atm < 0)
            {
                return RedirectToAction(nameof(Balance), "StoreDashboard", new { storeId = storeBalanceViewModel.StoreId });

            }


            var inputDate = _context.StoreDays
                .Include(s => s.Store)
                .Where(s => s.StoreId == storeBalanceViewModel.StoreId && s.IsFinalised == false)
                .FirstOrDefault()
                .Day;

            var storeDay = _context.StoreDays
            .Where(sd => sd.StoreId == storeBalanceViewModel.StoreId && sd.Day == inputDate)
            .FirstOrDefault();




            storeDay.PaidIn = storeBalanceViewModel.PaidIn;
            storeDay.Atm = storeBalanceViewModel.Atm;
            storeDay.BankDeposit = storeBalanceViewModel.BankDeposit;
            storeDay.AtmRemark = storeBalanceViewModel.AtmRemark;
            storeDay.BankDepositRemark = storeBalanceViewModel.BankDepositRemark;
            storeDay.PaidInRemark = storeBalanceViewModel.PaidInRemark;
            storeDay.CashInHand = storeBalanceViewModel.CashInHand;
            storeDay.Variance = storeBalanceViewModel.Variance;
            storeDay.TransferredStoreId = storeBalanceViewModel.TransferredStore.Id;
            storeDay.StoreTransferAmount = storeBalanceViewModel.StoreTransferAmount;
            _context.SaveChanges();

            Store? StoreTransferredTo = _context.Stores.Find(storeBalanceViewModel.TransferredStore.Id);


            //remove current StorePaidIn record with this storedayId for this day (because we know there can only be one)
            var storePaidIn = _context.StorePaidIns.FirstOrDefault(s => s.TransferredFromStoreDayId == storeDay.Id);
            if (storePaidIn != null)
            {
                _context.StorePaidIns.Remove(storePaidIn);
                _context.SaveChanges();
                _utilityService.UpdateStoreDaysToReflectChanges(storePaidIn.TransferToStoreId);
            }

            if (StoreTransferredTo != null)
            {
                DateTime TransferToStoreCurrentDay = _context.StoreDays.First(s => s.StoreId == StoreTransferredTo.Id && s.IsFinalised == false).Day.Date;

                StorePaidIn storePaidIn1 = new StorePaidIn();
                storePaidIn1.TransfeffedToDate = (storeDay.Day.Date > TransferToStoreCurrentDay) ? storeDay.Day.Date : TransferToStoreCurrentDay;
                storePaidIn1.TransferredFromDate = storeDay.Day.Date;
                storePaidIn1.TransferredFromStoreDayId = storeDay.Id;
                storePaidIn1.Amount = storeBalanceViewModel.StoreTransferAmount;
                storePaidIn1.TransferToStoreId = StoreTransferredTo.Id;
                storePaidIn1.StorePaidInState = StorePaidInState.Pending;

                _context.StorePaidIns.Add(storePaidIn1);
                _context.SaveChanges();
                _utilityService.UpdateStoreDaysToReflectChanges(StoreTransferredTo.Id);

            }



            _utilityService.UpdateStoreDaysToReflectChanges(storeDay.StoreId);




            return RedirectToAction("Sections", new { storeId = storeBalanceViewModel.StoreId });


        }





        [HttpGet]
        [TypeFilter(typeof(StoreManagerAuthorizationFilter))]
        public IActionResult PayOuts(int storeId)
        {

            var items = JsonConvert.DeserializeObject<List<ItemViewModel>>(TempData["Items"].ToString());

            ViewBag.StoreId = storeId;
            return View(items);
        }

        [HttpPost]
        public IActionResult PayOuts(int storeId, List<ItemViewModel> items)
        {

            if (InActiveAndNotAdmin(storeId))
            {
                return Content("Sorry, No Store");
            }

            var inputDate = _context.StoreDays
                .Include(s => s.Store)
                .Where(s => s.StoreId == storeId && s.IsFinalised == false)
                .FirstOrDefault().Day;


            if (items.Any(i => i.Amount < 0))
            {
                var ExistingPayOutsForTheDay = _context.StoreMgrPayOuts.Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date).ToList();

                List<ItemViewModel> items2 = new List<ItemViewModel>();

                foreach (var item in ExistingPayOutsForTheDay)
                {
                    items2.Add(new ItemViewModel() { Name = item.Recipient, Amount = item.Amount });
                }

                TempData["Items"] = JsonConvert.SerializeObject(items2);

                return RedirectToAction(nameof(PayOuts), "StoreDashboard", new { storeId = storeId });
            }



            _context.StoreMgrPayOuts
            .Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date).ToList()
            .ForEach(p => _context.StoreMgrPayOuts.Remove(p));


            _context.SaveChanges();

            foreach (var item in items)
            {
                if (item.Name != null)
                {
                    _context.StoreMgrPayOuts.Add(new StoreMgrPayOut()
                    {
                        StoreId = storeId,
                        Amount = item.Amount,
                        Recipient = item.Name,
                        Date = inputDate,

                    });
                }
            }
            _context.SaveChanges();

            return RedirectToAction("Sections", new { storeId = storeId });

        }


        public class ItemViewModel
        {
            public string Name { get; set; }
            public decimal Amount { get; set; }
        }



        public bool InActiveAndNotAdmin(int storeId)
        {
            if (_context.Stores.Find(storeId).Active == false &&
                HttpContext.Session.GetString("UserType") != "Admin")
            {
                return true;
            }
            return false;
        }


        public IActionResult Sections(int storeId)
        {

            if (InActiveAndNotAdmin(storeId))
            {
                return Content("Sorry, No Store");
            }

            try
            {



                var inputDate = _context.StoreDays.Include(s => s.Store).Where(s => s.StoreId == storeId && s.IsFinalised == false).FirstOrDefault().Day;


                var storeDay = _context.StoreDays
.Where(sd => sd.StoreId == storeId && sd.Day == inputDate)
.FirstOrDefault();

                //check whether there are pending store fund trasformations
                var storePaidIns = _context.StorePaidIns.Where(s => s.StorePaidInState == StorePaidInState.Pending
                   && s.TransferToStoreId == storeId && s.TransfeffedToDate.Date == storeDay.Day.Date).ToList();

                Dictionary<int, Store> keyValuePairs = new Dictionary<int, Store>();

                foreach (var s in storePaidIns)
                {
                    var storeday = _context.StoreDays.Include(ss => ss.Store).First(ss => ss.Id == s.TransferredFromStoreDayId);

                    keyValuePairs.Add(s.TransferredFromStoreDayId, storeDay.Store);
                }

                bool isThereAPendingPaidInThisScoreTransfered = false;

                if (_context.StorePaidIns.Where(s => s.StorePaidInState == StorePaidInState.Pending
                   && s.TransferredFromStoreDayId == storeDay.Id && s.TransfeffedToDate.Date == storeDay.Day.Date).Count() == 1)
                {
                    isThereAPendingPaidInThisScoreTransfered = true;

                }





                SectionsViewModel sectionsViewModel = new SectionsViewModel()
                {
                    StoreId = storeId,
                    IsFuel = _context.Stores.First(s => s.Id == storeId)?.StoreType == StoreType.store_with_fuel_station,
                    InputDate = inputDate,
                    CashInHand = storeDay.CashInHand,
                    storePaidIns = storePaidIns,
                    keyValuePairs = keyValuePairs,
                    IsThereAPendingPaidInThisScoreTransfered = isThereAPendingPaidInThisScoreTransfered
                };






                if (storeDay.FinalizeApplied == true && storeDay.FinalizeRejected == false && storeDay.FinalizeAccepted == false)
                {
                    ViewData["PendingMessage"] = "Admin Approval Pending for Finalizing...";



                    if (HttpContext.Session.GetString("UserType") == "Admin")
                    {
                        ViewData["Buttons"] = true;
                    }

                }
                else if (storeDay.FinalizeRejected == true)
                {
                    ViewData["Rejected"] = "Admin has Rejected Finalizing, Please Try again...";
                }




                return View(sectionsViewModel);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "Please Add Products, Services , Fuel and Scratch Tickets for Newly added Store and then Update Settings";
                return View("Index");
            }
        }


        //Finalise
        [TypeFilter(typeof(StoreManagerAuthorizationFilter))]
        public IActionResult Finalise(int storeId)
        {


            if (InActiveAndNotAdmin(storeId))
            {
                return Content("Sorry, No Store");
            }


          



            var inputDate = _context.StoreDays.Include(s => s.Store)
                  .Where(s => s.StoreId == storeId && s.IsFinalised == false).FirstOrDefault().Day.Date;

            var storeDay = _context.StoreDays
           .Where(sd => sd.StoreId == storeId && sd.Day == inputDate)
           .FirstOrDefault();

            //incase store transaction happned while this store page is open ( no reload)
            //check whether there are pending store fund trasformations
            var storePaidIns = _context.StorePaidIns.Where(s => s.StorePaidInState == StorePaidInState.Pending
               && s.TransferToStoreId == storeId && s.TransfeffedToDate.Date == storeDay.Day.Date).ToList();

            if(storePaidIns.Count() > 0)
            {
                return RedirectToAction(nameof(Sections), new { storeId = storeId });
            }


            decimal NextDayCarryFwd = 0;

            {





             

                var storePaymentMethods = _context.StorePaymentMethods
                .Where(spm => spm.StoreId == storeId && spm.Date.Date == inputDate.Date)
                .ToList();

                StoreBalanceViewModel storeBalanceViewModel = new();
                storeBalanceViewModel.StoreId = storeId;
                storeBalanceViewModel.PaidIn = storeDay.PaidIn;
                storeBalanceViewModel.Atm = storeDay.Atm;
                storeBalanceViewModel.BankDeposit = storeDay.BankDeposit;
                storeBalanceViewModel.CarryForward = storeDay.CarryForward;
                storeBalanceViewModel.AtmRemark = storeDay.AtmRemark;
                storeBalanceViewModel.BankDepositRemark = storeDay.BankDepositRemark;
                storeBalanceViewModel.PaidInRemark = storeDay.PaidInRemark;
                storeBalanceViewModel.CashInHand = storeDay.CashInHand;
                storeBalanceViewModel.Variance = storeDay.Variance;
                storeBalanceViewModel.Stores = _context.Stores.ToList();
                storeBalanceViewModel.StoreTransferAmount = storeDay.StoreTransferAmount;
                storeBalanceViewModel.TransferredStore = _context.Stores.Find(storeDay.TransferredStoreId);

                foreach (var item in storePaymentMethods)
                {
                    storeBalanceViewModel.Card += item.Amount;

                }


                //calculate all sales amount

                var storeFuelSalesAmount = _context.StoreFuelSales
                .Where(sfs => sfs.StoreId == storeId && sfs.Day.Date == inputDate.Date)
                .Sum(sfs => sfs.SaleAmount);


                var storeProductSales = _context.StoreProductSales
                .Where(sps => sps.StoreId == storeId && sps.Day.Date == inputDate.Date)
                 .Sum(sfs => sfs.SaleAmount);

                var scracthCardSaleList = _context.StoreScratchCardSales.Include(s => s.ScratchCard)
                    .Where(s => s.StoreId == storeId && s.Day.Date == inputDate.Date).ToList();

                decimal storeScratchCardSalesAmount = 0;

                foreach (var item in scracthCardSaleList)
                {

                    storeScratchCardSalesAmount += item.SalesCount * item.ScratchCard.Price;
                }

                var storeServiceSalesList = _context.StoreServiceSales.Include(s => s.Service)
                    .Where(s => s.StoreId == storeId && s.Day.Date == inputDate.Date).ToList();


                decimal storeServiceSalesAmount = 0;

                foreach (var item in storeServiceSalesList)
                {
                    storeServiceSalesAmount += item.SalesCount * (item.Service.Price-item.Service.Discount);
                }

                decimal AllSalesAmount = storeFuelSalesAmount + storeProductSales + storeScratchCardSalesAmount + storeServiceSalesAmount;


                storeBalanceViewModel.Cash = AllSalesAmount - storeBalanceViewModel.Card;


                var storeMgrPayOuts = _context.StoreMgrPayOuts
               .Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date)
               .ToList();

                foreach (var item in storeMgrPayOuts)
                {
                    storeBalanceViewModel.PaidOut += item.Amount;
                }

                var specificPayOuts = _context.SpecificPayOuts
                .Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date)
                .ToList();

                foreach (var item in specificPayOuts)
                {
                    storeBalanceViewModel.PaidOut += item.Amount;
                }

                //var balance = carryFwd + card + cash - paidOut + paidIn - atm - bankDeposit;

                NextDayCarryFwd = storeDay.CarryForward + storeBalanceViewModel.Cash
                   - storeBalanceViewModel.PaidOut
                   + storeBalanceViewModel.PaidIn - storeBalanceViewModel.Atm - storeBalanceViewModel.BankDeposit
                   + storeBalanceViewModel.Variance - storeBalanceViewModel.StoreTransferAmount;

                if (storeDay.CashInHand != NextDayCarryFwd)
                {

                    TempData["ErrorMessage"] = "Cash in hand not equal to Balance.";
                    return RedirectToAction("Sections", new { storeId = storeId });

                }


                //check for unresolved store transction
                if (_context.StorePaidIns
                    .Where(s => s.StorePaidInState == StorePaidInState.Pending && s.TransferredFromDate == storeDay.Day.Date)
                    .Select(s => s.TransferredFromStoreDayId)
                    .Any(s => s == storeDay.Id))
                {

                    TempData["ErrorMessage"] = "Pending Store Transaction.";
                    return RedirectToAction("Sections", new { storeId = storeId });

                }



                storeDay.FinalizeApplied = true;
                storeDay.FinalizeAccepted = false; //to be sure
                storeDay.FinalizeRejected = false;

                _context.SaveChanges();

                string message = $"New Finalize, Store{storeDay.Store.Name} , Day {storeDay.Day}";





            }


            //var dayToBeFinalied = _context.StoreDays.Include(s => s.Store).Where(s => s.StoreId == storeId && s.IsFinalised == false).FirstOrDefault();
            //dayToBeFinalied.IsFinalised = true;
            //_context.SaveChanges();

            //var newInputDay = dayToBeFinalied.Day.AddDays(1);


            //_context.StoreDays.Add(new StoreDay
            //{
            //    StoreId = storeId,
            //    Day = newInputDay,
            //    IsFinalised = false,
            //    CarryForward = NextDayCarryFwd
            //});
            //_context.SaveChanges();


            //SendEmails(inputDate, storeId);



            return RedirectToAction("Sections", new { storeId = storeId });

        }

        [HttpPost]
        public IActionResult CreateProductsSales(DaysProductSaleViewModal model)
        {

            var inputDate = _context.StoreDays
                .Include(s => s.Store)
                .Where(s => s.StoreId == model.StoreId && s.IsFinalised == false)
                .FirstOrDefault().Day;


            if (model.DaysProducts.Any(dp => dp.Sales < 0))
            {
                //මේ store එකට අදාළ products
                var productOfStore = _context.Products
                    .Include(p => p.StoreProducts)
                    .Include(p => p.ProductType)
                    .Where(p => p.StoreProducts.Any(s => s.StoreId == model.StoreId))
                    .ToList();

                var productTypes = _context.ProductTypes.ToList();


                // දැනට දාල තියන එම දවසේ එම store එකේ සියලුම product sales
                var productSales = _context.StoreProductSales
                    .Where(p => p.StoreId == model.StoreId && p.Day.Date == inputDate.Date)
                    .ToList();


                List<DaysProduct> daysProducts = new List<DaysProduct>();


                foreach (var product in productOfStore)
                {
                    //මේ product එකට අදාළ sale එකක් දාල නම් මේක null නැහැ
                    var releted = productSales.FirstOrDefault(p => p.ProductId == product.Id);

                    DaysProduct daysProduct = new DaysProduct()
                    {
                        Product = product,
                        Sales = (releted != null) ? releted.SaleAmount : 0,
                        Id = product.Id
                    };
                    daysProducts.Add(daysProduct);
                }

                return View("Products", new DaysProductSaleViewModal() { StoreId = model.StoreId, DaysProducts = daysProducts, ProductTypes = productTypes });
            }




            //එම store එකේ එම දවසේ sales විදියට දැනට දාල තියන
            //සේරම productsales
            var existingProductSales = _context.StoreProductSales
                    .Where(p => p.StoreId == model.StoreId && p.Day.Date == inputDate.Date)
                    .ToList();

            _context.StoreProductSales.RemoveRange(existingProductSales);
            _context.SaveChanges();

            // if (ModelState.IsValid)
            // {
            foreach (var daysProduct in model.DaysProducts)
            {


                Persistance.StoreProductSales storeProductSales = new Persistance.StoreProductSales()
                {
                    ProductId = daysProduct.Product.Id,
                    StoreId = model.StoreId,
                    SaleAmount = daysProduct.Sales,
                    Day = inputDate
                };

                _context.StoreProductSales.Add(storeProductSales);

            }
            _context.SaveChanges();
            return RedirectToAction("Sections", new { storeId = model.StoreId });

        }


        [HttpPost]
        public IActionResult CreateFuelsSales(DaysFuelSaleViewModal model)
        {

            if (model.DaysFuels == null)
                return RedirectToAction("Sections", new { storeId = model.StoreId });



            var inputDate = _context.StoreDays.Include(s => s.Store).Where(s => s.StoreId == model.StoreId && s.IsFinalised == false).FirstOrDefault().Day;


            if (model.DaysFuels.Any(df => df.Sales < 0))
            {
                var fuelsOfStore = _context.Fuels.Include(p => p.StoreFuels).Include(p => p.FuelType)
                    .Where(p => p.StoreFuels.Any(s => s.StoreId == model.StoreId))
                    .ToList();

                var fuelTypes = _context.FuelTypes.ToList();

                List<DaysFuel> daysFuels = new List<DaysFuel>();

                var fuelSales = _context.StoreFuelSales
                 .Where(p => p.StoreId == model.StoreId && p.Day.Date == inputDate.Date)
                 .ToList();


                foreach (var fuel in fuelsOfStore)
                {
                    var releted = fuelSales.FirstOrDefault(p => p.FuelId == fuel.Id);


                    DaysFuel daysFuel = new DaysFuel()
                    {
                        Fuel = fuel,
                        Sales = (releted != null) ? releted.SaleAmount : 0,
                        Id = fuel.Id
                    };
                    daysFuels.Add(daysFuel);
                }

                return View("Fuels", new DaysFuelSaleViewModal() { StoreId = model.StoreId, DaysFuels = daysFuels, FuelTypes = fuelTypes });
            }




            var existingFuelSales = _context.StoreFuelSales
                    .Where(p => p.StoreId == model.StoreId && p.Day.Date == inputDate.Date)
                    .ToList();

            _context.StoreFuelSales.RemoveRange(existingFuelSales);
            _context.SaveChanges();


            // if (ModelState.IsValid)
            // {
            foreach (var daysFuel in model.DaysFuels)
            {


                Persistance.StoreFuelSales storeFuelSales = new Persistance.StoreFuelSales()
                {
                    FuelId = daysFuel.Fuel.Id,
                    StoreId = model.StoreId,
                    SaleAmount = daysFuel.Sales,
                    Day = inputDate,
                    Liters = daysFuel.Liters
                };

                _context.StoreFuelSales.Add(storeFuelSales);

            }
            _context.SaveChanges();
            return RedirectToAction("Sections", new { storeId = model.StoreId });

        }


        [HttpPost]
        public IActionResult CreateServicesSales(DaysServiceSaleViewModal model)
        {

            var inputDate = _context.StoreDays.Include(s => s.Store).Where(s => s.StoreId == model.StoreId && s.IsFinalised == false).FirstOrDefault().Day;


            if (model.DaysServices.Any(ds => ds.Closing < 0 || ds.Opening > ds.Closing))
            {
                var servicesOfStore = _context.Services.Include(p => p.StoreServices).Include(p => p.ServiceType)
                  .Where(p => p.StoreServices.Any(s => s.StoreId == model.StoreId))
                  .ToList();

                var serviceTypes = _context.ServiceTypes.ToList();

                List<DaysService> daysServices = new List<DaysService>();


                // var serviceSales = _context.StoreServiceSales.Where(p => p.StoreId == storeId && p.Day == inputDate).ToList();



                foreach (var service in servicesOfStore)
                {
                    var allSales = _context.StoreServiceSales.Where(sss => sss.ServiceId == service.Id && sss.StoreId == model.StoreId).Sum(sss => sss.SalesCount);
                    var lasttimeSales = _context.StoreServiceSales.Where(sss => sss.ServiceId == service.Id && sss.StoreId == model.StoreId && sss.Day.Date == inputDate.Date).FirstOrDefault()?.SalesCount;
                    int close = 0;
                    int open = 0;


                    if (lasttimeSales == null)
                    {
                        //meaning; for current days there have not been any input for this stores this service
                        //then close has to be 0 
                        close = 0;
                        open = allSales;

                    }
                    else
                    {
                        //meaning; for current days there have been input for this stores this service
                        //then close has to be lasttimeSales
                        close = allSales;
                        open = close - lasttimeSales.Value;
                    }


                    DaysService daysService = new DaysService()
                    {
                        Service = service,
                        Id = service.Id,
                        Opening = open,
                        Closing = close
                    };
                    daysServices.Add(daysService);
                }




                return View("Services", new DaysServiceSaleViewModal() { StoreId = model.StoreId, DaysServices = daysServices, ServiceTypes = serviceTypes });
            }


            int x = 0;

            var existingServiceSales = _context.StoreServiceSales
               .Where(p => p.StoreId == model.StoreId && p.Day.Date == inputDate.Date)
               .ToList();

            _context.StoreServiceSales.RemoveRange(existingServiceSales);
            _context.SaveChanges();

            // if (ModelState.IsValid)
            // {
            foreach (var daysService in model.DaysServices)
            {


                Persistance.StoreServiceSales storeServiceSales = new Persistance.StoreServiceSales()
                {
                    ServiceId = daysService.Service.Id,
                    StoreId = model.StoreId,
                    SalesCount = daysService.Closing - daysService.Opening,
                    Day = inputDate
                };

                _context.StoreServiceSales.Add(storeServiceSales);

            }
            _context.SaveChanges();
            return RedirectToAction("Sections", new { storeId = model.StoreId });

        }


        [TypeFilter(typeof(StoreManagerAuthorizationFilter))]
        public IActionResult SectionView(int storeId, string section)
        {

            if (InActiveAndNotAdmin(storeId))
            {
                return Content("Sorry, No Store");
            }

            var inputDate = _context.StoreDays
                .Include(s => s.Store)
                .Where(s => s.StoreId == storeId && s.IsFinalised == false).FirstOrDefault().Day;


            if (section == nameof(Persistance.Product))
            {


                //මේ store එකට අදාළ products
                var productOfStore = _context.Products.Include(p => p.StoreProducts)
                    .Include(p => p.ProductType)
                    .Where(p => p.StoreProducts.Any(s => s.StoreId == storeId))
                    .ToList();

                var productTypes = _context.ProductTypes.ToList();


                // දැනට දාල තියන එම දවසේ එම store එකේ සියලුම product sales
                var productSales = _context.StoreProductSales
                    .Where(p => p.StoreId == storeId && p.Day.Date == inputDate.Date)
                    .ToList();


                List<DaysProduct> daysProducts = new List<DaysProduct>();


                foreach (var product in productOfStore)
                {
                    //මේ product එකට අදාළ sale එකක් දාල නම් මේක null නැහැ
                    var releted = productSales.FirstOrDefault(p => p.ProductId == product.Id);

                    DaysProduct daysProduct = new DaysProduct()
                    {
                        Product = product,
                        Sales = (releted != null) ? releted.SaleAmount : 0,
                        Id = product.Id
                    };
                    daysProducts.Add(daysProduct);
                }

                return View("Products", new DaysProductSaleViewModal() { StoreId = storeId, DaysProducts = daysProducts, ProductTypes = productTypes });
            }
            else if (section == nameof(Persistance.Service))
            {
                var servicesOfStore = _context.Services.Include(p => p.StoreServices).Include(p => p.ServiceType)
                   .Where(p => p.StoreServices.Any(s => s.StoreId == storeId))
                   .ToList();

                var serviceTypes = _context.ServiceTypes.ToList();

                List<DaysService> daysServices = new List<DaysService>();


                // var serviceSales = _context.StoreServiceSales.Where(p => p.StoreId == storeId && p.Day == inputDate).ToList();



                foreach (var service in servicesOfStore)
                {
                    var allSales = _context.StoreServiceSales.Where(sss => sss.ServiceId == service.Id && sss.StoreId == storeId).Sum(sss => sss.SalesCount);
                    var lasttimeSales = _context.StoreServiceSales.Where(sss => sss.ServiceId == service.Id && sss.StoreId == storeId && sss.Day.Date == inputDate.Date).FirstOrDefault()?.SalesCount;
                    int close = 0;
                    int open = 0;
                    int initialOpen = _context.StoreServices.First(s => s.StoreId == storeId && s.ServiceId == service.Id).InitialOpen;


                    if (lasttimeSales == null)
                    {
                        //meaning; for current days there have not been any input for this stores this service
                        //then close has to be 0 
                        close = 0;
                        open = allSales + initialOpen;

                    }
                    else
                    {
                        //meaning; for current days there have been input for this stores this service
                        //then close has to be lasttimeSales
                        close = allSales + initialOpen;
                        open = close - lasttimeSales.Value;
                    }


                    DaysService daysService = new DaysService()
                    {
                        Service = service,
                        Id = service.Id,
                        Opening = open,
                        Closing = close
                    };
                    daysServices.Add(daysService);
                }




                return View("Services", new DaysServiceSaleViewModal() { StoreId = storeId, DaysServices = daysServices, ServiceTypes = serviceTypes });
            }
            else if (section == nameof(Persistance.Fuel))
            {
                var fuelsOfStore = _context.Fuels.Include(p => p.StoreFuels).Include(p => p.FuelType)
                     .Where(p => p.StoreFuels.Any(s => s.StoreId == storeId))
                     .ToList();

                var fuelTypes = _context.FuelTypes.ToList();

                List<DaysFuel> daysFuels = new List<DaysFuel>();

                var fuelSales = _context.StoreFuelSales
                 .Where(p => p.StoreId == storeId && p.Day.Date == inputDate.Date)
                 .ToList();


                foreach (var fuel in fuelsOfStore)
                {
                    var releted = fuelSales.FirstOrDefault(p => p.FuelId == fuel.Id);


                    DaysFuel daysFuel = new DaysFuel()
                    {
                        Fuel = fuel,
                        Sales = (releted != null) ? releted.SaleAmount : 0,
                        Id = fuel.Id,
                        Liters = (releted != null) ? releted.Liters : 0
                    };
                    daysFuels.Add(daysFuel);
                }

                return View("Fuels", new DaysFuelSaleViewModal() { StoreId = storeId, DaysFuels = daysFuels, FuelTypes = fuelTypes });
            }
            else if (section == nameof(Persistance.PayOutParty))
            {
                var ExistingPayOutsForTheDay = _context.StoreMgrPayOuts
                    .Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date)
                    .ToList();

                List<ItemViewModel> items = new List<ItemViewModel>();

                foreach (var item in ExistingPayOutsForTheDay)
                {
                    items.Add(new ItemViewModel() { Name = item.Recipient, Amount = item.Amount });
                }

                TempData["Items"] = JsonConvert.SerializeObject(items);

                return RedirectToAction(nameof(PayOuts), "StoreDashboard", new { storeId = storeId });

            }
            else if (section == nameof(Persistance.PaymentCard))
            {
                var ExistingPaymentMethodsForTheDay = _context.StorePaymentMethods
                    .Include(p => p.PaymentCard)
                    .Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date).ToList();
                var PaymentCards = _context.PaymentCards.ToList();


                Dictionary<int, decimal> cardPayments = new Dictionary<int, decimal>();



                foreach (var card in PaymentCards)
                {
                    var amount = ExistingPaymentMethodsForTheDay.FirstOrDefault(e => e.PaymentCardId == card.Id)?.Amount;
                    if (amount != null)
                    {
                        cardPayments.Add(card.Id, amount.Value);
                    }
                    else
                    {
                        cardPayments.Add(card.Id, 0);
                    }
                }


                TempData["cardPayments"] = JsonConvert.SerializeObject(cardPayments);



                return RedirectToAction(nameof(PaymentMethods), "StoreDashboard", new { storeId = storeId, cardPayments = cardPayments });
            }
            else if (section == nameof(Persistance.ScratchCard))
            {
                return RedirectToAction(nameof(ScratchCards), "StoreDashboard", new { storeId = storeId });

            }
            else if (section == nameof(Persistance.SpecificPayOut))
            {


                return RedirectToAction(nameof(SpecificPayOut), "StoreDashboard", new { storeId = storeId });

            }
            else if (section == nameof(Persistance.StoreDay))
            {
                return RedirectToAction(nameof(Balance), "StoreDashboard", new { storeId = storeId });

            }
            else if (section == "Atm")
            {
                return RedirectToAction(nameof(Atm), "StoreDashboard", new { storeId = storeId });

            }

            return null;
        }





        [HttpGet]
        [TypeFilter(typeof(StoreManagerAuthorizationFilter))]
        public IActionResult DaysBusiness(int storeId, string section)
        {

            if (InActiveAndNotAdmin(storeId))
            {
                return Content("Sorry, No Store");
            }

            DaysSalesInputViewModal daysSalesInputViewModal = new DaysSalesInputViewModal();
            var products = _context.Products.Include(p => p.StoreProducts).Where(p => p.StoreProducts.Any(s => s.StoreId == storeId)).ToList();
            var services = _context.Services.Include(s => s.StoreServices).Include(s => s.ServiceType).Where(s => s.StoreServices.Any(s => s.StoreId == storeId)).ToList();
            List<DaysService> daysServices = new List<DaysService>();
            List<DaysProduct> daysProducts = new List<DaysProduct>();
            foreach (var product in products)
            {
                DaysProduct daysProduct = new DaysProduct()
                {
                    Product = product,
                    Sales = 0,
                    Id = product.Id
                };
                daysProducts.Add(daysProduct);
            }
            foreach (var service in services)
            {
                DaysService dashboardService = new DaysService()
                {
                    Service = service,
                    Opening = 0, // have to be retrived from db and calculated
                    Closing = 0,
                    Id = service.Id
                };
                daysServices.Add(dashboardService);
            }

            daysSalesInputViewModal.DaysProducts = daysProducts;
            daysSalesInputViewModal.DaysServices = daysServices;
            daysSalesInputViewModal.ServiceTypes = _context.ServiceTypes.ToList();


            //  dashboardServices.Add(new DashboardService() { Service = });





            return View(daysSalesInputViewModal);
        }



        [HttpPost]
        public IActionResult DaysBusiness(DaysSalesInputViewModal daysSalesInputViewModal)
        {
            List<Persistance.StoreProductSales> storeProductSales = new List<StoreProductSales>();
            List<Persistance.StoreServiceSales> storeServiceSales = new List<StoreServiceSales>();


            foreach (var item in daysSalesInputViewModal.DaysProducts)
            {
                storeProductSales.Add(new StoreProductSales()
                {
                    ProductId = item.Product.Id,
                    SaleAmount = item.Sales,
                    StoreId = daysSalesInputViewModal.StoreId,
                    Day = DateTime.Now
                });
            }

            foreach (var item in daysSalesInputViewModal.DaysServices)
            {
                storeServiceSales.Add(new StoreServiceSales()
                {
                    ServiceId = item.Service.Id,
                    StoreId = daysSalesInputViewModal.StoreId,
                    Day = DateTime.Now,
                    SalesCount = item.Closing - item.Opening,


                });
            }

            _context.Add(storeProductSales);
            _context.Add(storeServiceSales);
            _context.SaveChanges();

            return RedirectToAction(nameof(DaysBusiness), new { storeId = daysSalesInputViewModal.StoreId });
        }

        void SendEmails(DateTime date, int storeId)
        {

            var emailAddresses = _context.Stores.Include(s => s.EmailAddresses)
                .First(s => s.Id == storeId).EmailAddresses.ToList();

            string emailString = GetHtmlString(date, storeId);

            foreach (var email in emailAddresses)
            {
                try
                {
                    SendEmail("<EMAIL>", "Yn_536u1z", email.Address, "Sales Report", emailString);
                }
                catch (Exception ex)
                {

                }
            }
        }

        static void SendEmail(string senderEmail, string appPassword, string recipientEmail, string subject, string body)
        {
            // Create a new MailMessage
            MailMessage mail = new MailMessage(senderEmail, recipientEmail, subject, body);

            // Set the IsBodyHtml property to true
            mail.IsBodyHtml = true;

            // Create a new SmtpClient using Gmail's SMTP server
            SmtpClient smtp = new SmtpClient("samygroups.net");

            // Set the SMTP port to 587 and enable SSL
            smtp.Port = 587;
            smtp.EnableSsl = true;

            // Set the credentials using the sender's email and app password
            smtp.Credentials = new NetworkCredential(senderEmail, appPassword);

            try
            {
                // Send the email
                smtp.Send(mail);
                Console.WriteLine("Email sent successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        public string GetHtmlString(DateTime date, int storeId)
        {
            // Specify the name of the view you want to render
            var viewName = "SaleReportforEmail";


            List<SaleReportDTO> dtos = new List<SaleReportDTO>();
            decimal allSales = 0;
            decimal allSpendings = 0;

            //from startDate to endDate

            SaleReportDTO saleReportDTO = new SaleReportDTO();

            //income

            saleReportDTO.Date = date.ToString("yyyy/MM/dd");

            var productTypes = _context.ProductTypes.ToList();
            decimal productsSum = 0;

            foreach (var productType in productTypes)
            {
                saleReportDTO.ProductCatagoriesSale[productType.Name] = _context.StoreProductSales
                .Include(s => s.Product)
                .ThenInclude(p => p.ProductType)
                .Where(s => s.StoreId == storeId && s.Day.Date == date.Date && s.Product.ProductType.Id == productType.Id)
                .ToList().Sum(p => p.SaleAmount);
                productsSum += saleReportDTO.ProductCatagoriesSale[productType.Name];
            }

            saleReportDTO.FuelSale = _context.StoreFuelSales.Where(s => s.StoreId == storeId
               && s.Day.Date == date.Date).ToList().Sum(f => f.SaleAmount);

            var serviceTypes = _context.ServiceTypes.ToList();
            decimal serviceSum = 0;


            foreach (var serviceType in serviceTypes)
            {
                saleReportDTO.ServiceCatagoriesSale[serviceType.Name] = _context.StoreServiceSales
                .Include(s => s.Service)
                .ThenInclude(p => p.ServiceType)
                .Where(s => s.StoreId == storeId && s.Day.Date == date.Date && s.Service.ServiceType.Id == serviceType.Id)
                .ToList().Sum(s => s.SalesCount * s.Service.Price);

                serviceSum += saleReportDTO.ServiceCatagoriesSale[serviceType.Name];
            }




            saleReportDTO.ScratchCardsSale = _context.StoreScratchCardSales.Include(s => s.ScratchCard).Where(s => s.StoreId == storeId
                 && s.Day.Date == date.Date).ToList().Sum(s => s.SalesCount * s.ScratchCard.Price);

            //spendings
            saleReportDTO.GeneralPayout = _context.StoreMgrPayOuts
               .Where(s => s.StoreId == storeId && s.Date.Date == date.Date).ToList().Sum(s => s.Amount);

            saleReportDTO.SpecificPayout = _context.SpecificPayOuts
                .Where(s => s.StoreId == storeId && s.Date.Date == date.Date).ToList().Sum(s => s.Amount);

            saleReportDTO.DaysSale = productsSum + saleReportDTO.FuelSale + serviceSum + saleReportDTO.ScratchCardsSale;
            saleReportDTO.DaysSpendings = saleReportDTO.GeneralPayout + saleReportDTO.SpecificPayout;
            saleReportDTO.StoreName = _context.Stores.First(s => s.Id == storeId).Name;

            dtos.Add(saleReportDTO);


            allSales += saleReportDTO.DaysSale;
            allSpendings += saleReportDTO.DaysSpendings;



            SaleReportDisplayViewModal saleReportDisplayViewModal = new SaleReportDisplayViewModal();
            saleReportDisplayViewModal.salesDtos = dtos;
            saleReportDisplayViewModal.TotalSales = allSales;
            saleReportDisplayViewModal.TotalSpendings = allSpendings;
            saleReportDisplayViewModal.StartDate = date;
            saleReportDisplayViewModal.EndDate = date;
            saleReportDisplayViewModal.StoreName = _context.Stores.First(s => s.Id == storeId).Name;
            saleReportDisplayViewModal.CompnayName = _context.Companies.First(s => s.Id == _context.Stores.First(s => s.Id == storeId).CompanyId).Name;



            // Create a new action context
            var actionContext = new ActionContext(HttpContext, RouteData, ControllerContext.ActionDescriptor);

            // Create a view engine result
            var viewResult = _viewEngine.FindView(actionContext, viewName, false);

            if (!viewResult.Success)
            {
                throw new InvalidOperationException($"Couldn't find view '{viewName}'");
            }

            // Set the ViewData with the view model
            ViewData.Model = saleReportDisplayViewModal;

            // Create a view context
            var viewContext = new ViewContext(
                actionContext,
                viewResult.View,
                ViewData,
                TempData,
                new StringWriter(),
                new HtmlHelperOptions()
            );

            // Render the view to a string
            viewResult.View.RenderAsync(viewContext).GetAwaiter().GetResult();

            // Get the rendered HTML from the StringWriter
            var htmlContent = (viewContext.Writer as StringWriter).ToString();

            return htmlContent;
        }


    }
}
