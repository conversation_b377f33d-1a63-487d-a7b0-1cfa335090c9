﻿using Microsoft.EntityFrameworkCore;

namespace MultiStore.Persistance
{
    public class Data : DbContext
    {
        public DbSet<Store> Stores { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<StoreProduct> StoreProducts { get; set; }

        public DbSet<StoreProductSales> StoreProductSales { get; set; }

        public DbSet<StoreServiceSales> StoreServiceSales { get; set; }

        public DbSet<StoreService> StoreServices { get; set; }
        public DbSet<Service> Services { get; set; }

        public DbSet<ServiceType> ServiceTypes { get; set; }

        public DbSet<Admin> Admins { get; set; }

        public DbSet<StoreManager> StoreManagers { get; set; }

        public DbSet<BackOffice> BackOffices { get; set; }

        public DbSet<ProductType> ProductTypes { get; set; }

        public DbSet<VatPercentage> VatPercentages { get; set; } // Add this line

        public DbSet<FuelType> FuelTypes { get; set; }

        public DbSet<Fuel> Fuels { get; set; }

        public DbSet<StoreFuel> StoreFuels { get; set; }

        public DbSet<ScratchCard> ScratchCards { get; set; } // Add this line>


        public DbSet<StoreScratchCardSales> StoreScratchCardSales { get; set; }

        public DbSet<StoreScratchCard> StoreScratchCards { get; set; }


        public DbSet<PayOutParty> PayOutParties { get; set; }

        public DbSet<PaymentCard> PaymentCards { get; set; }

        public DbSet<StoreFuelSales> StoreFuelSales { get; set; }

        public DbSet<StoreMgrPayOut> StoreMgrPayOuts { get; set; }


        public DbSet<StorePaymentMethod> StorePaymentMethods { get; set; }


        public DbSet<Supplier> Suppliers { get; set; }


        public DbSet<BankPayment> BankPayments { get; set; }


        public DbSet<CommisionEntry> CommisionEntries { get; set; }

        public DbSet<PurchaseEntry> PurchaseEntries { get; set; }

        public DbSet<CompanyPurchaseEntry> CompanyPurchaseEntries { get; set; }


        public DbSet<SpecificPayOut> SpecificPayOuts { get; set; }

        public DbSet<StoreDay> StoreDays { get; set; }


        public DbSet<Company> Companies { get; set; }

        public DbSet<EmailAddress> EmailAddresses { get; set; }


        public DbSet<Notification> Notifications { get; set; }

        public DbSet<StorePaidIn> StorePaidIns { get; set; }


        public DbSet<CardType> CardTypes { get; set; }

        public DbSet<BackOfficeCardPaymentEntry> BackOfficeCardPaymentEntries { get; set; }

        public Data(DbContextOptions<Data> options) : base(options)
        {

        }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<StoreProduct>()
                .HasKey(sp => new { sp.StoreId, sp.ProductId });
            modelBuilder.Entity<StoreService>()
                .HasKey(sp => new { sp.StoreId, sp.ServiceId });
            modelBuilder.Entity<StoreFuel>()
                .HasKey(sp => new { sp.StoreId, sp.FuelId });
            modelBuilder.Entity<StoreScratchCard>()
                .HasKey(sp => new { sp.StoreId, sp.ScratchCardId });

            modelBuilder.Entity<Persistance.Product>()
          .HasIndex(p => p.Name)
          .IsUnique();

            modelBuilder.Entity<Persistance.Service>()
        .HasIndex(p => p.Name)
        .IsUnique();

            modelBuilder.Entity<Persistance.Fuel>()
        .HasIndex(p => p.Name)
        .IsUnique();

            modelBuilder.Entity<Persistance.ScratchCard>()
        .HasIndex(p => p.Name)
        .IsUnique();

        }
    }
}
