﻿namespace MultiStore.Persistance
{
    public class StoreDay
    {
        public int Id { get; set; }
        public DateTime Day { get; set; }

        public Store Store{ get; set; }

        public int StoreId { get; set; }

        public bool IsFinalised { get; set; }

        public decimal PaidIn { get; set; }

        public decimal Atm { get; set; }

        public decimal AtmWithdrawl { get; set; }

        public decimal BankDeposit { get; set; }

        public string? PaidInRemark { get; set; }

        public string? AtmRemark { get; set; }

        public string? BankDepositRemark { get; set; }


        public decimal CashInHand { get; set; }

        public decimal CarryForward { get; set; }

        public decimal Variance { get; set; }

        public bool FinalizeApplied { get; set; }

        public bool FinalizeAccepted { get; set; }

        public bool FinalizeRejected { get; set; }

        public decimal StoreTransferAmount { get; set; }

        public int? TransferredStoreId { get; set; }

    }
}
