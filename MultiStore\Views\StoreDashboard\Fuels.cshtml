﻿@model MultiStore.Models.DaysFuelSaleViewModal

@{
    ViewData["Title"] = "Products";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Products</h1>

<form asp-action="CreateFuelsSales" method="post">
    <input type="hidden" asp-for="StoreId" />

    @for (int i = 0; i < Model.FuelTypes.Count; i++)
    {
        var fuelType = Model.FuelTypes[i];
        <h2>@fuelType.Name</h2>
        <table class="table" id="<EMAIL>">
            <thead>
                <tr>
                    <th>Fuel Name</th>
                    <th>Liters</th>

                    <th>Sales</th>
                </tr>
            </thead>
            <tbody>
                @for (int j = 0; j < Model.DaysFuels.Count; j++)
                {
                    var daysFuel = Model.DaysFuels[j];
                    if (daysFuel.Fuel.FuelTypeId == fuelType.Id)
                    {
                        <tr>
                            <td>@daysFuel.Fuel.Name</td>
                            <td>
                                <input name="DaysFuels[@j].Liters" value="@daysFuel.Liters" class="form-control"/>
                            </td>

                            <td>
                                <input type="hidden" name="DaysFuels[@j].Id" value="@daysFuel.Id" />
                                <input type="hidden" name="DaysFuels[@j].Fuel.Id" value="@daysFuel.Fuel.Id" />
                                <input name="DaysFuels[@j].Sales" value="@daysFuel.Sales" class="form-control sales-input" data-fuel-type-id="@fuelType.Id" />
                            </td>
                        </tr>
                    }
                }
                <tr>
                    <td><strong>Total Sales</strong></td>
                    <td></td>
                    <td><strong id="<EMAIL>">0.00</strong></td>
                </tr>
            </tbody>
        </table>
    }

    <div class="form-group">
        <input type="submit" value="Submit" class="btn btn-primary" />
    </div>
    <input type="button" value="Cancel" class="btn btn-dark" onclick="window.history.back()" />
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            console.log("DOMContentLoaded");

            function updateSalesSums() {
        @foreach (var fuelType in Model.FuelTypes)
        {
            <text>
                            {
                                let sum = 0;
                                    document.querySelectorAll(`input[data-fuel-type-id='@fuelType.Id']`).forEach(input => {
                                        const value = parseFloat(input.value) || 0;
                            sum += value;
                                    });
                            document.getElementById(`<EMAIL>`).innerText = sum.toFixed(2);
                                }
            </text>
        }
                    }

            document.querySelectorAll(".sales-input").forEach(input => {
                input.addEventListener("input", function () {
                    console.log("Input value changed");
                    updateSalesSums();
                });
            });

            updateSalesSums(); // Initial calculation on page load
        });
    </script>
}