﻿@model MultiStore.Models.DaysServiceSaleViewModal

@{
    ViewData["Title"] = "Products";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Services</h1>

<form asp-action="CreateServicesSales" method="post">
    <input type="hidden" asp-for="StoreId" />

    @for (int i = 0; i < Model.ServiceTypes.Count; i++)
    {
        var serviceType = Model.ServiceTypes[i];
        <h2>@serviceType.Name</h2>
        <table class="table" id="<EMAIL>">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Price</th>
                    <th>Discount</th>
                    <th>Opening</th>
                    <th>Closing</th>
                    <th>Sales</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                @for (int j = 0; j < Model.DaysServices.Count; j++)
                {
                    var daysService = Model.DaysServices[j];
                    if (daysService.Service.ServiceTypeId == serviceType.Id)
                    {
                        <tr>
                            <td>@daysService.Service.Name</td>
                            <td>@daysService.Service.Price</td>
                            <td>@daysService.Service.Discount</td>
                            <td>
                                @daysService.Opening
                                <input type="hidden" name="DaysServices[@j].Opening" value="@daysService.Opening">
                            </td>
                            <td>
                                <input type="hidden" name="DaysServices[@j].Id" value="@daysService.Id" />
                                <input type="hidden" name="DaysServices[@j].Service.Id" value="@daysService.Service.Id" />
                                <input name="DaysServices[@j].Closing" value="@daysService.Closing" class="form-control closing-input"
                                       data-opening="@daysService.Opening"
                                       data-price="@daysService.Service.Price"
                                       data-discount="@daysService.Service.Discount" />
                            </td>
                            <td class="sales-column">0</td>
                            <td class="amount-column">0</td>
                        </tr>
                    }
                }
                <tr>
                    <td colspan="6"><strong>Total Amount</strong></td>
                    <td><strong id="<EMAIL>">0.00</strong></td>
                </tr>
            </tbody>
        </table>
    }

    <div class="form-group">
        <input type="submit" value="Submit" class="btn btn-primary" />
    </div>
    <input type="button" value="Cancel" class="btn btn-dark" onclick="window.history.back()" />
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            function updateCalculations() {
        @foreach (var serviceType in Model.ServiceTypes)
        {
            <text>
                            {
                                let totalAmount = 0;
                                    document.querySelectorAll("#<EMAIL> .closing-input").forEach(input => {
                                        const opening = parseFloat(input.dataset.opening) || 0;
                            const closing = parseFloat(input.value) || 0;
                            const price = parseFloat(input.dataset.price) || 0;
                            const discount = parseFloat(input.dataset.discount) || 0;
                            const sales = closing - opening;
                            const discountAmount = discount * sales;
                            const amount = (price * sales) - discountAmount;

                            const salesColumn = input.closest('tr').querySelector('.sales-column');
                            const amountColumn = input.closest('tr').querySelector('.amount-column');

                            if (salesColumn && amountColumn) {
                                salesColumn.innerText = sales.toFixed(2);
                            amountColumn.innerText = amount.toFixed(2);
                            totalAmount += amount;
                                        }
                                    });
                            document.getElementById("<EMAIL>").innerText = totalAmount.toFixed(2);
                                }
            </text>
        }
                    }

            document.querySelectorAll(".closing-input").forEach(input => {
                input.addEventListener("input", updateCalculations);
            });

            updateCalculations(); // Initial calculation on page load
        });
    </script>
}
