﻿@model MultiStore.Persistance.StorePaymentMethod

@{
    ViewData["Title"] = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Edit Store Payment Method</h1>

<h4>Store Payment Method</h4>
<hr />
<div class="row">
    <div class="col-md-12">
        <form asp-action="Edit">
            <div class="form-group">
                <label asp-for="StoreId" class="control-label"></label>
                <select asp-for="StoreId" class="form-control" asp-items="@(new SelectList(ViewBag.Stores, "Value", "Text"))"></select>
            </div>
            <div class="form-group">
                <label asp-for="PaymentCardId" class="control-label"></label>
                <select asp-for="PaymentCardId" class="form-control" asp-items="@(new SelectList(ViewBag.PaymentCards, "Value", "Text"))"></select>
            </div>
            
            <div class="form-group">
                <label asp-for="Amount" class="control-label"></label>
                <input asp-for="Amount" class="form-control" />
                <span asp-validation-for="Amount" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Date" class="control-label"></label>
                <input asp-for="Date" class="form-control" />
                <span asp-validation-for="Date" class="text-danger"></span>
            </div>
            <input type="hidden" asp-for="Id" />
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
                <a asp-action="Index" class="btn btn-secondary">Back to List</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
