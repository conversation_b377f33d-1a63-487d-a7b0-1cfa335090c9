﻿using MultiStore.Persistance;
using System.Reflection.Metadata.Ecma335;

namespace MultiStore.Models
{
    public class SectionsViewModel
    {

        public int StoreId { get; set; }
        public bool IsFuel { get; set; }

        public decimal CashInHand { get; set; }

        public DateTime InputDate { get; set; }

        public List<StorePaidIn> storePaidIns { get; set; }

        public Dictionary<int,Store> keyValuePairs { get; set; }

        public bool IsThereAPendingPaidInThisScoreTransfered { get; set; }
    }
}
