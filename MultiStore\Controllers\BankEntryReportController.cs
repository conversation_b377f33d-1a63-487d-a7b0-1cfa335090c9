﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class BankEntryReportController : Controller
    {
        private readonly Data _context;

        public BankEntryReportController(Data context)
        {
            _context = context;
        }


        public IActionResult Index()
        {
            ReportViewModel reportViewModel = new ReportViewModel();
            reportViewModel.Companies = _context.Companies.ToList();
            reportViewModel.Stores = _context.Stores.ToList();

            return View(reportViewModel);
        }

        [HttpPost]
        public IActionResult Submit(ReportViewModel model)
        {
            try
            {
                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;
                BankReportViewModel b = new();

                if (model.StartDate != null && model.EndDate != null)
                {
                    if (model.StartDate > model.EndDate)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDate;
                        endDate = (DateTime)model.EndDate;
                    }
                }
                else if (model.SingleDate != null)
                {


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {
                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDate.Value.AddMonths(-2);
                            endDate = model.SingleDate.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }

                }



                if (model.SelectedStoreId != 0)
                {

                    var bankEntries = _context.BankPayments
                         .Include(p => p.Supplier)
                      .Where(p => p.StoreId == model.SelectedStoreId && p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                      .ToList();

                    b.StartDate = startDate;
                    b.EndDate = endDate;

                    b.StoreName = _context.Stores
                     .Where(s => s.Id == model.SelectedStoreId)
                     .FirstOrDefault().Name;

                    b.CompnayName = _context.Companies
                     .Where(c => c.Id == _context.Stores
                     .Where(s => s.Id == model.SelectedStoreId)
                     .FirstOrDefault().CompanyId)
                     .FirstOrDefault().Name;

                    List<BankEntryDTO> bankEntryDtos = new List<BankEntryDTO>();

                    foreach (var item in bankEntries)
                    {
                        bankEntryDtos.Add(new BankEntryDTO()
                        {
                            StoreName = b.StoreName
                            ,
                            Date = item.Date
                            ,
                            SupplierName = item.Supplier.Name
                            ,
                            Amount = item.Amount
                        });
                    }


                    b.BankEntries = bankEntryDtos;


                    return View("bankReport", b);

                }
                else if (model.SelectedCompanyId != 0)
                {

                    List<BankPayment> bankPayments = new();

                    var stores = _context.Stores.Where(s => s.CompanyId == model.SelectedCompanyId)
                       .ToList();


                    foreach (var item in stores)
                    {
                        bankPayments.AddRange(_context.BankPayments
                           .Include(p => p.Supplier)
                        .Where(p => p.StoreId == item.Id && p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                        .ToList());
                    }

                    b.StartDate = startDate;
                    b.EndDate = endDate;

                    b.StoreName = "All";

                    b.CompnayName = _context.Companies.Where(c => c.Id == model.SelectedCompanyId)
                        .FirstOrDefault().Name;

                    List<BankEntryDTO> bankEntryDtos = new List<BankEntryDTO>();



                    foreach (var item in bankPayments)
                    {
                        bankEntryDtos.Add(new BankEntryDTO()
                        {
                            StoreName = b.StoreName
                            ,
                            Date = item.Date
                            ,
                            SupplierName = item.Supplier.Name
                            ,
                            Amount = item.Amount
                        });
                    }


                    b.BankEntries = bankEntryDtos;



                    return View("bankReport", b);
                }
                else //both compnay id and store id =0 
                {

                    List<BankPayment> bankPayments = new();


                    bankPayments = _context.BankPayments
                       .Include(p => p.Supplier)
                       .Include(p => p.Store)
                    .Where(p => p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                    .ToList();



                    b.StartDate = startDate;
                    b.EndDate = endDate;

                    b.StoreName = "All";
                    b.CompnayName = "All";

                    List<BankEntryDTO> bankEntryDtos = new List<BankEntryDTO>();

                    foreach (var item in bankPayments)
                    {
                        bankEntryDtos.Add(new BankEntryDTO()
                        {
                            StoreName = item.Store.Name
                            ,
                            Date = item.Date
                            ,
                            SupplierName = item.Supplier.Name
                            ,
                            Amount = item.Amount
                        });
                    }


                    b.BankEntries = bankEntryDtos;



                    return View("bankReport", b);
                }


            }
            catch (Exception ex)
            {

                return View("Index");
            }
        }

        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }
    }
}
