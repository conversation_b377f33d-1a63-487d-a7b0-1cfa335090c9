﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;
using System.Linq;
using System.Threading.Tasks;

namespace MultiStore.Controllers
{
    [TypeFilter(typeof(AdminAuthorizationFilter))]
    public class PayOutPartyController : Controller
    {
        private readonly Data _context;

        public PayOutPartyController(Data context)
        {
            _context = context;
        }

        // GET: PayOutParty
        public async Task<IActionResult> Index()
        {
            return View(await _context.PayOutParties.ToListAsync());
        }

        // GET: PayOutParty/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var payOutParty = await _context.PayOutParties
                .FirstOrDefaultAsync(m => m.Id == id);
            if (payOutParty == null)
            {
                return NotFound();
            }

            return View(payOutParty);
        }

        // GET: PayOutParty/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: PayOutParty/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Name")] PayOutParty payOutParty)
        {
            //  if (ModelState.IsValid)
            //   {
            _context.Add(payOutParty);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
            //  }
            //  return View(payOutParty);
        }

        // GET: PayOutParty/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var payOutParty = await _context.PayOutParties.FindAsync(id);
            if (payOutParty == null)
            {
                return NotFound();
            }
            return View(payOutParty);
        }

        // POST: PayOutParty/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name")] PayOutParty payOutParty)
        {
            if (id != payOutParty.Id)
            {
                return NotFound();
            }

          //  if (ModelState.IsValid)
          //  {
                try
                {
                    _context.Update(payOutParty);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PayOutPartyExists(payOutParty.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
          //  }
          //  return View(payOutParty);
        }

        // GET: PayOutParty/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var payOutParty = await _context.PayOutParties
                .FirstOrDefaultAsync(m => m.Id == id);
            if (payOutParty == null)
            {
                return NotFound();
            }

            return View(payOutParty);
        }

        // POST: PayOutParty/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var payOutParty = await _context.PayOutParties.FindAsync(id);
            _context.PayOutParties.Remove(payOutParty);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool PayOutPartyExists(int id)
        {
            return _context.PayOutParties.Any(e => e.Id == id);
        }
    }
}
