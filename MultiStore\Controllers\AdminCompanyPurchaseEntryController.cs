﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class AdminCompanyPurchaseEntryController : Controller
    {
        private readonly Data _context;

        public AdminCompanyPurchaseEntryController(Data context)
        {
            _context = context;
        }

        // GET: AdminCompanyPurchaseEntry
        public async Task<IActionResult> Index()
        {
            var entries = _context.CompanyPurchaseEntries
                .Include(c => c.Company)
                .Include(s => s.Supplier);
            return View(await entries.ToListAsync());
        }

        // GET: AdminCompanyPurchaseEntry/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var entry = await _context.CompanyPurchaseEntries.FindAsync(id);
            if (entry == null)
            {
                return NotFound();
            }

            ViewData["CompanyId"] = new SelectList(_context.Companies, "Id", "Name", entry.CompanyId);
            ViewData["SupplierId"] = new SelectList(_context.Suppliers, "Id", "Name", entry.SupplierId);
            return View(entry);
        }

        // POST: AdminCompanyPurchaseEntry/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,CompanyId,InvoiceNumber,SupplierId,Gross,Vat,Net,Date")] CompanyPurchaseEntry entry)
        {
            if (id != entry.Id)
            {
                return NotFound();
            }

            //    if (ModelState.IsValid)
            //    {
            try
            {
                _context.Update(entry);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!CompanyPurchaseEntryExists(entry.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Index));
            //    }

            //   ViewData["CompanyId"] = new SelectList(_context.Companies, "Id", "Name", entry.CompanyId);
            //   ViewData["SupplierId"] = new SelectList(_context.Suppliers, "Id", "Name", entry.SupplierId);
            //   return View(entry);
        }

        // GET: AdminCompanyPurchaseEntry/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var entry = await _context.CompanyPurchaseEntries
                .Include(c => c.Company)
                .Include(s => s.Supplier)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (entry == null)
            {
                return NotFound();
            }

            return View(entry);
        }

        // POST: AdminCompanyPurchaseEntry/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var entry = await _context.CompanyPurchaseEntries.FindAsync(id);
            _context.CompanyPurchaseEntries.Remove(entry);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool CompanyPurchaseEntryExists(int id)
        {
            return _context.CompanyPurchaseEntries.Any(e => e.Id == id);
        }
    }
}
