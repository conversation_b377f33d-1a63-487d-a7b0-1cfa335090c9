﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MultiStore.Migrations
{
    public partial class migration90 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ScratchCards",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Activation = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScratchCards", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "StoreScratchCards",
                columns: table => new
                {
                    StoreId = table.Column<int>(type: "int", nullable: false),
                    ScratchCardId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StoreScratchCards", x => new { x.StoreId, x.ScratchCardId });
                    table.ForeignKey(
                        name: "FK_StoreScratchCards_ScratchCards_ScratchCardId",
                        column: x => x.ScratchCardId,
                        principalTable: "ScratchCards",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StoreScratchCards_Stores_StoreId",
                        column: x => x.StoreId,
                        principalTable: "Stores",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_StoreScratchCards_ScratchCardId",
                table: "StoreScratchCards",
                column: "ScratchCardId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "StoreScratchCards");

            migrationBuilder.DropTable(
                name: "ScratchCards");
        }
    }
}
