﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using System.Linq;
using System.Threading.Tasks;

namespace MultiStore.Controllers
{
    [TypeFilter(typeof(AdminAuthorizationFilter))]
    public class ScratchCardsController : Controller
    {
        private readonly Data _context;

        public ScratchCardsController(Data context)
        {
            _context = context;
        }

        // GET: ScratchCards
        public async Task<IActionResult> Index()
        {
            return View(await _context.ScratchCards.ToListAsync());
        }

        // GET: ScratchCards/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var scratchCard = await _context.ScratchCards
                .FirstOrDefaultAsync(m => m.Id == id);
            if (scratchCard == null)
            {
                return NotFound();
            }

            return View(scratchCard);
        }

        // GET: ScratchCards/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: ScratchCards/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Name,Price")] ScratchCard scratchCard)
        {
            //  if (ModelState.IsValid)
            //  {
            try
            {
                _context.Add(scratchCard);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                ViewData["Message"] = ex.Message;

                return View(scratchCard);
            }
            //  }
        }

        // GET: ScratchCards/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var scratchCard = await _context.ScratchCards.FindAsync(id);
            if (scratchCard == null)
            {
                return NotFound();
            }
            return View(scratchCard);
        }

        // POST: ScratchCards/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,Price")] ScratchCard scratchCard)
        {
            if (id != scratchCard.Id)
            {
                return NotFound();
            }

            //  if (ModelState.IsValid)
            //   {
            try
            {
                _context.Update(scratchCard);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ScratchCardExists(scratchCard.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
                //    }
            }
            catch (Exception ex)
            {
                ViewData["Message"] = ex.Message;

                return View(scratchCard);
            }
            return View(scratchCard);
        }

        // GET: ScratchCards/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var scratchCard = await _context.ScratchCards
                .FirstOrDefaultAsync(m => m.Id == id);
            if (scratchCard == null)
            {
                return NotFound();
            }

            return View(scratchCard);
        }

        // POST: ScratchCards/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var scratchCard = await _context.ScratchCards.FindAsync(id);
            _context.ScratchCards.Remove(scratchCard);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool ScratchCardExists(int id)
        {
            return _context.ScratchCards.Any(e => e.Id == id);
        }
    }
}
