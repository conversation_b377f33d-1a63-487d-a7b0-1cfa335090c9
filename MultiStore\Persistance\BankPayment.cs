﻿namespace MultiStore.Persistance
{
    public class BankPayment
    {
        public int Id { get; set; }
        public Store Store { get; set; }

        public int StoreId { get; set; }

        public Supplier Supplier { get; set; }

        public int SupplierId { get; set; }

        public decimal Amount { get; set; }

        public DateTime Date { get; set; }

    }


    public class BackOfficeCardPaymentEntry
    {
        public int Id { get; set; }
        public Store Store { get; set; }

        public int StoreId { get; set; }

        public CardType CardType { get; set; }

        public int CardTypeId { get; set; }

        public decimal Amount { get; set; }

        public DateTime Date { get; set; }

    }

    public class BankEntryDTO
    {
        public string StoreName { get; set; }


        public string SupplierName { get; set; }


        public decimal Amount { get; set; }

        public DateTime Date { get; set; }
    }
}
