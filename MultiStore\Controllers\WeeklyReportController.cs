﻿using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using System.Globalization;
using System.Runtime.CompilerServices;

namespace MultiStore.Controllers
{
    public class WeeklyReportController : Controller
    {

        private readonly Data _context;

        public WeeklyReportController(Data context)
        {
            _context = context;
        }

        public class YourViewModel
        {
            public bool IsChecked { get; set; }
        }



        public IActionResult Index()
        {
            var companies = _context.Companies.ToList(); // Assuming you're fetching companies from the database
            ViewData["Companies"] = new SelectList(companies, "Id", "Name");
            return View();
        }

        [HttpPost]
        public IActionResult SubmitWeek(YourViewModel yourViewModel, string selectedWeek, int? SelectedCompanyId)
        {
            // Example of input: "2024-W33"
            if (selectedWeek != null && selectedWeek.Length == 8 && selectedWeek[5] == 'W')
            {
                int year = int.Parse(selectedWeek.Substring(0, 4));  // Extract the year: "2024"
                int week = int.Parse(selectedWeek.Substring(6));     // Extract the week: "33"

                // Get the start date of the week (Monday as the first day)
                DateTime weekStartDate = FirstDateOfWeekISO8601(year, week);
                DateTime endDate = weekStartDate.AddDays(6);
                DateTime firstDateOfYear = new DateTime(year, 1, 1);




                List<Store> stores = null;
                Company? company = null;

                if (SelectedCompanyId == null)
                {
                    stores = _context.Stores.Include(s => s.Company).ToList();
                }
                else
                {
                    company = _context.Companies.First(c => c.Id == SelectedCompanyId);

                    stores = _context.Stores.Where(s => s.CompanyId == company.Id).ToList();

                }

                //get stores of the company

                List<WeeklyReportViewModel> weeklyReports = new List<WeeklyReportViewModel>();


                foreach (var store in stores)
                {
                    decimal _totalSales = 0;
                    decimal _shopSales = 0;
                    decimal _totalSpendins = 0;
                    decimal _paidIns = 0;
                    decimal _atm = 0;
                    decimal _bankDeposit = 0;
                    decimal _storeTransfer = 0;
                    decimal _clearedDeposit = 0;
                    decimal _cardpayments = 0;
                    decimal _bankEntry = 0;
                    decimal _varience = 0;
                    decimal _cashInHand = 0;
                    decimal _clearedCash = 0;
                    decimal _weeklyPercentage = 0;



                    decimal yearlyPercentage = 0;
                    decimal yearlyclearedCash = 0;

                    if (yourViewModel.IsChecked == true)
                    {
                        for (var date = firstDateOfYear; date <= endDate; date = date.AddDays(1))
                        {
                            var storeDay = _context.StoreDays
                         .FirstOrDefault(sd => sd.StoreId == store.Id && sd.Day == date);



                            if (storeDay == null)
                            {
                                continue;
                            }

                            SaleReportDTO saleReportDTO = new SaleReportDTO();

                            //income

                            saleReportDTO.Date = date.ToString("yyyy/MM/dd");

                            var productTypes = _context.ProductTypes.ToList();
                            var ShopSalesProductType = _context.ProductTypes.First();
                            decimal productsSum = 0;

                            foreach (var productType in productTypes)
                            {
                                saleReportDTO.ProductCatagoriesSale[productType.Name] = _context.StoreProductSales
                                .Include(s => s.Product)
                                .ThenInclude(p => p.ProductType)
                                .Where(s => s.StoreId == store.Id && s.Day.Date == date.Date && s.Product.ProductType.Id == productType.Id)
                                .ToList().Sum(p => p.SaleAmount);
                                productsSum += saleReportDTO.ProductCatagoriesSale[productType.Name];

                                if (productType.Id == ShopSalesProductType.Id)
                                {
                                    _shopSales += saleReportDTO.ProductCatagoriesSale[productType.Name];
                                }
                            }

                            saleReportDTO.FuelSale = _context.StoreFuelSales.Where(s => s.StoreId == store.Id
                               && s.Day.Date == date.Date).ToList().Sum(f => f.SaleAmount);


                            var serviceTypes = _context.ServiceTypes.ToList();
                            decimal serviceSum = 0;


                            foreach (var serviceType in serviceTypes)
                            {
                                saleReportDTO.ServiceCatagoriesSale[serviceType.Name] = _context.StoreServiceSales
                                .Include(s => s.Service)
                                .ThenInclude(p => p.ServiceType)
                                .Where(s => s.StoreId == store.Id && s.Day.Date == date.Date && s.Service.ServiceType.Id == serviceType.Id)
                                .ToList().Sum(s => s.SalesCount * s.Service.Price);

                                serviceSum += saleReportDTO.ServiceCatagoriesSale[serviceType.Name];
                            }


                            saleReportDTO.ScratchCardsSale = _context.StoreScratchCardSales.Include(s => s.ScratchCard).Where(s => s.StoreId == store.Id
                                 && s.Day.Date == date.Date).ToList().Sum(s => s.SalesCount * s.ScratchCard.Price);

                            //spendings
                            saleReportDTO.GeneralPayout = _context.StoreMgrPayOuts
                               .Where(s => s.StoreId == store.Id && s.Date.Date == date.Date).ToList().Sum(s => s.Amount);

                            saleReportDTO.SpecificPayout = _context.SpecificPayOuts
                                .Where(s => s.StoreId == store.Id && s.Date.Date == date.Date).ToList().Sum(s => s.Amount);

                            /*2*/
                            var ts = productsSum + saleReportDTO.FuelSale + serviceSum + saleReportDTO.ScratchCardsSale;
                            _totalSales += ts;

                            var tsp = saleReportDTO.GeneralPayout + saleReportDTO.SpecificPayout;
                            _totalSpendins += tsp;
                            /*1*/
                            var storeName = _context.Stores.First(s => s.Id == store.Id).Name;



                            _paidIns += storeDay.PaidIn;
                            _atm += storeDay.Atm;
                            _bankDeposit += storeDay.BankDeposit;
                            _storeTransfer += storeDay.StoreTransferAmount;

                            _clearedDeposit += storeDay.Atm + storeDay.BankDeposit + storeDay.StoreTransferAmount - storeDay.PaidIn;

                            var storePaymentMethods = _context.StorePaymentMethods
             .Where(spm => spm.StoreId == store.Id && spm.Date.Date == date.Date)
             .ToList();


                            foreach (var item in storePaymentMethods)
                            {
                                _cardpayments += item.Amount;

                            }




                            var be = _context.BankPayments
                                  .Where(b => b.StoreId == store.Id && b.Date.Date == date.Date).Sum(b => b.Amount);

                            _bankEntry += be;

                            _varience += storeDay.Variance;


                            yearlyclearedCash += ts - tsp - be;

                            if (date == endDate)
                            {
                                yearlyPercentage = (yearlyclearedCash / _totalSales) * 100;
                            }


                        }
                    }

                    decimal totalSales = 0;
                    decimal shopSales = 0;
                    decimal totalSpendins = 0;
                    decimal paidIns = 0;
                    decimal atm = 0;
                    decimal bankDeposit = 0;
                    decimal storeTransfer = 0;
                    decimal clearedDeposit = 0;
                    decimal cardpayments = 0;
                    decimal bankEntry = 0;
                    decimal varience = 0;
                    decimal cashInHand = 0;
                    decimal clearedCash = 0;
                    decimal weeklyPercentage = 0;

                    for (var date = weekStartDate; date <= endDate; date = date.AddDays(1))
                    {
                        var storeDay = _context.StoreDays
                      .Where(sd => sd.StoreId == store.Id && sd.Day == date)
                      .FirstOrDefault();


                        if (storeDay == null)
                        {
                            continue;
                        }

                        SaleReportDTO saleReportDTO = new SaleReportDTO();

                        //income

                        saleReportDTO.Date = date.ToString("yyyy/MM/dd");

                        var productTypes = _context.ProductTypes.ToList();
                        var ShopSalesProductType = _context.ProductTypes.First();
                        decimal productsSum = 0;

                        foreach (var productType in productTypes)
                        {
                            saleReportDTO.ProductCatagoriesSale[productType.Name] = _context.StoreProductSales
                            .Include(s => s.Product)
                            .ThenInclude(p => p.ProductType)
                            .Where(s => s.StoreId == store.Id && s.Day.Date == date.Date && s.Product.ProductType.Id == productType.Id)
                            .ToList().Sum(p => p.SaleAmount);
                            productsSum += saleReportDTO.ProductCatagoriesSale[productType.Name];

                            if (productType.Id == ShopSalesProductType.Id)
                            {
                                shopSales += saleReportDTO.ProductCatagoriesSale[productType.Name];
                            }
                        }

                        saleReportDTO.FuelSale = _context.StoreFuelSales.Where(s => s.StoreId == store.Id
                           && s.Day.Date == date.Date).ToList().Sum(f => f.SaleAmount);


                        var serviceTypes = _context.ServiceTypes.ToList();
                        decimal serviceSum = 0;


                        foreach (var serviceType in serviceTypes)
                        {
                            saleReportDTO.ServiceCatagoriesSale[serviceType.Name] = _context.StoreServiceSales
                            .Include(s => s.Service)
                            .ThenInclude(p => p.ServiceType)
                            .Where(s => s.StoreId == store.Id && s.Day.Date == date.Date && s.Service.ServiceType.Id == serviceType.Id)
                            .ToList().Sum(s => s.SalesCount * s.Service.Price);

                            serviceSum += saleReportDTO.ServiceCatagoriesSale[serviceType.Name];
                        }


                        saleReportDTO.ScratchCardsSale = _context.StoreScratchCardSales.Include(s => s.ScratchCard).Where(s => s.StoreId == store.Id
                             && s.Day.Date == date.Date).ToList().Sum(s => s.SalesCount * s.ScratchCard.Price);

                        //spendings
                        saleReportDTO.GeneralPayout = _context.StoreMgrPayOuts
                           .Where(s => s.StoreId == store.Id && s.Date.Date == date.Date).ToList().Sum(s => s.Amount);

                        saleReportDTO.SpecificPayout = _context.SpecificPayOuts
                            .Where(s => s.StoreId == store.Id && s.Date.Date == date.Date).ToList().Sum(s => s.Amount);

                        /*2*/
                        var ts = productsSum + saleReportDTO.FuelSale + serviceSum + saleReportDTO.ScratchCardsSale;
                        totalSales += ts;

                        var tsp = saleReportDTO.GeneralPayout + saleReportDTO.SpecificPayout;
                        totalSpendins += tsp;
                        /*1*/
                        var storeName = _context.Stores.First(s => s.Id == store.Id).Name;



                        paidIns += storeDay.PaidIn;
                        atm += storeDay.Atm;
                        bankDeposit += storeDay.BankDeposit;
                        storeTransfer += storeDay.StoreTransferAmount;

                        clearedDeposit += storeDay.Atm + storeDay.BankDeposit + storeDay.StoreTransferAmount - storeDay.PaidIn;

                        var storePaymentMethods = _context.StorePaymentMethods
         .Where(spm => spm.StoreId == store.Id && spm.Date.Date == date.Date)
         .ToList();


                        foreach (var item in storePaymentMethods)
                        {
                            cardpayments += item.Amount;

                        }




                        var be = _context.BankPayments
                              .Where(b => b.StoreId == store.Id && b.Date.Date == date.Date).Sum(b => b.Amount);

                        bankEntry += be;

                        varience += storeDay.Variance;


                        clearedCash += ts - tsp - be;

                        if (date == endDate)
                        {
                            cashInHand = storeDay.CashInHand;
                            weeklyPercentage = (clearedCash / totalSales) * 100;
                        }
                    }



                    weeklyReports.Add(new WeeklyReportViewModel
                    {
                        CompanyName = (company == null) ? store.Company.Name : company.Name,
                        StoreName = store.Name,
                        TotalSales = totalSales,
                        ShopSales = shopSales,
                        TotalSpendings = totalSpendins,
                        PaidIns = paidIns,
                        Atm = atm,
                        BankDeposit = bankDeposit,
                        StoreTransfer = storeTransfer,
                        ClearedDeposit = clearedDeposit,
                        CardPayments = cardpayments,
                        BankEntry = bankEntry,
                        Variance = varience,
                        CashInHand = cashInHand,
                        ClearedCash = clearedCash,
                        WeeklyPercentage = weeklyPercentage,
                        YearlyClearedCash = yearlyclearedCash,
                        YearlyPercentage = yearlyPercentage
                    });

                }

                ViewData["StartDate"] = weekStartDate.ToShortDateString();
                ViewData["EndDate"] = endDate.ToShortDateString();



                ViewData["CompanyName"] = (SelectedCompanyId != null) ? _context.Companies
                    .First(c => c.Id == SelectedCompanyId).Name : "All Companies";


                return View("SelectWeek", weeklyReports);

            }
            else
            {
                ViewBag.Message = "Invalid week selected";
            }

            return View("Index");
        }

        public StoreBalanceViewModel Balance(int storeId, DateTime inputDate)
        {


            var storeDay = _context.StoreDays
          .Where(sd => sd.StoreId == storeId && sd.Day == inputDate)
          .FirstOrDefault();

            var storePaymentMethods = _context.StorePaymentMethods
            .Where(spm => spm.StoreId == storeId && spm.Date.Date == inputDate.Date)
            .ToList();

            StoreBalanceViewModel storeBalanceViewModel = new();
            storeBalanceViewModel.StoreId = storeId;
            storeBalanceViewModel.PaidIn = storeDay.PaidIn;
            storeBalanceViewModel.Atm = storeDay.Atm;
            storeBalanceViewModel.BankDeposit = storeDay.BankDeposit;
            storeBalanceViewModel.CarryForward = storeDay.CarryForward;

            foreach (var item in storePaymentMethods)
            {
                storeBalanceViewModel.Card += item.Amount;

            }


            //calculate all sales amount

            var storeFuelSalesAmount = _context.StoreFuelSales
            .Where(sfs => sfs.StoreId == storeId && sfs.Day.Date == inputDate.Date)
            .Sum(sfs => sfs.SaleAmount);


            var storeProductSales = _context.StoreProductSales
            .Where(sps => sps.StoreId == storeId && sps.Day.Date == inputDate.Date)
             .Sum(sfs => sfs.SaleAmount);

            var scracthCardSaleList = _context.StoreScratchCardSales.Include(s => s.ScratchCard)
                .Where(s => s.StoreId == storeId && s.Day.Date == inputDate.Date).ToList();

            decimal storeScratchCardSalesAmount = 0;

            foreach (var item in scracthCardSaleList)
            {

                storeScratchCardSalesAmount += item.SalesCount * item.ScratchCard.Price;
            }

            var storeServiceSalesList = _context.StoreServiceSales.Include(s => s.Service)
                .Where(s => s.StoreId == storeId && s.Day.Date == inputDate.Date).ToList();


            decimal storeServiceSalesAmount = 0;

            foreach (var item in storeServiceSalesList)
            {
                storeServiceSalesAmount += item.SalesCount * item.Service.Price;
            }

            decimal AllSalesAmount = storeFuelSalesAmount + storeProductSales + storeScratchCardSalesAmount + storeServiceSalesAmount;


            storeBalanceViewModel.Cash = AllSalesAmount - storeBalanceViewModel.Card;


            var storeMgrPayOuts = _context.StoreMgrPayOuts
           .Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date)
           .ToList();

            foreach (var item in storeMgrPayOuts)
            {
                storeBalanceViewModel.PaidOut += item.Amount;
            }

            var specificPayOuts = _context.SpecificPayOuts
            .Where(s => s.StoreId == storeId && s.Date.Date == inputDate.Date)
            .ToList();

            foreach (var item in specificPayOuts)
            {
                storeBalanceViewModel.PaidOut += item.Amount;
            }

            return storeBalanceViewModel;
        }



        // Helper method to get the start date of a given ISO 8601 week number
        private static DateTime FirstDateOfWeekISO8601(int year, int weekOfYear)
        {
            DateTime jan1 = new DateTime(year, 1, 1);
            // ISO 8601 weeks start with Monday, with the first week having at least four days
            int daysOffset = DayOfWeek.Thursday - jan1.DayOfWeek;

            // Use Thursday as the base to calculate the first week
            DateTime firstThursday = jan1.AddDays(daysOffset);
            // Calculate the first week
            var calendar = CultureInfo.CurrentCulture.Calendar;
            int firstWeek = calendar.GetWeekOfYear(firstThursday, CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);

            var weekNum = weekOfYear;
            if (firstWeek == 1)
            {
                weekNum -= 1;
            }

            // Add weeks based on the ISO 8601 standard
            return firstThursday.AddDays(weekNum * 7 - 3); // Monday of the selected week
        }
    }

}

