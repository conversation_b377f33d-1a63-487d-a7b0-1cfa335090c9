﻿using MultiStore.Persistance;
using System.ComponentModel.DataAnnotations;

namespace MultiStore.Models
{
    public class DaysProduct
    {
        public int Id { get; set; }

        public Product Product { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "The field Sales must be a positive number.")]
        public decimal Sales { get; set; }
    }

    public class DaysFuel
    {
        public int Id { get; set; }

        public Fuel Fuel { get; set; }

        public decimal Liters { get; set; }

        public decimal Sales { get; set; }
    }
}
