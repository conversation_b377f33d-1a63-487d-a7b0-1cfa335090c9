﻿@model MultiStore.Persistance.ScratchCard

@{
    ViewData["Title"] = "Create";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Create</h1>

<h4>ScratchCard</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Price" class="control-label"></label>
                <input asp-for="Price" class="form-control" />
                <span asp-validation-for="Price" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@if (ViewData["Message"] != null)
{
    <div class="alert alert-danger">
        @ViewData["Message"]
    </div>
}

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
