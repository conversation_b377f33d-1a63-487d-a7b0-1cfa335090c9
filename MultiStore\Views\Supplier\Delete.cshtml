﻿@model MultiStore.Persistance.Supplier

@{
    ViewData["Title"] = "Delete Supplier";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Delete Supplier</h1>

<h4>Supplier</h4>
<hr />
<div>
    <h4>Are you sure you want to delete this?</h4>
    <div>
        <h4>@Model.Name</h4>
    </div>
    <div>
        <form asp-action="Delete">
            <input type="hidden" asp-for="Id" />
            <input type="submit" value="Delete" class="btn btn-danger" /> |
            <a asp-action="Index">Back to List</a>
        </form>
    </div>
</div>
