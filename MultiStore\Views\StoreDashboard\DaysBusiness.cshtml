﻿@model MultiStore.Models.DaysSalesInputViewModal

@{
    ViewData["Title"] = "Days Business";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}





<form asp-action="DaysBusiness" method="post">

    <input type="hidden" asp-for="@Model.StoreId" />

    <h1>Products</h1>


    <table class="table">
        <thead>
            <tr>
                <th>Name</th>
                <th></th>
            </tr>
        </thead>
        <tbody>

            @for (int i = 0; i < Model.DaysProducts.Count(); i++)
            {
                <tr>
                    <td>@Model.DaysProducts[i].Product.Name</td>
                    <td>
                        <input asp-for="@Model.DaysProducts[i].Sales">
                        <input type="hidden" asp-for="@Model.DaysProducts[i].Product.Id">
                    </td>
                    <td>
                    </td>
                </tr>
            }

            <tr>
                <td>Total</td>
                <td id="total_sales"></td>
            </tr>
        </tbody>
    </table>

    @foreach (var serviceType in Model.ServiceTypes)
    {
        var serviceDays = Model.DaysServices.Where(ds => ds.Service.ServiceTypeId == serviceType.Id).ToList();

        <h1>@serviceType.Name</h1>

        <table class="table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Price</th>
                    <th>Opening</th>
                    <th>Closing</th>
                    <th>Sales</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>

                @for (int i = 0; i < serviceDays.Count(); i++)
                {
                    <tr>
                        <td>@serviceDays[i].Service.Name</td>
                        <td>@serviceDays[i].Service.Price</td>
                        <td>@serviceDays[i].Opening</td>
                        <td>
                            <input asp-for="@serviceDays[i].Closing">
                            <input type="hidden" asp-for="@serviceDays[i].Service.Id">
                            <input type="hidden" asp-for="@serviceDays[i].Id">

                        </td>
                        <td></td>
                        <td></td>

                        @*  <td>
                <input asp-for="@Model.DaysProducts[i].Sales">
                </td>*@
                        <td>
                        </td>
                    </tr>
                }

                <tr>
                    <td>Total</td>
                    <td id="total_sales"></td>
                </tr>
            </tbody>
        </table>

    }



    <input type="submit" value="Submit" class="btn btn-primary">

</form>

