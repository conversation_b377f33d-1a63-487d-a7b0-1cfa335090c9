﻿@model MultiStore.Models.CommisionEntryViewModel

@{
    ViewData["Title"] = "Commision Entries";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Commision Entries</h1>

<form asp-action="CommissionEntries" method="post">
    <table class="table" id="commision-entries-table">
        <thead>
            <tr>
                <th>Commision Type</th>
                <th>Store</th>
                <th>Invoice Number</th>
                <th>Supplier</th>
                <th>Gross</th>
                <th>Vat</th>
                <th>Net</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <select class="form-control" name="commisionEntries[0].CommisionType">
                        <option value="Income">Income</option>
                        <option value="Outcome">Outcome</option>
                    </select>
                </td>
                <td>
                    <select class="form-control" name="commisionEntries[0].StoreId">
                        <option value="">Select Store</option>
                        @foreach (var store in Model.Stores)
                        {
                            <option value="@store.Id">@store.Name</option>
                        }
                    </select>
                </td>
                <td>
                    <input type="text" class="form-control" name="commisionEntries[0].InvoiceNumber" />
                </td>
                <td>
                    <select class="form-control" name="commisionEntries[0].SupplierId">
                        <option value="">Select Supplier</option>
                        @foreach (var supplier in Model.Suppliers)
                        {
                            <option value="@supplier.Id">@supplier.Name</option>
                        }
                    </select>
                </td>
                <td>
                    <input type="number" step="0.01" class="form-control gross" name="commisionEntries[0].Gross" />
                </td>
                <td>
                    <input type="number" step="0.01" class="form-control vat" name="commisionEntries[0].Vat" />
                </td>
                <td>
                    <input type="number" step="0.01" class="form-control net" name="commisionEntries[0].Net" readonly />
                </td>
                <td>
                    <button type="button" class="btn btn-danger remove-row">Remove</button>
                </td>
            </tr>
        </tbody>
    </table>

    <input type="date" name="recordDate" value="@DateTime.Now.ToString("yyyy-MM-dd")" />

    <button type="button" class="btn btn-primary" id="add-row">Add Row</button>
    <input type="submit" value="Submit" class="btn btn-success" />
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        $(document).ready(function () {
            var rowIdx = 1;

            $('#add-row').click(function () {
                var newRow = `<tr>
                                        <td>
                                            <select class="form-control" name="commisionEntries[${rowIdx}].CommisionType">
                                                <option value="Income">Income</option>
                                                <option value="Outcome">Outcome</option>
                                            </select>
                                        </td>
                                        <td>
                                            <select class="form-control" name="commisionEntries[${rowIdx}].StoreId">
                                                <option value="">Select Store</option>
        @foreach (var store in Model.Stores)
        {
                                                        <option value="@store.Id">@store.Name</option>
        }
                                            </select>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" name="commisionEntries[${rowIdx}].InvoiceNumber" />
                                        </td>
                                        <td>
                                            <select class="form-control" name="commisionEntries[${rowIdx}].SupplierId">
                                                <option value="">Select Supplier</option>
        @foreach (var supplier in Model.Suppliers)
        {
                                                        <option value="@supplier.Id">@supplier.Name</option>
        }
                                            </select>
                                        </td>
                                        <td>
                                            <input type="number" step="0.01" class="form-control gross" name="commisionEntries[${rowIdx}].Gross" />
                                        </td>
                                        <td>
                                            <input type="number" step="0.01" class="form-control vat" name="commisionEntries[${rowIdx}].Vat" />
                                        </td>
                                        <td>
                                            <input type="number" step="0.01" class="form-control net" name="commisionEntries[${rowIdx}].Net" readonly />
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-danger remove-row">Remove</button>
                                        </td>
                                    </tr>`;
                $('#commision-entries-table tbody').append(newRow);
                rowIdx++;
            });

            $('#commision-entries-table').on('click', '.remove-row', function () {
                $(this).closest('tr').remove();
            });

            // Event delegation for dynamically added rows
            $('#commision-entries-table').on('input', '.gross, .vat', function () {
                var row = $(this).closest('tr');
                var gross = parseFloat(row.find('.gross').val()) || 0;
                var vat = parseFloat(row.find('.vat').val()) || 0;
                var net = gross - vat;
                row.find('.net').val(net.toFixed(2)); // Set the Net value
            });
        });
    </script>
}
