﻿@model MultiStore.Models.PaymentsViewModel

@{
    ViewData["Title"] = "Create Payments";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Create Payments</h1>

<form asp-action="PaymentMethods" method="post">

    <input type="hidden" asp-for="StoreId" />

    <h2>Payment Cards</h2>
    <table class="table">
        <thead>
            <tr>
                <th>Payment Card Name</th>
                <th>Amount</th>
            </tr>
        </thead>
        <tbody>
            @for (int i = 0; i < Model.PaymentCardAmounts.Count; i++)
            {
                <tr>
                    <td>
                        @Model.PaymentCardAmounts[i].PaymentCardName
                        <input type="hidden" name="PaymentCardAmounts[@i].PaymentCardId" value="@Model.PaymentCardAmounts[i].PaymentCardId" />
                        <input type="hidden" name="PaymentCardAmounts[@i].PaymentCardName" value="@Model.PaymentCardAmounts[i].PaymentCardName" />
                    </td>
                    <td>
                        <input type="number" name="PaymentCardAmounts[@i].Amount" class="form-control" value="@Model.PaymentCardAmounts[i].Amount" />
                    </td>
                </tr>
            }
        </tbody>
    </table>

        <div class="form-group">
        <input type="submit" value="Submit" class="btn btn-primary" />
    </div>
    <input type="button" value="Cancel" class="btn btn-dark" onclick="window.history.back()" />

</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
