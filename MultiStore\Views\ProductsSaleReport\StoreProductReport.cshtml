﻿@model IEnumerable<MultiStore.Persistance.StoreProductSales>

@{
    ViewData["Title"] = "Products Sales Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed
                                                     // Update the layout path as needed
    var saleAmountSum = Model.Sum(item => item.SaleAmount);

}

<h1>Products Sales Report</h1>

<div>
    <p><strong>Store:</strong> @ViewData["StoreName"]</p>

    <p><strong>Company:</strong> @ViewData["CompanyName"]</p>
    <p><strong>From:</strong> @ViewData["StartDate"] <strong>To:</strong> @ViewData["EndDate"]</p>
</div>


<table class="table">
    <thead>
        <tr>
            <th>Store</th>
            <th>Product</th>
            <th>Sale Amount</th>
            <th>Time Span</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item?.Store?.Name</td>
                <td>@item?.Product?.Name</td>
                <td>@item?.SaleAmount.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@item?.Day.ToShortDateString()</td>
            </tr>
        }
        <tr>
            <td colspan="2"><strong>Total</strong></td>
            <td><strong>@saleAmountSum.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
            <td></td>
        </tr>
    </tbody>
</table>
