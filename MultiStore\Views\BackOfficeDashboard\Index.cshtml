﻿

@{
    ViewData["Title"] = "Backoffice";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

@{
//if viewbag exists then use it
    if (ViewBag.ErrorMessage != null)
    {
       <label class="text-danger" >@ViewBag.ErrorMessage</label>
    }

}

<h1>Back Office | Entries</h1>

<div class="col-md-3">
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="BackOfficeDashboard" asp-action="BankPayments" class="btn btn-primary btn-block">Bank Entries</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="BackOfficeDashboard" asp-action="CommissionEntries" class="btn btn-primary btn-block">Commission Entries</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="BackOfficeDashboard" asp-action="PurchaseEntries" class="btn btn-primary btn-block">Purchase Entries</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="BackOfficeDashboard" asp-action="CompanyPurchaseEntries" class="btn btn-primary btn-block">(Company) Purchase Entries</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="BackOfficeDashboard" asp-action="BackOfficeCardPaymentEntries" class="btn btn-primary btn-block">Card Entries</a>

    </div>


<h1>Reports</h1>

<div>
    <div class="col">
            <div class="col-md-3">
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Reports" asp-action="Index" class="btn btn-primary btn-block">Purchase Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="CommissionReport" asp-action="Index" class="btn btn-primary btn-block">Commission Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="BankEntryReport" asp-action="Index" class="btn btn-primary btn-block">Bank Entry Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="SaleReport" asp-action="Index" class="btn btn-primary btn-block">Store Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="CompanyPurchase" asp-action="Index" class="btn btn-primary btn-block">Company Purchase Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ProductsSaleReport" asp-action="Index" class="btn btn-primary btn-block">Product Sale Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ProductSaleReport2" asp-action="Index" class="btn btn-primary btn-block">Product Sale Report 2</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="FuelSaleReport" asp-action="Index" class="btn btn-primary btn-block">Fuel Sale Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="FuelSaleReport2" asp-action="Index" class="btn btn-primary btn-block">Fuel Sale Report 2</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ServiceSaleReport" asp-action="Index" class="btn btn-primary btn-block">Service Sale Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ScratchCardSaleReport" asp-action="Index" class="btn btn-primary btn-block">Scratch Cards Sale Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="PaymentMethodsReport" asp-action="Index" class="btn btn-primary btn-block">Payment Methods Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="PaymentMethodsReport2" asp-action="Index" class="btn btn-primary btn-block">Payment Methods Report 2</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="PayoutReport" asp-action="Index" class="btn btn-primary btn-block">Payout Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="PayoutSpecificReport" asp-action="Index" class="btn btn-primary btn-block">Specific Payout Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="BalanceReport" asp-action="Index" class="btn btn-primary btn-block">Balance Report</a>
            <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ClosingBalance" asp-action="Index" class="btn btn-primary btn-block">Closing Balance Report</a>
        </div>
    </div>
</div>


