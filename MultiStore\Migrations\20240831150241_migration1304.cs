﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MultiStore.Migrations
{
    public partial class migration1304 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Enabled",
                table: "EmailAddresses");

            migrationBuilder.AddColumn<int>(
                name: "StoreId",
                table: "EmailAddresses",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_EmailAddresses_StoreId",
                table: "EmailAddresses",
                column: "StoreId");

            migrationBuilder.AddForeignKey(
                name: "FK_EmailAddresses_Stores_StoreId",
                table: "EmailAddresses",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_EmailAddresses_Stores_StoreId",
                table: "EmailAddresses");

            migrationBuilder.DropIndex(
                name: "IX_EmailAddresses_StoreId",
                table: "EmailAddresses");

            migrationBuilder.DropColumn(
                name: "StoreId",
                table: "EmailAddresses");

            migrationBuilder.AddColumn<bool>(
                name: "Enabled",
                table: "EmailAddresses",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
