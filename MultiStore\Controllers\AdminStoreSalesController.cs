﻿using Microsoft.AspNetCore.Mvc;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class AdminStoreSalesController : Controller
    {


        [HttpGet]
        public IActionResult Index()
        {
            AdminSaleViewModal adminSaleViewModal = new AdminSaleViewModal();
            return View(adminSaleViewModal);
        }


        [HttpPost]
        public IActionResult Index(AdminSaleViewModal model, string submitButton)
        {

            DateTime startDate = DateTime.MinValue;
            DateTime endDate = DateTime.MinValue;

            if (model.StartDate != null && model.EndDate != null)
            {
                if (model.StartDate > model.EndDate)
                {
                    return View("Index");
                }
                else
                {
                    startDate = (DateTime)model.StartDate;
                    endDate = (DateTime)model.EndDate;

                }
            }
            else if (model.SingleDate != null)
            {


                //calculate start and end date depening on single date 
                //and period
                switch (model.Period.ToLower())
                {
                    case "week":
                        startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                        endDate = startDate.AddDays(6);
                        break;
                    case "month":
                        startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                        endDate = startDate.AddMonths(1).AddDays(-1);
                        break;
                    case "3months":
                        startDate = model.SingleDate.Value.AddMonths(-2);
                        endDate = model.SingleDate.Value;
                        break;
                    case "year":
                        startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                        endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                        break;
                    default:
                        return View("Index");
                }

            }


            if (submitButton == "Product")
            {
                return RedirectToAction("Index", "AdminStoreProductSales", new { startDate = startDate, endDate = endDate });
            }
            else if (submitButton == "Service")
            {
                return RedirectToAction("Index", "AdminStoreServiceSales", new { startDate = startDate, endDate = endDate });

            }
            else if (submitButton == "ScratchCard")
            {
                return RedirectToAction("Index", "AdminStoreScratchCardSales", new { startDate = startDate, endDate = endDate });

            }
            else
            {
                return RedirectToAction("Index", "AdminStoreFuelSales", new { startDate = startDate, endDate = endDate });

            }

        }

        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }

        public class AdminSaleViewModal
        {
            public DateTime? StartDate { get; set; }
            public DateTime? EndDate { get; set; }
            public DateTime? SingleDate { get; set; }
            public string Period { get; set; }


        }

    }
}
