﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using static MultiStore.Controllers.ProductsSaleReportController;
using System.Globalization;

namespace MultiStore.Controllers
{
    public class ProductSaleReport2Controller : Controller
    {

        private readonly Data _context;

        public ProductSaleReport2Controller(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            SaleReportSelectViewModal reportViewModel = new SaleReportSelectViewModal();
            reportViewModel.Stores = _context.Stores.ToList();
            reportViewModel.Companies = _context.Companies.ToList();

            return View(reportViewModel);
        }

        [HttpPost]
        public IActionResult Submit(SaleReportSelectViewModal model)
        {

            bool newMode = false;

            try
            {
                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;

                if (model.StartDate != null && model.EndDate != null)
                {
                    if (model.StartDate > model.EndDate)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDate;
                        endDate = (DateTime)model.EndDate;
                        newMode = true;
                    }
                }
                else if (model.SingleDate != null)
                {


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {
                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDate.Value.AddMonths(-2);
                            endDate = model.SingleDate.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }

                }

                List<StoreProductSales>? storeProductSales = new();


                //if (model.SelectedStoreId != 0)
                //{

                //    storeProductSales = _context.StoreProductSales
                //         .Include(s => s.Store)
                //         .Include(s => s.Product)
                //      .Where(p => p.StoreId == model.SelectedStoreId && p.Day.Date >= startDate.Date && p.Day.Date <= endDate.Date)
                //      .ToList();


                //}
                if (model.SelectedCompanyId != 0)
                {


                    var stores1 = _context.Stores.Where(s => s.CompanyId == model.SelectedCompanyId)
                       .ToList();


                    foreach (var item in stores1)
                    {
                        storeProductSales.AddRange(_context.StoreProductSales
                           .Include(s => s.Store)
                         .Include(s => s.Product)
                         .ThenInclude(p => p.VatPercentage)
                        .Where(p => p.StoreId == item.Id && p.Day.Date >= startDate.Date && p.Day.Date <= endDate.Date)
                        .ToList());
                    }



                }
                else //both compnay id=0 
                {
                    storeProductSales = _context.StoreProductSales
                        .Include(s => s.Store)
                        .Include(s => s.Product)
                         .ThenInclude(p => p.VatPercentage)

                     .Where(p => p.Day.Date >= startDate.Date && p.Day.Date <= endDate.Date)
                     .ToList();
                }

                var groupedSales = storeProductSales
   .Select(s => new
   {
       ProductName = s.Product.Name,
       StoreName = s.Store.Name,
       s.SaleAmount
   })
   .GroupBy(s => new { s.ProductName, s.StoreName })
   .Select(g => new
   {
       g.Key.ProductName,
       g.Key.StoreName,
       TotalSales = g.Sum(s => s.SaleAmount)
   })
   .ToList();





                var products = storeProductSales.Select(s => s.Product).Distinct().ToList();
                var stores = storeProductSales.Select(s => s.Store.Name).Distinct().ToList();

                var viewModel = products.Select(product => new StoreProductSalesViewModel
                {
                    ProductName = product.Name,
                    Vat = product.VatPercentage.Percentage,
                    StoreSales = stores.ToDictionary(
                        storeName => storeName,
                        storeName => groupedSales
                            .Where(g => g.ProductName == product.Name && g.StoreName == storeName)
                            .Select(g => g.TotalSales)
                            .FirstOrDefault()
                    ),
                    TotalSales = groupedSales
                        .Where(g => g.ProductName == product.Name)
                        .Sum(g => g.TotalSales)
                }).ToList();

                ViewData["StartDate"] = startDate.ToShortDateString();
                ViewData["EndDate"] = endDate.ToShortDateString();

                ViewData["CompanyName"] = (model.SelectedCompanyId != 0) ? _context.Companies
                    .FirstOrDefault(c => c.Id == model.SelectedCompanyId).Name : "All Companies";


                return View("StoreProductSalesSummary", viewModel);
            }
            catch (Exception ex)
            {

                return View("Index");
            }
        }

        public class StoreProductSalesViewModel
        {
            public string ProductName { get; set; }
            public decimal Vat { get; set; }

            public Dictionary<string, decimal> StoreSales { get; set; }
            public decimal TotalSales { get; set; }
        }


        public static DateTime FirstDateOfWeek(DateTime date, DayOfWeek startOfWeek)
        {
            int diff = (7 + (date.DayOfWeek - startOfWeek)) % 7;
            return date.AddDays(-1 * diff).Date;
        }

        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }
    }
}
