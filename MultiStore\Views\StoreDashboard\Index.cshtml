﻿@model IEnumerable<MultiStore.Persistance.Store>

@{
    ViewData["Title"] = "Home";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Select Store</h1>

@if (Model != null)
{
    <div class="row">
        <div class="col-md-9">
            @foreach (var store in Model)
            {
                <a style="width: 100%; margin-bottom: 0.5rem" asp-controller="StoreDashboard" asp-action="Sections" asp-route-storeId="@store.Id" class="btn btn-primary btn-block">@store.Name</a>
            }
        </div>
        <div class="col-md-3">
            <!-- User Manual Notes Section -->
            <div class="user-manual">
                <h2 class="text-success">User Manual Notes</h2>
                <ul class="list-group">
                    <li class="list-group-item list-group-item-info">Ensure you select a store to view its sections.</li>
                    <li class="list-group-item list-group-item-warning">If you encounter any issues, please contact support</li>
                    <li class="list-group-item list-group-item-danger">Error messages will be displayed in red alert boxes.</li>
                    <li class="list-group-item list-group-item-info">Use Orginal Report for Paypoint and National Lottery Sales.</li>
                    <li class="list-group-item list-group-item-success">Use Physical Stock Qty for Instant Lottery ( Scratch Card ) Sales.</li>
                    <li class="list-group-item list-group-item-info">Input Actual Readings from Machine for Costa, Airserv, Car Wash and Jet Wash in Service Sections.</li>
                </ul>
            </div>
        </div>
    </div>
}

@{
    var errorMessage = ViewBag.ErrorMessage as string;
}

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger">
        @errorMessage
    </div>
}
