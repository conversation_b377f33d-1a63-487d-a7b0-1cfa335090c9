﻿@model MultiStore.Persistance.Admin


@{
    ViewData["Title"] = "Company Purchase Report";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2>Reset Admin</h2>

<div class="row">
    <div class="col-md-4">
        <form asp-action="Update">
           <div class="form-group">
                <label asp-for="Password" class="control-label"></label>
                <input asp-for="Password" class="form-control" />
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Update Password" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>
