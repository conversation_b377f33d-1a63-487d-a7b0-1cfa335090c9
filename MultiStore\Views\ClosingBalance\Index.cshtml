﻿@model List<MultiStore.Controllers.ClosingBalanceController.ClosingBalanceViewModel>

@{
    ViewData["Title"] = "Closing Balances";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed

}

<h2>@ViewData["Title"]</h2>

<table class="table table-bordered table-striped">
    <thead class="thead-dark">
        <tr>
            <th>Date</th>
            <th>Store Name</th>
            <th>Closing Balance</th>
            <th>No of Days</th> <!-- New column for number of days -->
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.Date.ToString("yyyy-MM-dd")</td>
                <td>@item.StoreName</td>
                <td>@item.ClosingBalance.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@item.NoOfDays</td> <!-- Display number of days -->
            </tr>
        }
    </tbody>
</table>
