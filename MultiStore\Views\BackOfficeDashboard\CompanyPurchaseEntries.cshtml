﻿@model MultiStore.Models.CompanyPurchaseEntryViewModel

@{
    ViewData["Title"] = "Purchase Entries";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Purchase Entries</h1>

<form asp-action="CompanyPurchaseEntries" method="post">
    <table class="table" id="purchase-entries-table">
        <thead>
            <tr>
                <th>Company</th>
                <th>Invoice Number</th>
                <th>Supplier</th>
                <th>Gross</th>
                <th>Vat</th>
                <th>Net</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <select class="form-control" name="purchaseEntries[0].CompanyId">
                        <option value="">Select Company</option>
                        @foreach (var company in Model.Companies)
                        {
                            <option value="@company.Id">@company.Name</option>
                        }
                    </select>
                </td>
                <td>
                    <input type="text" class="form-control" name="purchaseEntries[0].InvoiceNumber" />
                </td>
                <td>
                    <select class="form-control" name="purchaseEntries[0].SupplierId">
                        <option value="">Select Supplier</option>
                        @foreach (var supplier in Model.Suppliers)
                        {
                            <option value="@supplier.Id">@supplier.Name</option>
                        }
                    </select>
                </td>
                <td>
                    <input type="number" step="0.01" class="form-control gross" name="purchaseEntries[0].Gross" />
                </td>
                <td>
                    <input type="number" step="0.01" class="form-control vat" name="purchaseEntries[0].Vat" />
                </td>
                <td>
                    <input type="number" step="0.01" class="form-control net" name="purchaseEntries[0].Net" readonly />
                </td>
                <td>
                    <button type="button" class="btn btn-danger remove-row">Remove</button>
                </td>
            </tr>
        </tbody>
    </table>

    <input type="date" name="recordDate" value="@DateTime.Now.ToString("yyyy-MM-dd")" />

    <button type="button" class="btn btn-primary" id="add-row">Add Row</button>
    <input type="submit" value="Submit" class="btn btn-success" />
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        $(document).ready(function () {
            var rowIdx = 1;

            $('#add-row').click(function () {
                var newRow = `<tr>
                                        <td>
                                            <select class="form-control" name="purchaseEntries[${rowIdx}].CompanyId">
                                                <option value="">Select Company</option>
        @foreach (var company in Model.Companies)
        {
                                                        <option value="@company.Id">@company.Name</option>
        }
                                            </select>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" name="purchaseEntries[${rowIdx}].InvoiceNumber" />
                                        </td>
                                        <td>
                                            <select class="form-control" name="purchaseEntries[${rowIdx}].SupplierId">
                                                <option value="">Select Supplier</option>
        @foreach (var supplier in Model.Suppliers)
        {
                                                        <option value="@supplier.Id">@supplier.Name</option>
        }
                                            </select>
                                        </td>
                                        <td>
                                            <input type="number" step="0.01" class="form-control gross" name="purchaseEntries[${rowIdx}].Gross" />
                                        </td>
                                        <td>
                                            <input type="number" step="0.01" class="form-control vat" name="purchaseEntries[${rowIdx}].Vat" />
                                        </td>
                                        <td>
                                            <input type="number" step="0.01" class="form-control net" name="purchaseEntries[${rowIdx}].Net" readonly />
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-danger remove-row">Remove</button>
                                        </td>
                                    </tr>`;
                $('#purchase-entries-table tbody').append(newRow);
                rowIdx++;
            });

            $('#purchase-entries-table').on('click', '.remove-row', function () {
                $(this).closest('tr').remove();
            });

            // Event delegation for dynamically added rows
            $('#purchase-entries-table').on('input', '.gross, .vat', function () {
                var row = $(this).closest('tr');
                var gross = parseFloat(row.find('.gross').val()) || 0;
                var vat = parseFloat(row.find('.vat').val()) || 0;
                var net = gross - vat;
                row.find('.net').val(net.toFixed(2)); // Set the Net value
            });
        });
    </script>
}
