﻿@model IEnumerable<MultiStore.Persistance.Service>

@{
    ViewData["Title"] = "Services";
    Layout = "~/Views/Shared/_Layout.cshtml";

}

<h1>Services</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Price)
            </th>
            <th>
                Service Type
            </th>
            <th>
                Vat Percentage
            </th>
            <th>
                Discount
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.Name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Price)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.ServiceType.Name)
                </td>
                <th>
                    @Html.DisplayFor(modelItem => item.VatPercentage.Percentage)
                </th>
                <th>
                    @Html.DisplayFor(modelItem => item.Discount)
                </th>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@item.Id">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
