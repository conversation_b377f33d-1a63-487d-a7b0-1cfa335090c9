﻿@model MultiStore.Persistance.StoreServiceSales

@{
    ViewData["Title"] = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Edit Store Service Sales</h1>

<h4>Store Service Sales</h4>
<hr />
<div class="row">
    <div class="col-md-12">
        <form asp-action="Edit">
            <div class="form-group">
                <label asp-for="StoreId" class="control-label"></label>
                <select asp-for="StoreId" class="form-control" asp-items="@(new SelectList(ViewBag.Stores, "Value", "Text"))"></select>
            </div>
            <div class="form-group">
                <label asp-for="ServiceId" class="control-label"></label>
                <select asp-for="ServiceId" class="form-control" asp-items="@(new SelectList(ViewBag.Services, "Value", "Text"))"></select>
            </div>
            <div class="form-group">
                <label asp-for="SalesCount" class="control-label"></label>
                <input asp-for="SalesCount" class="form-control" />
                <span asp-validation-for="SalesCount" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Day" class="control-label"></label>
                <input asp-for="Day" class="form-control" />
                <span asp-validation-for="Day" class="text-danger"></span>
            </div>
            <input type="hidden" asp-for="Id" />
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
                <a asp-action="Index" class="btn btn-secondary">Back to List</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
