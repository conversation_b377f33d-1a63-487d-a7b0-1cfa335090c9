﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using MultiStore.Models;
using MultiStore.Persistance;

namespace MultiStore
{
    public class UtilityService : IUtilityService
    {

        private readonly Data _context;

        public UtilityService(Data context)
        {
            _context = context;
        }

        public void UpdateStoreDaysToReflectChanges(int storeId)
        {

            var storeDays = _context.StoreDays
                          .Where(sd => sd.StoreId == storeId)
                          .OrderBy(sd => sd.Day)
                          .ToList();




            foreach (var storeDay in storeDays)
            {
                //if (storeDay.Day.Day == new DateTime(2024, 10, 20).Day)
                //    continue;


                loop(storeDay);
            }


            void loop(StoreDay storeDay)
            {
                decimal NextDayCarryFwd = 0;

                {


                    decimal card = 0;
                    decimal paidOut = 0;

                    var paidInSum = _context.StorePaidIns
                        .Where(s => s.TransferToStoreId == storeDay.StoreId && s.TransfeffedToDate.Date == storeDay.Day.Date && s.StorePaidInState != StorePaidInState.Rejected)
                    .Sum(s => s.Amount);


                    var transferFromStoreDayIds = _context.StorePaidIns
                        .Where(s => s.TransferToStoreId == storeDay.StoreId && s.TransfeffedToDate.Date == storeDay.Day.Date && s.StorePaidInState != StorePaidInState.Rejected)
                        .Select(s => s.TransferredFromStoreDayId).ToList();

                   var storenames = _context.StoreDays.Include(sd => sd.Store)
                        .Where(sd => transferFromStoreDayIds.Contains(sd.Id))
                        .Select(sd => sd.Store.Name).ToList();

                    var storenamesString = "";

                    foreach (var storeName in storenames)
                    {
                        storenamesString += storeName + " ";
                    }


                    var storePaymentMethods = _context.StorePaymentMethods
                    .Where(spm => spm.StoreId == storeId && spm.Date.Date == storeDay.Day.Date)
                    .ToList();





                    card += storePaymentMethods.Sum(item => item.Amount);



                    //calculate all sales amount

                    var storeFuelSalesAmount = _context.StoreFuelSales
                    .Where(sfs => sfs.StoreId == storeId && sfs.Day.Date == storeDay.Day.Date)
                    .Sum(sfs => sfs.SaleAmount);


                    var storeProductSales = _context.StoreProductSales
                    .Where(sps => sps.StoreId == storeId && sps.Day.Date == storeDay.Day.Date)
                    .Sum(sfs => sfs.SaleAmount);

                    var scracthCardSaleList = _context.StoreScratchCardSales.Include(s => s.ScratchCard)
                        .Where(s => s.StoreId == storeId && s.Day.Date == storeDay.Day.Date).ToList();

                    decimal storeScratchCardSalesAmount = 0;

                    foreach (var item in scracthCardSaleList)
                    {

                        storeScratchCardSalesAmount += item.SalesCount * item.ScratchCard.Price;
                    }

                    var storeServiceSalesList = _context.StoreServiceSales.Include(s => s.Service)
                        .Where(s => s.StoreId == storeId && s.Day.Date == storeDay.Day.Date).ToList();


                    decimal storeServiceSalesAmount = 0;

                    foreach (var item in storeServiceSalesList)
                    {
                        storeServiceSalesAmount += item.SalesCount * (item.Service.Price-item.Service.Discount);
                    }

                    decimal AllSalesAmount = storeFuelSalesAmount + storeProductSales + storeScratchCardSalesAmount + storeServiceSalesAmount;




                    var storeMgrPayOuts = _context.StoreMgrPayOuts
                   .Where(s => s.StoreId == storeId && s.Date.Date == storeDay.Day.Date)
                   .ToList();

                    foreach (var item in storeMgrPayOuts)
                    {
                        paidOut += item.Amount;
                    }

                    var specificPayOuts = _context.SpecificPayOuts
                    .Where(s => s.StoreId == storeId && s.Date.Date == storeDay.Day.Date)
                    .ToList();

                    foreach (var item in specificPayOuts)
                    {
                        paidOut += item.Amount;
                    }

                    //var balance = carryFwd + card + cash - paidOut + paidIn - atm - bankDeposit;

                    NextDayCarryFwd = storeDay.CarryForward + AllSalesAmount - card
                   - paidOut + paidInSum
                   - storeDay.Atm - storeDay.BankDeposit
                   + storeDay.Variance - storeDay.StoreTransferAmount;


                    storeDay.PaidIn = paidInSum;
                    storeDay.PaidInRemark = storenamesString;
                    storeDay.CashInHand = NextDayCarryFwd;
                    _context.SaveChanges();

                }


                try
                {
                    _context.StoreDays.Where(s => s.StoreId == storeId && s.Day.Date == storeDay.Day.AddDays(1).Date)
                  .FirstOrDefault().CarryForward = NextDayCarryFwd;

                    _context.SaveChanges();
                }
                catch (Exception ex)
                {

                }


            }

        }

    }
}
