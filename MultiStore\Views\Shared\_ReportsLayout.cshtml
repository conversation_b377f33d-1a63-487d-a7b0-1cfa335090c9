﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - DRS</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/MultiStore.styles.css" asp-append-version="true" />


</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">Samy Groups | MultiStore</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        @if (Context.Session.Keys.Count() != 0)
                        {
                            <li class="nav-item">
                                <span class="nav-link text-dark">Welcome, @Context.Session.GetString("FirstName") (@Context.Session.GetString("UserType"))</span>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link text-dark" asp-area="" asp-controller="Home" asp-action="Logout">Log Out</a>
                            </li>

                        }
                        <li class="nav-item">
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <div><button class="btn btn-primary" id="pdfExportButton" onclick="downloadPDF()" >Export Pdf</button></div>
        <label for="cars">Document Size:</label>
        <select name="size" id="size">
            <option value="a0">A0</option>
            <option value="a1">A1</option>
            <option value="a2">A2</option>
            <option value="a3">A3</option>
            <option value="a4">A4</option>
            <option value="a5">A5</option>
            <option value="a6">A6</option>
            <option value="a7">A7</option>
            <option value="a8">A8</option>
            <option value="a9">A9</option>
            <option value="a10">A10</option>

        </select>
        <label for="cars">Orientation:</label>
        <select name="Orientation" id="Orientation">
            <option value="portrait">portrait</option>
            <option value="landscape">landscape</option>
        </select>
        <main id="main" role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2024 - Samy Groups | MultiStore -  - Version 3.1 <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script> 
  @*   <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/0.4.1/html2canvas.min.js"></script> *@

    @await RenderSectionAsync("Scripts", required: false)


    <script>




        function downloadPDF() {
            const element = document.getElementById('main');

            // Get selected size and orientation from the dropdowns
            const selectedSize = document.getElementById('size').value;
            const selectedOrientation = document.getElementById('Orientation').value;


            // Use html2canvas to take a screenshot of the selected element
            html2canvas(element).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF(selectedOrientation[0], 'mm', selectedSize.toLowerCase());


                const pdfWidth = pdf.internal.pageSize.getWidth();
                const pdfHeight = pdf.internal.pageSize.getHeight();

                const canvasWidth = canvas.width;
                const canvasHeight = canvas.height;

                // Calculate the scale factor to fit the canvas width into the PDF width
                const scaleFactor = pdfWidth / canvasWidth;

                // Calculate the scaled height based on the scale factor
                const imgWidth = pdfWidth;
                const imgHeight = canvasHeight * scaleFactor;  // Maintain aspect ratio with scaling

                let heightLeft = imgHeight;
                let position = 0;

                // Add the image to the PDF page by page
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pdfHeight;

                while (heightLeft > 0) {
                    position -= pdfHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pdfHeight;
                }


                // Create a Blob from the PDF and open it in a new tab
                const blob = pdf.output('blob');
                const url = URL.createObjectURL(blob);
                window.open(url, '_blank');
            });
        }
    </script>


</body>
</html>
