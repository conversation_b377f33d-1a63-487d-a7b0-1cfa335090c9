﻿@model MultiStore.Persistance.SpecificPayOut

@{
    ViewData["Title"] = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2>Delete</h2>

<h4>SpecificPayOut</h4>
<hr />
<div>
    <h4>Are you sure you want to delete this?</h4>
    <div>
        <h5>@Model.PayOutParty.Name</h5>
        <h5>@Model.Store.Name</h5>
        <h5>@Model.Amount</h5>
        <h5>@Model.Date</h5>
    </div>
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
