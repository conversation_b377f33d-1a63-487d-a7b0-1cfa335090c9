﻿@{
    ViewData["Title"] = "Home";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}


<h1>Samy Groups | Admin Dashboard</h1>


<div>Pending Finalizes</div>
<div id="notificationCount">0</div>
<audio id="alertSound" src="~/alert-sound.mp3" preload="auto"></audio>


<div class="container">
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">Product</div>
                <div class="card-body">
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ProductType" asp-action="Index" class="btn btn-primary btn-block">Product Types</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="VatPercentage" asp-action="Index" class="btn btn-primary btn-block">Vat Rates</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Product" asp-action="Index" class="btn btn-primary btn-block">Producs</a>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">Service </div>
                <div class="card-body">


                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ServiceType" asp-action="Index" class="btn btn-primary btn-block">Service Types</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Service" asp-action="Index" class="btn btn-primary btn-block">Services</a>





                </div>
            </div>
        </div>




        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">Fuel</div>
                <div class="card-body">
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Fuel" asp-action="Index" class="btn btn-primary btn-block">Fuel</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="FuelType" asp-action="Index" class="btn btn-primary btn-block">Fuel Types</a>


                </div>
            </div>
        </div>


        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">Payments</div>
                <div class="card-body">
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="PayOutParty" asp-action="Index" class="btn btn-primary btn-block">Pay out parties</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Supplier" asp-action="Index" class="btn btn-primary btn-block">Suppliers</a>

                </div>
            </div>
        </div>


        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">Accounts</div>
                <div class="card-body">
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="StoreManagers" asp-action="Index" class="btn btn-primary btn-block">Store Managers Accounts</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="BackOffice" asp-action="Index" class="btn btn-primary btn-block">Back Office Accounts</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Admin" asp-action="Update" class="btn btn-primary btn-block">Admin</a>

                </div>
            </div>
        </div>


        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">MISC.</div>
                <div class="card-body">
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ScratchCards" asp-action="Index" class="btn btn-primary btn-block">Scratch Cards</a>

                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="AdminCompanies" asp-action="Index" class="btn btn-primary btn-block">Companies</a>

                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="CardTypes" asp-action="Index" class="btn btn-primary btn-block">Card Types</a>

                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="PaymentCard" asp-action="Index" class="btn btn-primary btn-block">Cards</a>
                   

                </div>
            </div>
        </div>


        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">Store</div>
                <div class="card-body">
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Stores" asp-action="Index" class="btn btn-primary btn-block">Stores</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="StoreDashboard" asp-action="Index" class="btn btn-primary btn-block">Store Dashboards</a>
                </div>
            </div>
        </div>


        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">Store Inputs</div>
                <div class="card-body">
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="AdminStoreSales" asp-action="Index" class="btn btn-primary btn-block">Sales</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="AdminStoreOtherInputs" asp-action="Index" class="btn btn-primary btn-block"> Other Store Inputs</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="AdminApproveDaysSale" asp-action="Index" class="btn btn-primary btn-block"> Approve Finalize</a>


               </div>
            </div>
        </div>


        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">Back Office</div>
                <div class="card-body">
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="BackOfficeDashboard" asp-action="Index" class="btn btn-primary btn-block">Back Office Dashboards</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="AdminCommisionEntry" asp-action="Index" class="btn btn-primary btn-block">Admin Commission Entries</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="AdminBankPayment" asp-action="Index" class="btn btn-primary btn-block">Admin Bank Entries</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="AdminPurchaseEntry" asp-action="Index" class="btn btn-primary btn-block">Admin Purchase Entries</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="AdminCompanyPurchaseEntry" asp-action="Index" class="btn btn-primary btn-block">Admin (Company) Purchase Entries</a>
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="AdminBackOfficeCardPaymentEntry" asp-action="Index" class="btn btn-primary btn-block">Admin Backend Card Payment Entries</a>

                </div>
            </div>
        </div>



        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">Settings</div>
                <div class="card-body">
                    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="AdminSettings" asp-action="Index" class="btn btn-primary btn-block">Settings</a>
                   @*  <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="AdminAtmStartValues" asp-action="Index" class="btn btn-primary btn-block">Atm Open Values</a> *@

                </div>
            </div>
        </div>

    </div>

    <div>
        <div class="card">
            <div class="card-header">Reports</div>
            <div class="card-body" >

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="CardPaymentReport" asp-action="Index" class="btn btn-primary btn-block">Card Payment Summary Report</a>


                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="AtmReport" asp-action="Index" class="btn btn-primary btn-block">Atm Report</a>


                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="WeeklyReport" asp-action="Index" class="btn btn-primary btn-block">Weekly Report</a>

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Reports" asp-action="Index" class="btn btn-primary btn-block">Purchace Report</a>
                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="CommissionReport" asp-action="Index" class="btn btn-primary btn-block">Commission Report</a>

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="BankEntryReport" asp-action="Index" class="btn btn-primary btn-block">Bank Entry Report</a>

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="SaleReport" asp-action="Index" class="btn btn-primary btn-block">Store Report</a>

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="CompanyPurchase" asp-action="Index" class="btn btn-primary btn-block">Company Purchase Report</a>

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ProductsSaleReport" asp-action="Index" class="btn btn-primary btn-block">Product Sale Report</a>

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ProductSaleReport2" asp-action="Index" class="btn btn-primary btn-block">Product Sale Total Report</a>


                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="FuelSaleReport" asp-action="Index" class="btn btn-primary btn-block">Fuel Sale Report</a>
                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="FuelSaleReport2" asp-action="Index" class="btn btn-primary btn-block">Fuel Sale Total Report</a>


                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ServiceSaleReport" asp-action="Index" class="btn btn-primary btn-block">Service Sale Report</a>

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ScratchCardSaleReport" asp-action="Index" class="btn btn-primary btn-block">Scratch Cards Sale Report</a>

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="PaymentMethodsReport" asp-action="Index" class="btn btn-primary btn-block">Card Payment Report</a>

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="PaymentMethodsReport2" asp-action="Index" class="btn btn-primary btn-block">Card Payment Total Report</a>
                

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="PayoutReport" asp-action="Index" class="btn btn-primary btn-block"> Back Office Payout Report</a>

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="PayoutSpecificReport" asp-action="Index" class="btn btn-primary btn-block">TILL Payout Report</a>

                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="BalanceReport" asp-action="Index" class="btn btn-primary btn-block">Balance Report</a>
                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ClosingBalance" asp-action="Index" class="btn btn-primary btn-block">DRS Report</a>
                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="ClosingBalance2" asp-action="Index" class="btn btn-primary btn-block">Store Remarks Report</a>
                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="GrossMarginReport" asp-action="Index" class="btn btn-primary btn-block">Gross Margin Report</a>
               
                <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="FuelLetersReport" asp-action="Index" class="btn btn-primary btn-block">Fuel Liters Report</a>

            </div>
        </div>
    </div>


</div>


<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<script type="text/javascript">
    $(document).ready(function() {
            let oldCount = 0; // Initialize old count

        function updateNotificationCount() {
            $.ajax({
                url: '@Url.Action("GetFinalizeNotifications", "Home")',
                type: 'GET',
                    success: function(result) {
                    let newCount = parseInt(result, 10); // Convert result to an integer

                    // Compare new count with old count
                    if (newCount > oldCount) {
                            // Update the old count to the new count
                            oldCount = newCount;

                                $('#alertSound')[0].play();


                            // Display popup
                            alert('New notifications: ' + newCount);

                            // Play sound
                        }

                        // Update the notification count display
                        $('#notificationCount').text(newCount);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching notifications:', error);
                    }
                });
            }

            // Update the notification count immediately when the page loads
            updateNotificationCount();

            // Set an interval to update the notification count every 10 seconds
            setInterval(updateNotificationCount, 10000);
        });
</script>













