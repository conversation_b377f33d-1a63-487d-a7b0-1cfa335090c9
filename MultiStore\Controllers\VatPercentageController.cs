﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;
using System.Linq;
using System.Threading.Tasks;

namespace MultiStore.Controllers
{
    public class VatPercentageController : Controller
    {
        private readonly Data _context;

        public VatPercentageController(Data context)
        {
            _context = context;
        }

        // GET: VatPercentage
        public async Task<IActionResult> Index()
        {
            return View(await _context.VatPercentages.ToListAsync());
        }

        // GET: VatPercentage/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var vatPercentage = await _context.VatPercentages
                .FirstOrDefaultAsync(m => m.Id == id);
            if (vatPercentage == null)
            {
                return NotFound();
            }

            return View(vatPercentage);
        }

        // GET: VatPercentage/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: VatPercentage/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Percentage")] VatPercentage vatPercentage)
        {
            if (ModelState.IsValid)
            {
                _context.Add(vatPercentage);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(vatPercentage);
        }

        // GET: VatPercentage/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var vatPercentage = await _context.VatPercentages.FindAsync(id);
            if (vatPercentage == null)
            {
                return NotFound();
            }
            return View(vatPercentage);
        }

        // POST: VatPercentage/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Percentage")] VatPercentage vatPercentage)
        {
            if (id != vatPercentage.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(vatPercentage);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!VatPercentageExists(vatPercentage.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(vatPercentage);
        }

        // GET: VatPercentage/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var vatPercentage = await _context.VatPercentages
                .FirstOrDefaultAsync(m => m.Id == id);
            if (vatPercentage == null)
            {
                return NotFound();
            }

            return View(vatPercentage);
        }

        // POST: VatPercentage/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var vatPercentage = await _context.VatPercentages.FindAsync(id);
            _context.VatPercentages.Remove(vatPercentage);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool VatPercentageExists(int id)
        {
            return _context.VatPercentages.Any(e => e.Id == id);
        }
    }
}
