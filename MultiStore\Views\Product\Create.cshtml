﻿@model MultiStore.Persistance.Product

@{
    ViewData["Title"] = "Create Product";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Create Product</h1>

<h4>Product</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="ProductTypeId" class="control-label"></label>
                <select asp-for="ProductTypeId" class="form-control" asp-items="ViewBag.ProductTypeId"></select>
            </div>
            <div class="form-group">
                <label asp-for="VatPercentageId" class="control-label"></label>
                <select asp-for="VatPercentageId" class="form-control" asp-items="ViewBag.VatPercentageId"></select>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

@if (ViewData["Message"] != null)
{
    <div class="alert alert-danger">
        @ViewData["Message"]
    </div>
}


<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
