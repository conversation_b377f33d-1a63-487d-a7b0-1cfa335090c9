﻿@model MultiStore.Models.EditFuelsViewModel

@{
    ViewData["Title"] = "Edit Fuel";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Edit Fuel for Store</h1>

<form asp-action="UpdateFuels" method="post">
    <input type="hidden" asp-for="StoreId" />
    <input type="hidden" id="selectedFuelIds" name="selectedFuelIds" />

    <div class="row">
        <div class="col-md-6">
            <h3>Selected Fuels</h3>
            <select multiple class="form-control" id="selectedFuels" size="10">
                @foreach (var Fuel in Model.SelectedFuels)
                {
                    <option value="@Fuel.Id">@Fuel.Name</option>
                }
            </select>
            <button type="button" class="btn btn-primary" id="deselectAll">Deselect All</button>
        </div>
        <div class="col-md-6">
            <h3>Non-Selected Fuels</h3>
            <select multiple class="form-control" id="nonSelectedFuels" size="10">
                @foreach (var Fuel in Model.NonSelectedFuels)
                {
                    <option value="@Fuel.Id">@Fuel.Name</option>
                }
            </select>
            <button type="button" class="btn btn-primary" id="selectAll">Select All</button>
        </div>
    </div>

    <div class="form-group mt-3">
        <input type="submit" value="Save" class="btn btn-primary" />
    </div>
</form>

@section Scripts {
    <script>
        document.getElementById("deselectAll").onclick = function () {
            var selectedFuels = document.getElementById("selectedFuels");
            var nonSelectedFuels = document.getElementById("nonSelectedFuels");
            while (selectedFuels.options.length > 0) {
                nonSelectedFuels.appendChild(selectedFuels.options[0]);
            }
        };

        document.getElementById("selectAll").onclick = function () {
            var selectedFuels = document.getElementById("selectedFuels");
            var nonSelectedFuels = document.getElementById("nonSelectedFuels");
            while (nonSelectedFuels.options.length > 0) {
                selectedFuels.appendChild(nonSelectedFuels.options[0]);
            }
        };

        document.getElementById("selectedFuels").ondblclick = function () {
            var selectedOptions = this.selectedOptions;
            var nonSelectedFuels = document.getElementById("nonSelectedFuels");
            for (var i = 0; i < selectedOptions.length; i++) {
                nonSelectedFuels.appendChild(selectedOptions[i]);
            }
        };

        document.getElementById("nonSelectedFuels").ondblclick = function () {
            var selectedOptions = this.selectedOptions;
            var selectedFuels = document.getElementById("selectedFuels");
            for (var i = 0; i < selectedOptions.length; i++) {
                selectedFuels.appendChild(selectedOptions[i]);
            }
        };

        document.querySelector("form").onsubmit = function () {
            var selectedFuelIds = document.getElementById("selectedFuelIds");
            var selectedFuels = document.getElementById("selectedFuels");

            var selectedIds = [];
            for (var i = 0; i < selectedFuels.options.length; i++) {
                selectedIds.push(selectedFuels.options[i].value);
            }

            selectedFuelIds.value = selectedIds.join(",");
        };
    </script>
}
