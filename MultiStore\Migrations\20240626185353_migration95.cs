﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MultiStore.Migrations
{
    public partial class migration95 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Activation",
                table: "ScratchCards");

            migrationBuilder.AddColumn<int>(
                name: "Activation",
                table: "StoreScratchCards",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Activation",
                table: "StoreScratchCards");

            migrationBuilder.AddColumn<int>(
                name: "Activation",
                table: "ScratchCards",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }
    }
}
