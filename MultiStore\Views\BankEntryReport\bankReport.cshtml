﻿@model MultiStore.Models.BankReportViewModel

@{
    ViewData["Title"] = "Bank Entry Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed
                                                     // Update the layout path as needed

}

<h2>Bank Entry Report</h2>

<div>
    <p><strong>Company:</strong> @Model.CompnayName</p>
    <p><strong>Store:</strong> @Model.StoreName</p>
    <p><strong>From:</strong> @Model.StartDate?.ToString("MM/dd/yyyy") <strong>To:</strong> @Model.EndDate?.ToString("MM/dd/yyyy")</p>
</div>

<table class="table table-bordered">
    <thead>
        <tr>
            <th>Date</th>
            <th>Store Name</th>
            <th>Supplier Name</th>
            <th>Amount</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var entry in Model.BankEntries)
        {
            <tr>
                <td>@entry.Date.ToString("MM/dd/yyyy")</td>
                <td>@entry.StoreName</td>
                <td>@entry.SupplierName</td>
                <td>@entry.Amount.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            </tr>
        }
    </tbody>
    <tfoot>
        <tr>
            <td colspan="3" class="text-right"><strong>Total:</strong></td>
            <td>@Model.BankEntries.Sum(e => e.Amount).ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
        </tr>
    </tfoot>
</table>
