﻿using Microsoft.AspNetCore.Mvc;
using MultiStore.Models;
using MultiStore.Persistance;
using System;
using System.Linq;

namespace MultiStore.Controllers
{
    public class ClosingBalance2Controller : Controller
    {
        private readonly Data _context;

        public ClosingBalance2Controller(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            ViewData["Companies"] = _context.Companies.ToList();
            ViewData["Stores"] = _context.Stores.ToList();
            return View();

        }

        public IActionResult Submit(DateTime? startDate, DateTime? endDate, DateTime? singleDate, string period, int selectedCompanyId, int selectedStoreId, int page = 1)
        {


            DateTime _startDate = DateTime.MinValue;
            DateTime _endDate = DateTime.MinValue;

            if (startDate != null && endDate != null)
            {
                if (startDate > endDate)
                {
                    return View("Index");
                }
                else
                {
                    _startDate = (DateTime)startDate;
                    _endDate = (DateTime)endDate;
                }
            }
            else if (singleDate != null)
            {


                //calculate start and end date depening on single date 
                //and period
                switch (period.ToLower())
                {
                    case "week":
                        _startDate = GetFirstDateOfWeek(singleDate.Value);
                        _endDate = _startDate.AddDays(6);
                        break;
                    case "month":
                        _startDate = new DateTime(singleDate.Value.Year, singleDate.Value.Month, 1);
                        _endDate = _startDate.AddMonths(1).AddDays(-1);
                        break;
                    case "3months":
                        _startDate = singleDate.Value.AddMonths(-2);
                        _endDate = singleDate.Value;
                        break;
                    case "year":
                        _startDate = new DateTime(singleDate.Value.Year, 1, 1);
                        _endDate = new DateTime(singleDate.Value.Year, 12, 31);
                        break;
                    default:
                        return View("Index");
                }

            }


            const int itemsPerPage = 15;

            // Set default filter date range to current month if not provided

            List<StoreDay>? query = new List<StoreDay>();

            if (selectedStoreId != null && selectedStoreId != 0)
            {
                query = _context.StoreDays
                                .Where(s => s.Day >= _startDate && s.Day <= _endDate && s.StoreId == selectedStoreId)
                                .OrderByDescending(s => s.Day)
                                .ToList();
            }
            else if(selectedCompanyId != null && selectedCompanyId != 0)
            {
                var stores = _context.Stores.Where(s => s.CompanyId == selectedCompanyId).ToList();

                query.AddRange(_context.StoreDays
                                .Where(s => s.Day >= _startDate && s.Day <= _endDate && s.StoreId == selectedStoreId)
                                .OrderByDescending(s => s.Day)
                                .ToList());
            }
            else
            {
                query = _context.StoreDays
                               .Where(s => s.Day >= _startDate && s.Day <= _endDate)
                               .OrderByDescending(s => s.Day)
                               .ToList();
            }

            var pagedItems = query
                .Skip((page - 1) * itemsPerPage)
                .Take(itemsPerPage)
                .Select(item => new ClosingBalanceViewModel
                {
                    Date = item.Day,
                    StoreName = _context.Stores.FirstOrDefault(s => s.Id == item.StoreId)?.Name,
                    PaidInRemark = item.PaidInRemark,
                    AtmRemark = item.AtmRemark,
                    BankDepositRemark = item.BankDepositRemark
                })
                .ToList();

            var totalItems = query.Count();
            var totalPages = (int)Math.Ceiling((double)totalItems / itemsPerPage);

            var viewModel = new PagedList<ClosingBalanceViewModel>
            {
                Items = pagedItems,
                CurrentPage = page,
                TotalPages = totalPages
            };

            ViewData["FromDate"] = _startDate;
            ViewData["ToDate"] = _endDate;
            ViewData["CurrentDate"] = DateTime.Now; // Add current date to ViewData
            ViewData["SingleDate"] = singleDate;
            ViewData["Period"] = period;
            ViewData["SelectedCompanyId"] = selectedCompanyId;
            ViewData["SelectedStoreId"] = selectedStoreId;


            return View("ClosingBalance2", viewModel);

        }


        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }

        public class ClosingBalance2SelectViewModel
        {
            public DateTime? StartDate { get; set; }
            public DateTime? EndDate { get; set; }
            public DateTime? SingleDate { get; set; }
            public string Period { get; set; }

            public List<Store> Stores { get; set; }

            public List<Company> Companies { get; set; }

            public int SelectedStoreId { get; set; }
            public int SelectedCompanyId { get; set; }

        }

        public class ClosingBalanceViewModel
        {
            public DateTime Date { get; set; }
            public string StoreName { get; set; }
            public decimal ClosingBalance { get; set; }
            public int NoOfDays { get; set; } // New property for number of days
            public string? PaidInRemark { get; set; }
            public string? AtmRemark { get; set; }
            public string? BankDepositRemark { get; set; }
        }

        public class PagedList<T>
        {
            public List<T> Items { get; set; }
            public int CurrentPage { get; set; }
            public int TotalPages { get; set; }
        }
    }
}
