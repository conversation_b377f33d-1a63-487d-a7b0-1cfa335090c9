﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace MultiStore
{
    public class BackOfficeAuthorizationFilter : IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            // Check if the user is logged in
            if (context.HttpContext.Session.GetInt32("UserId") == null)
            {
                context.Result = new RedirectToActionResult("Login", "Home", null);
                return;
            }

            if (context.HttpContext.Session.GetString("UserType") != "Admin")
            {


                // Check if the user is a StoreManager
                if (context.HttpContext.Session.GetString("UserType") != "BackOffice")
                {
                    context.Result = new RedirectToActionResult("Restricted", "Home", null);
                    return;
                }


            }

        }
    }
}
