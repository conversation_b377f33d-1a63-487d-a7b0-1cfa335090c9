﻿using MultiStore.Persistance;
using System.ComponentModel.DataAnnotations;

namespace MultiStore.Models
{
    public class DaysService
    {
        public int Id { get; set; }
        public Service Service { get; set; }

        public int Opening { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Closing must be a non-negative number.")]
        public int Closing { get; set; }
    }
}
