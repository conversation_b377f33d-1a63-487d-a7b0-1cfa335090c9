﻿namespace MultiStore
{
    public static class RouteValueDictionaryExtensions
    {
        public static object Merge(this object source, object additionalValues)
        {
            var routeValues = new RouteValueDictionary(source);
            foreach (var property in additionalValues.GetType().GetProperties())
            {
                routeValues[property.Name] = property.GetValue(additionalValues);
            }
            return routeValues;
        }
    }
}
