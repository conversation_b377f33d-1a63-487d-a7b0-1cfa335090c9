﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class AtmReportController : Controller
    {

        private readonly Data _context;

        public AtmReportController(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            SaleReportSelectViewModal reportViewModel = new SaleReportSelectViewModal();
            reportViewModel.Stores = _context.Stores.ToList();
            reportViewModel.Companies = _context.Companies.ToList();

            return View(reportViewModel);
        }


        [HttpPost]
        public IActionResult Submit(SaleReportSelectViewModal model)
        {
            try
            {
                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;

                if (model.StartDate != null && model.EndDate != null)
                {
                    if (model.StartDate > model.EndDate)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDate;
                        endDate = (DateTime)model.EndDate;
                    }
                }
                else if (model.SingleDate != null)
                {


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {
                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDate.Value.AddMonths(-2);
                            endDate = model.SingleDate.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }

                }

                List<AtmModel>? atmModels = new();

                DateTime storesStartDay = _context.StoreDays.First().Day.Date;


                if (model.SelectedStoreId != 0)
                {

                    var storeDays = _context.StoreDays
                        .Where(s => s.StoreId == model.SelectedStoreId)
                        .ToList();


                    decimal previousDaysClose = 0;

                    foreach (var storeDay in storeDays)
                    {
                        AtmModel atmModel = new AtmModel();


                        if (storeDay.Day.Date == storesStartDay)
                        {
                            atmModel.StoreName = _context.Stores.First(s => s.Id == model.SelectedStoreId).Name;
                            atmModel.open = _context.Stores.First(s => s.Id == model.SelectedStoreId).AtmInitialOpenValue;
                            atmModel.deposit = storeDay.Atm;
                            atmModel.withdraw = storeDay.AtmWithdrawl;
                            previousDaysClose = atmModel.close = atmModel.open + atmModel.deposit - atmModel.withdraw;
                        }
                        else
                        {
                            atmModel.StoreName = _context.Stores.First(s => s.Id == model.SelectedStoreId).Name;
                            atmModel.open = previousDaysClose;
                            atmModel.deposit = storeDay.Atm;
                            atmModel.withdraw = storeDay.AtmWithdrawl;
                            previousDaysClose = atmModel.close = atmModel.open + atmModel.deposit - atmModel.withdraw;
                        }

                        if (storeDay.Day.Date >= startDate.Date && storeDay.Day.Date <= endDate.Date)
                            atmModels.Add(atmModel);


                    }



                }
                else if (model.SelectedCompanyId != 0)
                {


                    var stores = _context.Stores.Where(s => s.CompanyId == model.SelectedCompanyId)
                       .ToList();


                    foreach (var store in stores)
                    {
                        var storeDays = _context.StoreDays
                       .Where(s => s.StoreId == store.Id)
                       .ToList();


                        decimal previousDaysClose = 0;

                        foreach (var storeDay in storeDays)
                        {
                            AtmModel atmModel = new AtmModel();


                            if (storeDay.Day.Date == storesStartDay)
                            {
                                atmModel.StoreName = _context.Stores.First(s => s.Id == store.Id).Name;
                                atmModel.open = _context.Stores.First(s => s.Id == store.Id).AtmInitialOpenValue;
                                atmModel.deposit = storeDay.Atm;
                                atmModel.withdraw = storeDay.AtmWithdrawl;
                                previousDaysClose = atmModel.close = atmModel.open + atmModel.deposit - atmModel.withdraw;
                            }
                            else
                            {
                                atmModel.StoreName = _context.Stores.First(s => s.Id == store.Id).Name;
                                atmModel.open = previousDaysClose;
                                atmModel.deposit = storeDay.Atm;
                                atmModel.withdraw = storeDay.AtmWithdrawl;
                                previousDaysClose = atmModel.close = atmModel.open + atmModel.deposit - atmModel.withdraw;
                            }

                            if (storeDay.Day.Date >= startDate.Date && storeDay.Day.Date <= endDate.Date)
                                atmModels.Add(atmModel);


                        }

                    }



                }
                else //both compnay id and store id =0 
                {
                    var stores = _context.Stores.ToList();


                    foreach (var store in stores)
                    {
                        var storeDays = _context.StoreDays
                        .Where(s => s.StoreId == store.Id)
                        .ToList();


                        decimal previousDaysClose = 0;

                        foreach (var storeDay in storeDays)
                        {
                            AtmModel atmModel = new AtmModel();


                            if (storeDay.Day.Date == storesStartDay)
                            {
                                atmModel.StoreName = _context.Stores.First(s => s.Id == store.Id).Name;
                                atmModel.open = _context.Stores.First(s => s.Id == store.Id).AtmInitialOpenValue;
                                atmModel.deposit = storeDay.Atm;
                                atmModel.withdraw = storeDay.AtmWithdrawl;
                                previousDaysClose = atmModel.close = atmModel.open + atmModel.deposit - atmModel.withdraw;
                            }
                            else
                            {
                                atmModel.StoreName = _context.Stores.First(s => s.Id == store.Id).Name;
                                atmModel.open = previousDaysClose;
                                atmModel.deposit = storeDay.Atm;
                                atmModel.withdraw = storeDay.AtmWithdrawl;
                                previousDaysClose = atmModel.close = atmModel.open + atmModel.deposit - atmModel.withdraw;
                            }

                            if (storeDay.Day.Date >= startDate.Date && storeDay.Day.Date <= endDate.Date)
                                atmModels.Add(atmModel);


                        }

                    }
                }



                ViewData["StartDate"] = startDate.ToShortDateString();
                ViewData["EndDate"] = endDate.ToShortDateString();

                ViewData["StoreName"] = (model.SelectedStoreId != 0) ? _context.Stores
                 .Where(s => s.Id == model.SelectedStoreId)
                 .FirstOrDefault().Name : "All Stores";

                ViewData["CompanyName"] = (model.SelectedCompanyId != 0) ? _context.Companies
                    .FirstOrDefault(c => c.Id == model.SelectedCompanyId).Name : "All Companies";

                return View("AtmReport", atmModels);
            }
            catch (Exception ex)
            {

                return View("Index");
            }
        }



        public class AtmModel
        {
            public string StoreName { get; set; }
            public DateTime Date { get; set; }
            public decimal open { get; set; }
            public decimal deposit { get; set; }
            public decimal withdraw { get; set; }
            public decimal close { get; set; }
        }


        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }
    }
}
