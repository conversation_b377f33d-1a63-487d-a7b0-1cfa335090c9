﻿@model List<MultiStore.Controllers.FuelLetersReportController.FuelLitersViewModel>

@{
    ViewData["Title"] = "Fuel Liters";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

    decimal totalLiters = Model.Sum(item => item.Liters);
}

<h2>Fuel Liters Report</h2>


<div class="container mt-4">    
    <h2 class="mb-3">Fuel Liters List</h2>
    <table class="table table-bordered table-striped">
        <thead class="thead-dark">
            <tr>
                <th>Store Name</th>
                <th>Company Name</th>
                <th>Fuel Name</th>
                <th>Liters</th>
                <th>Date</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                <tr>
                    <td>@item.StoreName</td>
                    <td>@item.CompanyName</td>
                    <td>@item.FuelName</td>
                    <td>@item.Liters</td>
                    <td>@item.Date.ToString("yyyy-MM-dd")</td>
                </tr>
            }
        </tbody>
        <tfoot>
            <tr>
                <th colspan="3" class="text-right">Total Liters:</th>
                <th>@totalLiters</th>
                <th></th>
            </tr>
        </tfoot>
    </table>
</div>
