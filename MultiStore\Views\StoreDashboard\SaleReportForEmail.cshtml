﻿@model MultiStore.Models.SaleReportDisplayViewModal

@{
    ViewData["Title"] = "Stores Total Sales Report";
    Layout = "~/Views/Shared/_EmailLayout.cshtml"; // Update the layout path as needed
    var cultureInfo = new System.Globalization.CultureInfo("en-GB");
}

<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }

        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border: 1px solid #ddd;
        }

        h2 {
            color: #007bff;
            text-align: center;
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table, th, td {
            border: 1px solid #ddd;
        }

        th, td {
            padding: 10px;
            text-align: left;
        }

        th {
            background-color: #007bff;
            color: white;
        }

        tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        .total-row td {
            font-weight: bold;
            background-color: #e9ecef;
        }

        .highlight {
            background-color: #dff0d8;
        }

        .footer {
            text-align: center;
            font-size: 12px;
            color: #777;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Stores Total Sales Report</h2>

        <div>
            <p><strong>Company Name:</strong> @Model.CompnayName</p>
            <p><strong>Start Date:</strong> @Model.StartDate?.ToString("yyyy-MM-dd")</p>
            <p><strong>End Date:</strong> @Model.EndDate?.ToString("yyyy-MM-dd")</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Store Name</th>
                    <th>Category</th>
                    <th>Sale Amount</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var storeGroup in Model.salesDtos.GroupBy(s => s.StoreName))
                {
                    var storeName = storeGroup.Key;
                    var storeSales = storeGroup.ToList();

                    // Loop through product categories for each store
                    @foreach (var productCat in storeSales.First().ProductCatagoriesSale.Keys)
                    {
                        <tr class="@(storeSales.Sum(s => s.DaysSale) > 5000 ? "highlight" : "")">
                            <td>@storeName</td>
                            <td>@productCat</td>
                            <td>@storeSales.Sum(s => s.ProductCatagoriesSale[productCat]).ToString("C", cultureInfo)</td>
                        </tr>
                    }

                    // Loop through service categories for each store
                    @foreach (var serviceCat in storeSales.First().ServiceCatagoriesSale.Keys)
                    {
                        <tr class="@(storeSales.Sum(s => s.DaysSale) > 5000 ? "highlight" : "")">
                            <td>@storeName</td>
                            <td>@serviceCat</td>
                            <td>@storeSales.Sum(s => s.ServiceCatagoriesSale[serviceCat]).ToString("C", cultureInfo)</td>
                        </tr>
                    }

                    <tr class="@(storeSales.Sum(s => s.DaysSale) > 5000 ? "highlight" : "")">
                        <td>@storeName</td>
                        <td>Fuel Sale</td>
                        <td>@storeSales.Sum(s => s.FuelSale).ToString("C", cultureInfo)</td>
                    </tr>
                    <tr class="@(storeSales.Sum(s => s.DaysSale) > 5000 ? "highlight" : "")">
                        <td>@storeName</td>
                        <td>Scratch Cards Sale</td>
                        <td>@storeSales.Sum(s => s.ScratchCardsSale).ToString("C", cultureInfo)</td>
                    </tr>
                    <tr class="@(storeSales.Sum(s => s.DaysSale) > 5000 ? "highlight" : "")">
                        <td>@storeName</td>
                        <td>General Payout</td>
                        <td>@storeSales.Sum(s => s.GeneralPayout).ToString("C", cultureInfo)</td>
                    </tr>
                    <tr class="@(storeSales.Sum(s => s.DaysSale) > 5000 ? "highlight" : "")">
                        <td>@storeName</td>
                        <td>Specific Payout</td>
                        <td>@storeSales.Sum(s => s.SpecificPayout).ToString("C", cultureInfo)</td>
                    </tr>
                    <tr class="@(storeSales.Sum(s => s.DaysSale) > 5000 ? "highlight" : "")">
                        <td>@storeName</td>
                        <td>Day's Sale</td>
                        <td>@storeSales.Sum(s => s.DaysSale).ToString("C", cultureInfo)</td>
                    </tr>
                    <tr class="@(storeSales.Sum(s => s.DaysSale) > 5000 ? "highlight" : "")">
                        <td>@storeName</td>
                        <td>Day's Spendings</td>
                        <td>@storeSales.Sum(s => s.DaysSpendings).ToString("C", cultureInfo)</td>
                    </tr>
                }
            </tbody>
        </table>

        <div class="footer">
            <p>This is an automated email generated by the Samy Groups system. Please do not reply to this email. For any inquiries or support, contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
            <p>© 2024 Samy Groups. All rights reserved.</p>
            <p>Samy Groups, 73-75 Corporation Road, Middlesbrough, TS1 1LY, United Kingdom</p>
        </div>
    </div>
</body>
</html>
