﻿@model IEnumerable<MultiStore.Persistance.BackOfficeCardPaymentEntry>

@{
    ViewData["Title"] = "BackOffice Card Payment Entries";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2>BackOffice Card Payment Entries</h2>


<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Store.Name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.CardType.Name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Amount)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Date)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.Store.Name)
                </td>
                <th>
                    @Html.DisplayFor(model => item.CardType.Name)
                </th>
                <th>
                    @Html.DisplayFor(model => item.Amount)
                </th>
                <td>
                    @Html.DisplayFor(modelItem => item.Date)
                </td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@item.Id">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
