﻿@model List<MultiStore.Models.WeeklyReportViewModel>

@{
    ViewData["Title"] = "Weekly Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml";

    // Group by Company Name and calculate totals for Cleared Cash
    var totalsByCompany = Model
        .GroupBy(report => report.CompanyName)
        .Select(group => new
        {
            CompanyName = group.Key,
            TotalClearedCash = group.Sum(report => report.ClearedCash)
        }).ToList();
}

<h2>Weekly Report</h2>

<div>
    <p><strong>Company:</strong> @ViewData["CompanyName"]</p>
    <p><strong>From:</strong> @ViewData["StartDate"] <strong>To:</strong> @ViewData["EndDate"]</p>

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Company Name</th>
                <th>Store Name</th>
                <th>Total Sales</th>
                <th>Shop Sales</th>
                <th>Total Spendings</th>
                <th>Paid Ins</th>
                <th>Atm</th>
                <th>Bank Deposit</th>
                <th>Store Transfer</th>
                <th>Cleared Deposit</th>
                <th>Card Payments</th>
                <th>Bank Entry</th>
                <th>Variance</th>
                <th>Cash In Hand</th>
                <th>Cleared Cash</th>
                <th>Weekly Percentage</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var report in Model)
            {
                <tr>
                    <td>@report.CompanyName</td>
                    <td>@report.StoreName</td>
                    <td>@report.TotalSales</td>
                    <td>@report.ShopSales</td>
                    <td>@report.TotalSpendings</td>
                    <td>@report.PaidIns</td>
                    <td>@report.Atm</td>
                    <td>@report.BankDeposit</td>
                    <td>@report.StoreTransfer</td>
                    <td>@report.ClearedDeposit</td>
                    <td>@report.CardPayments</td>
                    <td>@report.BankEntry</td>
                    <td>@report.Variance</td>
                    <td>@report.CashInHand</td>
                    <td>@report.ClearedCash</td>
                    <td>@report.WeeklyPercentage.ToString("#.##")%</td>
                    <td>@report.YearlyClearedCash</td>
                    <td>@report.YearlyPercentage.ToString("#.##")%</td>


                </tr>
            }
        </tbody>
    </table>

    <!-- New row for total Cleared Cash for each company -->
    <h4>Totals by Company</h4>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Company Name</th>
                <th>Total Cleared Cash</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var total in totalsByCompany)
            {
                <tr>
                    <td>@total.CompanyName</td>
                    <td>@total.TotalClearedCash</td>
                </tr>
            }
        </tbody>
    </table>
</div>
