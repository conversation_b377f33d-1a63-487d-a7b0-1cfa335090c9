﻿using System.Runtime.CompilerServices;

namespace MultiStore.Persistance
{
    public class StorePaymentMethod
    {
        public int Id { get; set; }
        public int StoreId { get; set; }

        public Store Store { get; set; }
        public PaymentCard? PaymentCard { get; set; }

        public int? PaymentCardId { get; set; }

        public decimal Amount { get; set; }

        public DateTime Date { get; set; }
    }
}
