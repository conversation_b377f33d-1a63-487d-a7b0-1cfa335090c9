﻿@model MultiStore.Models.DaysProductSaleViewModal

@{
    ViewData["Title"] = "Products";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Products</h1>

<form asp-action="CreateProductsSales" method="post">
    <input type="hidden" asp-for="StoreId" />

    @for (int i = 0; i < Model.ProductTypes.Count; i++)
    {
        var productType = Model.ProductTypes[i];
        <h2>@productType.Name</h2>
        <table class="table" id="<EMAIL>">
            <thead>
                <tr>
                    <th>Product Name</th>
                    <th>Sales</th>
                </tr>
            </thead>
            <tbody>
                @for (int j = 0; j < Model.DaysProducts.Count; j++)
                {
                    var daysProduct = Model.DaysProducts[j];
                    if (daysProduct.Product.ProductTypeId == productType.Id)
                    {
                        <tr>
                            <td>@daysProduct.Product.Name</td>
                            <td>
                                <input type="hidden" name="DaysProducts[@j].Id" value="@daysProduct.Id" />
                                <input type="hidden" name="DaysProducts[@j].Product.Id" value="@daysProduct.Product.Id" />
                                <input name="DaysProducts[@j].Sales" value="@daysProduct.Sales" class="form-control sales-input" data-product-type-id="@productType.Id" />
                                <span asp-validation-for="DaysProducts[@j].Sales" class="text-danger"></span>
                            </td>
                        </tr>
                    }
                }
                <tr>
                    <td><strong>Total Sales</strong></td>
                    <td><strong id="<EMAIL>">0.00</strong></td>
                </tr>
            </tbody>
        </table>
    }

    <div class="form-group">
        <input type="submit" value="Submit" class="btn btn-primary" />
    </div>
    <input type="button" value="Cancel" class="btn btn-dark" onclick="window.history.back()" />

</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            console.log("DOMContentLoaded");

            function updateSalesSums() {
                @foreach (var productType in Model.ProductTypes)
                {
                    <text>
                    {
                        let sum = 0;
                        document.querySelectorAll(`input[data-product-type-id='@productType.Id']`).forEach(input => {
                            const value = parseFloat(input.value) || 0;
                            sum += value;
                        });
                        document.getElementById(`<EMAIL>`).innerText = sum.toFixed(2);
                    }
                    </text>
                }
            }

            document.querySelectorAll(".sales-input").forEach(input => {
                input.addEventListener("input", function() {
                    console.log("Input value changed");
                    updateSalesSums();
                });
            });

            updateSalesSums(); // Initial calculation on page load
        });
    </script>
}