﻿@model IEnumerable<MultiStore.Controllers.CardPaymentReportController.CardPayment>

@{
    ViewData["Title"] = "Card Payment Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed
                                                     // Update the layout path as needed
    var Totalstore = Model.Sum(item => item.Store);
    var TotalAccount = Model.Sum(item => item.Account);

}

<h1>Card Payment Report</h1>

<div>
    <p><strong>Store:</strong> @ViewData["StoreName"]</p>

    <p><strong>Company:</strong> @ViewData["CompanyName"]</p>
    <p><strong>From:</strong> @ViewData["StartDate"] <strong>To:</strong> @ViewData["EndDate"]</p>
</div>


<table class="table">
    <thead>
        <tr>
            <th>Date</th>
            <th>Store Name</th>
            <th>Card Type</th>
            <th>Store</th>
            <th>Account</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item?.Date</td>
                <td>@item?.StoreName</td>
                <td>@item?.CardType</td>
                <td>@item?.Store.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@item?.Account.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            </tr>
        }
        <tr>
            <td colspan="3"><strong>Total</strong></td>
            <td><strong>@Totalstore.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
            <td><strong>@TotalAccount.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>

            <td></td>
        </tr>
    </tbody>
</table>
