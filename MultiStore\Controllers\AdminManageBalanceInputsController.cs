﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using static MultiStore.Controllers.AdminStoreSalesController;

namespace MultiStore.Controllers
{
    public class AdminManageBalanceInputsController : Controller
    {

        private readonly Data _context;
        private readonly IUtilityService _utilityService;


        public AdminManageBalanceInputsController(Data context, IUtilityService utilityService)
        {
            _context = context;
            _utilityService = utilityService;

        }


        public async Task<IActionResult> Index(DateTime startDate, DateTime endDate)
        {
            var storeDays = await _context.StoreDays
                .Include(s => s.Store)
                .Where(s => s.Day.Date >= startDate && s.Day.Date <= endDate)
                .ToListAsync();
            return View(storeDays);
        }

        // GET: AdminManageBalanceInputs/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeDay = await _context.StoreDays.FindAsync(id);
            if (storeDay == null)
            {
                return NotFound();
            }

            return View(storeDay);
        }

        // POST: AdminManageBalanceInputs/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Atm,BankDeposit,PaidIn,AtmRemark,BankDepositRemark,PaidInRemark,AtmWithdrawl")] StoreDay storeDay)
        {
            if (id != storeDay.Id)
            {
                return NotFound();
            }

            // if (ModelState.IsValid)
            // {
            try
            {
                var existingStoreDay = await _context.StoreDays.FindAsync(id);
                if (existingStoreDay == null)
                {
                    return NotFound();
                }

                // Update only the specified properties
                existingStoreDay.Atm = storeDay.Atm;
                existingStoreDay.BankDeposit = storeDay.BankDeposit;
                existingStoreDay.PaidIn = storeDay.PaidIn;
                existingStoreDay.PaidInRemark = storeDay.PaidInRemark;
                existingStoreDay.AtmRemark = storeDay.AtmRemark;
                existingStoreDay.BankDepositRemark = storeDay.BankDepositRemark;
                existingStoreDay.AtmWithdrawl = storeDay.AtmWithdrawl;

                _context.Update(existingStoreDay);
                _utilityService.UpdateStoreDaysToReflectChanges(existingStoreDay.StoreId);

                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StoreDayExists(storeDay.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Index), "AdminStoreSales");

            //   }
            return View(storeDay);
        }

        // GET: AdminManageBalanceInputs/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeDay = await _context.StoreDays
                .Include(s => s.Store)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (storeDay == null)
            {
                return NotFound();
            }

            return View(storeDay);
        }

        // POST: AdminManageBalanceInputs/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var storeDay = await _context.StoreDays.FindAsync(id);
            _context.StoreDays.Remove(storeDay);
            await _context.SaveChangesAsync();
            _utilityService.UpdateStoreDaysToReflectChanges(storeDay.StoreId);
            return RedirectToAction(nameof(Index), "AdminStoreSales");

        }

        private bool StoreDayExists(int id)
        {
            return _context.StoreDays.Any(e => e.Id == id);
        }


    }
}
