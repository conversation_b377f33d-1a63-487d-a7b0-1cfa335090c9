﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class PaymentMethodsReport2Controller : Controller
    {
        private readonly Data _context;

        public PaymentMethodsReport2Controller(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            SaleReportSelectViewModal reportViewModel = new SaleReportSelectViewModal();
            reportViewModel.Stores = _context.Stores.ToList();
            reportViewModel.Companies = _context.Companies.ToList();

            return View(reportViewModel);
        }

        [HttpPost]
        public IActionResult Submit(SaleReportSelectViewModal model)
        {
            try
            {
                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;

                if (model.StartDate != null && model.EndDate != null)
                {
                    if (model.StartDate > model.EndDate)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDate;
                        endDate = (DateTime)model.EndDate;
                    }
                }
                else if (model.SingleDate != null)
                {


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {
                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDate.Value.AddMonths(-2);
                            endDate = model.SingleDate.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }

                }

                List<StorePaymentMethod>? storePaymentMethods = new();



                if (model.SelectedCompanyId != 0)
                {


                    var stores1 = _context.Stores.Where(s => s.CompanyId == model.SelectedCompanyId)
                       .ToList();


                    foreach (var item in stores1)
                    {
                        storePaymentMethods.AddRange(_context.StorePaymentMethods
                           .Include(s => s.Store)
                         .Include(s => s.PaymentCard)
                        .Where(p => p.StoreId == item.Id && p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                        .ToList());
                    }

                }
                else //both compnay id and store id =0 
                {
                    storePaymentMethods = _context.StorePaymentMethods
                        .Include(s => s.Store)
                        .Include(s => s.PaymentCard)
                     .Where(p => p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                     .ToList();
                }



                var groupedSales = storePaymentMethods
.Select(s => new
{
    CardName = s.PaymentCard.Name,
    StoreName = s.Store.Name,
    s.Amount
})
.GroupBy(s => new { s.CardName, s.StoreName })
.Select(g => new
{
    g.Key.CardName,
    g.Key.StoreName,
    TotalSales = g.Sum(s => s.Amount)
})
.ToList();





                var cards = storePaymentMethods.Select(s => s.PaymentCard.Name).Distinct().ToList();
                var stores = storePaymentMethods.Select(s => s.Store.Name).Distinct().ToList();

                var viewModel = cards.Select(cardName => new StorePaymentMethodReportViewModel
                {
                    CardName = cardName,
                    StorePayments = stores.ToDictionary(
                        storeName => storeName,
                        storeName => groupedSales
                            .Where(g => g.CardName == cardName && g.StoreName == storeName)
                            .Select(g => g.TotalSales)
                            .FirstOrDefault()
                    ),
                    TotalSales = groupedSales
                        .Where(g => g.CardName == cardName)
                        .Sum(g => g.TotalSales)
                }).ToList();


                ViewData["StartDate"] = startDate.ToShortDateString();
                ViewData["EndDate"] = endDate.ToShortDateString();

                ViewData["StoreName"] = (model.SelectedStoreId != 0) ? _context.Stores
                 .Where(s => s.Id == model.SelectedStoreId)
                 .FirstOrDefault().Name : "All Stores";

                ViewData["CompanyName"] = (model.SelectedCompanyId != 0) ? _context.Companies
                    .FirstOrDefault(c => c.Id == model.SelectedCompanyId).Name : "All Companies";


                var payments = _context.StorePaymentMethods
                .Include(spm => spm.Store)
                .Include(spm => spm.PaymentCard)
                .ToList();

                ViewData["CardPayments"] = payments.ToList();

                return View("StorePaymentMethodsSummary", viewModel);
            }
            catch (Exception ex)
            {

                return View("Index");
            }
        }

        public class StorePaymentMethodReportViewModel
        {
            public string CardName { get; set; }
            public Dictionary<string, decimal> StorePayments { get; set; }
            public decimal TotalSales { get; set; }
        }

        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }
    }
}
