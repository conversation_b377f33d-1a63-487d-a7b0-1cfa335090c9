﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using MultiStore.Models;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class AdminSettingsController : Controller
    {

        private readonly Data _context;


        public AdminSettingsController(Data context)
        {
            _context = context;
        }


        public IActionResult Index()
        {
            AdminSettingsViewModel adminSettingsViewModel = new AdminSettingsViewModel();
            adminSettingsViewModel.StoreServices = _context.StoreServices
                                                    .Include(s => s.Store)
                                                    .Include(s => s.Service)
                                                    .ToList();

            adminSettingsViewModel.StoreScratchCards = _context.StoreScratchCards
                                                     .Include(s => s.Store)
                                                     .Include(s => s.ScratchCard)
                                                     .ToList();

            adminSettingsViewModel.CurrentStartDate = _context.StoreDays.FirstOrDefault()?.Day.ToString("yyyy-MM-dd") ?? "Not Set";


            return View(adminSettingsViewModel);
        }


        public IActionResult FormSumbit(AdminSettingsViewModel adminSettingsViewModel, string action)
        {

            //adminSettingsViewModel = new AdminSettingsViewModel()
            //{
            //    CurrentStartDate = DateTime.UtcNow.ToString(),
            //    StartDate = new DateTime(2024, 10, 20),
            //    StoreScratchCards = _context.StoreScratchCards.ToList(),
            //    StoreServices = _context.StoreServices.ToList()
            //};

            //return ResetStartDate(adminSettingsViewModel);

            if (action == "Reset")
            {
                return ResetStartDate(adminSettingsViewModel);
            }
            else
            {
                return UpdateOpeningValues(adminSettingsViewModel);
            }
        }



        public IActionResult ResetStartDate(AdminSettingsViewModel adminSettingsViewModel)
        {
            //delete existing data
            _context.BankPayments.RemoveRange(_context.BankPayments);//back office
            _context.CommisionEntries.RemoveRange(_context.CommisionEntries);//back office
            _context.CompanyPurchaseEntries.RemoveRange(_context.CompanyPurchaseEntries);//back office
            _context.PurchaseEntries.RemoveRange(_context.PurchaseEntries);//back office
            _context.StoreDays.RemoveRange(_context.StoreDays.ToList());
            _context.StoreFuelSales.RemoveRange(_context.StoreFuelSales);//store
            _context.StoreMgrPayOuts.RemoveRange(_context.StoreMgrPayOuts);//store
            _context.SpecificPayOuts.RemoveRange(_context.SpecificPayOuts);//store
            _context.StoreProductSales.RemoveRange(_context.StoreProductSales);//store
            _context.StoreScratchCardSales.RemoveRange(_context.StoreScratchCardSales);//store
            _context.StoreServiceSales.RemoveRange(_context.StoreServiceSales);//store
            _context.StorePaymentMethods.RemoveRange(_context.StorePaymentMethods);//store
            _context.StorePaidIns.RemoveRange(_context.StorePaidIns.ToList());
            _context.BackOfficeCardPaymentEntries.RemoveRange(_context.BackOfficeCardPaymentEntries.ToList());

            foreach (var storeService in adminSettingsViewModel.StoreServices)
            {
                _context.StoreServices.First(s => s.ServiceId == storeService.ServiceId && s.StoreId == storeService.StoreId)
                    .InitialOpen = storeService.InitialOpen;
            }

            foreach (var scratchCard in adminSettingsViewModel.StoreScratchCards)
            {
                _context.StoreScratchCards.First(s => s.ScratchCardId == scratchCard.ScratchCardId && s.StoreId == scratchCard.StoreId)
                    .InitialOpen = scratchCard.InitialOpen;
            }

            foreach (var store in _context.Stores)
            {
                _context.StoreDays.Add(new StoreDay()
                {
                    StoreId = store.Id,
                    Day = adminSettingsViewModel.StartDate.Date,
                    IsFinalised = false
                });
            }


            _context.SaveChanges();

            return RedirectToAction(nameof(Index));
        }


        public IActionResult UpdateOpeningValues(AdminSettingsViewModel adminSettingsViewModel)
        {
            foreach (var storeService in adminSettingsViewModel.StoreServices)
            {
                _context.StoreServices.First(s => s.ServiceId == storeService.ServiceId && s.StoreId == storeService.StoreId)
                    .InitialOpen = storeService.InitialOpen;
            }

            foreach (var scratchCard in adminSettingsViewModel.StoreScratchCards)
            {
                _context.StoreScratchCards.First(s => s.ScratchCardId == scratchCard.ScratchCardId && s.StoreId == scratchCard.StoreId)
                    .InitialOpen = scratchCard.InitialOpen;
            }

            var SstoreIdsWithStoreDays = _context.StoreDays.Select(s => s.StoreId).Distinct().ToList();

            var newStoreIds = _context.Stores.Select(s => s.Id).ToList().Except(SstoreIdsWithStoreDays).ToList();


            foreach (var storeId in newStoreIds)
            {
                _context.StoreDays.Add(new StoreDay()
                {
                    StoreId = storeId,
                    Day = DateTime.Now.Date.AddDays(-1),
                    IsFinalised = false
                });
            }

            _context.SaveChanges();

            return RedirectToAction(nameof(Index));
        }
    }
}