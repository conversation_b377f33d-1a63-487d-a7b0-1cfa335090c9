﻿namespace MultiStore.Persistance
{
    public class PurchaseEntry
    {
        public int Id { get; set; }

        public Store Store { get; set; }

        public int StoreId { get; set; }

        public string InvoiceNumber { get; set; }


        public Supplier Supplier { get; set; }

        public int SupplierId { get; set; }


        public decimal Gross { get; set; }

        public decimal Vat { get; set; }

        public decimal Net { get; set; }

        //invoice  date
        public DateTime Date { get; set; }

        public DateTime DueDate { get; set; }

    }

    public class PurchaseEntryDTO
    {
        public string StoreName { get; set; }

        public string InvoiceNumber { get; set; }


        public string SupplierName { get; set; }

        public decimal Gross { get; set; }

        public decimal Vat { get; set; }

        public decimal Net { get; set; }

        public DateTime Date { get; set; }

        public DateTime DueDate { get; set; }

    }

    public class CompanyPurchaseEntryDTO
    {
        public string CompnayName { get; set; }

        public string InvoiceNumber { get; set; }


        public string SupplierName { get; set; }

        public decimal Gross { get; set; }

        public decimal Vat { get; set; }

        public decimal Net { get; set; }

        public DateTime Date { get; set; }

    }
}
