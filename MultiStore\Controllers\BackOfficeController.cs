﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;
using MultiStore.Models;
using System.Linq;
using System.Threading.Tasks;

namespace MultiStore.Controllers
{
    [TypeFilter(typeof(AdminAuthorizationFilter))]
    public class BackOfficeController : Controller
    {
        private readonly Data _context;

        public BackOfficeController(Data context)
        {
            _context = context;
        }

        // GET: BackOffice
        public async Task<IActionResult> Index()
        {
            return View(await _context.BackOffices.ToListAsync());
        }

        // GET: BackOffice/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var backOffice = await _context.BackOffices
                .FirstOrDefaultAsync(m => m.Id == id);
            if (backOffice == null)
            {
                return NotFound();
            }

            return View(backOffice);
        }

        // GET: BackOffice/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: BackOffice/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,FirstName,LastName,UserName,Password")] BackOffice backOffice)
        {

            if (_context.BackOffices.Any(b => b.UserName == backOffice.UserName))
            {
                return RedirectToAction(nameof(Index));

            }


            // if (ModelState.IsValid)
            // {
            _context.Add(backOffice);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
            // }
            // return View(backOffice);
        }

        // GET: BackOffice/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var backOffice = await _context.BackOffices.FindAsync(id);
            if (backOffice == null)
            {
                return NotFound();
            }
            return View(backOffice);
        }

        // POST: BackOffice/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,FirstName,LastName,UserName,Password")] BackOffice backOffice)
        {
            if (id != backOffice.Id)
            {
                return NotFound();
            }


            if (!_context.BackOffices.Any(b => b.UserName == backOffice.UserName))
            {
                return RedirectToAction(nameof(Index));

            }

            // if (ModelState.IsValid)
            //  {
            try
            {
                _context.Update(backOffice);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!BackOfficeExists(backOffice.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Index));
            //  }
            //   return View(backOffice);
        }

        // GET: BackOffice/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var backOffice = await _context.BackOffices
                .FirstOrDefaultAsync(m => m.Id == id);
            if (backOffice == null)
            {
                return NotFound();
            }

            return View(backOffice);
        }

        // POST: BackOffice/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var backOffice = await _context.BackOffices.FindAsync(id);
            _context.BackOffices.Remove(backOffice);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool BackOfficeExists(int id)
        {
            return _context.BackOffices.Any(e => e.Id == id);
        }
    }
}
