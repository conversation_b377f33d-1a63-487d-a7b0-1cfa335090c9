﻿@model MultiStore.Models.StoreBalanceViewModel


@{
    ViewData["Title"] = "Purchase Entries";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h2>Store Balance</h2>

<form asp-action="Balance" method="post">

    <input type="hidden" asp-for="StoreId">

    <div class="form-group">
        <label asp-for="CarryForward" class="control-label"></label>
        <input asp-for="CarryForward" class="form-control" readonly />
    </div>

    <div class="form-group">
        <label asp-for="Card" class="control-label"></label>
        <input asp-for="Card" class="form-control" readonly />
    </div>
    <div class="form-group">
        <label asp-for="Cash" class="control-label"></label>
        <input asp-for="Cash" class="form-control" readonly />
    </div>
    <div class="form-group">
        <label asp-for="PaidOut" class="control-label"></label>
        <input asp-for="PaidOut" class="form-control" readonly />
    </div>
    
  


    <div class="row">
        <div class="form-group col-md-3">
            <label asp-for="PaidIn" class="control-label"></label>
            <input asp-for="PaidIn" class="form-control" type="number" readonly />
        </div>

        <!-- New Form Group on the Left -->
        <div class="form-group col-md-6">
            <label asp-for="PaidInRemark" class="control-label">Remarks (Paid In)</label>
            <input asp-for="PaidInRemark" class="form-control" />
        </div>

        @* <div class="form-group col-md-3">
            <label for="store-select">Select Store</label>
            <select id="store-select" class="form-control" asp-items="ViewBag.StoreList">
                <option value="">Select Store</option>
            </select>
        </div> *@
    </div>

   


    <div class="row">
       <!-- Atm Form Group on the Right -->
        <div class="form-group col-md-3">
            <label asp-for="Atm" class="control-label"></label>
            <input asp-for="Atm" class="form-control" type="number" />
        </div>

        <!-- New Form Group on the Left -->
        <div class="form-group col-md-9">
            <label  asp-for="AtmRemark" class="control-label">Remarks (Atm)</label>
            <input  asp-for="AtmRemark" class="form-control" />
        </div>
    </div>


    <div class="row">
        <div class="form-group col-md-3">
            <label asp-for="StoreTransferAmount"></label>
            <input asp-for="StoreTransferAmount" class="form-control" />
            <span asp-validation-for="StoreTransferAmount" class="text-danger"></span>
        </div>

        <div class="form-group col-md-9">
            <label asp-for="TransferredStore.Id">Transferred Store</label>
            <select asp-for="TransferredStore.Id" class="form-control" asp-items="@(new SelectList(Model.Stores, "Id", "Name"))">
                <option value="">-- Select Store --</option>
            </select>
            <span asp-validation-for="TransferredStore.Id" class="text-danger"></span>
        </div>
    </div>

    <div class="row">
        <div class="form-group col-md-3">
            <label asp-for="BankDeposit" class="control-label"></label>
            <input asp-for="BankDeposit" class="form-control" type="number" />
        </div>
        <div class="form-group col-md-9">
            <label asp-for="BankDepositRemark" class="control-label">Remarks (Bank Deposit)</label>
            <input asp-for="BankDepositRemark" *@ class="form-control" />
        </div>
    </div>

    <div class="form-group">
        <label asp-for="CashInHand" class="control-label"></label>
        <input asp-for="CashInHand" class="form-control" />
    </div>


    <div class="form-group">
        <label asp-for="Variance" class="control-label"></label>
        <input asp-for="Variance" class="form-control" />
    </div>


    <div class="form-group">
        <label>Balance</label>
        <input id="Balance" class="form-control" readonly />
    </div>

    <div class="form-group">
        <input type="submit" value="Submit" class="btn btn-primary" />
        <input type="button" value="Cancel" class="btn btn-dark" onclick="window.history.back()" />

    </div>
</form>



@section Scripts {
    <script>
        $(document).ready(function () {
            function calculateBalance() {
                var card = parseFloat($('#Card').val()) || 0;
                var cash = parseFloat($('#Cash').val()) || 0;
                var paidOut = parseFloat($('#PaidOut').val()) || 0;
                var paidIn = parseFloat($('input[name="PaidIn"]').val()) || 0;
                var atm = parseFloat($('input[name="Atm"]').val()) || 0;
                var bankDeposit = parseFloat($('input[name="BankDeposit"]').val()) || 0;
                var carryFwd = parseFloat($('#CarryForward').val()) || 0;
                var variance = parseFloat($('input[name="Variance"]').val()) || 0;
                var storeTransferAmount = parseFloat($('input[name="StoreTransferAmount"]').val()) || 0;




                var balance = carryFwd + cash - paidOut + paidIn - atm - bankDeposit + variance - storeTransferAmount;
                $('#Balance').val(balance.toFixed(2));
            }

            $('input[name="PaidIn"], input[name="Atm"], input[name="BankDeposit"], input[name="Variance"], input[name="StoreTransferAmount"]').on('input', calculateBalance);
            calculateBalance();
        });
    </script>

    <script>
        document.getElementById('store-select').addEventListener('change', function () {
            var selectedStore = this.options[this.selectedIndex].text;
            var paidInRemarkInput = document.getElementById('PaidInRemark');
            paidInRemarkInput.value += " ";

            paidInRemarkInput.value += selectedStore;
        });
    </script>

}
