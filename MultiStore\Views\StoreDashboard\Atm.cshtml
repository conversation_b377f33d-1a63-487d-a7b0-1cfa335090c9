﻿@model MultiStore.Controllers.StoreDashboardController.StoreAtmViewModel

@{
    ViewData["Title"] = "ATM";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<div class="container">
    <h3 class="mb-4">Store ATM Details</h3>
    <form asp-action="Atm" method="post">
        <div class="row mb-3">
            <label for="open" class="col-sm-2 col-form-label">Open</label>
            <div class="col-sm-10">
                <input type="text" class="form-control" id="open" value="@Model.open" readonly />
            </div>
        </div>
        <div class="row mb-3">
            <label for="deposit" class="col-sm-2 col-form-label">Deposit</label>
            <div class="col-sm-10">
                <input type="text" class="form-control" id="deposit" value="@Model.deposit" readonly />
            </div>
        </div>
        <div class="row mb-3">
            <label for="withdraw" class="col-sm-2 col-form-label">Withdraw</label>
            <div class="col-sm-10">
                <input type="number" class="form-control" id="withdraw" name="withdraw" value="@Model.withdraw" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="close" class="col-sm-2 col-form-label">Close</label>
            <div class="col-sm-10">
                <input type="text" class="form-control" id="close" value="@Model.close" readonly />
            </div>
        </div>
        <button type="submit" class="btn btn-primary">Update</button>

        <input type="hidden" asp-for="@Model.StoreId" />

    </form>
</div>

<script>
    document.getElementById('withdraw').addEventListener('input', function () {
        var open = parseFloat(document.getElementById('open').value) || 0;
        var deposit = parseFloat(document.getElementById('deposit').value) || 0;
        var withdraw = parseFloat(document.getElementById('withdraw').value) || 0;
        var close = open + deposit - withdraw;
        document.getElementById('close').value = close.toFixed(2);
    });
</script>

