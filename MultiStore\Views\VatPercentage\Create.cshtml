﻿@model MultiStore.Persistance.VatPercentage

@{
    ViewData["Title"] = "Create Vat Percentage";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Create Vat Percentage</h1>

<h4>Vat Percentage</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div class="form-group">
                <label asp-for="Percentage" class="control-label"></label>
                <input asp-for="Percentage" class="form-control" />
                <span asp-validation-for="Percentage" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
