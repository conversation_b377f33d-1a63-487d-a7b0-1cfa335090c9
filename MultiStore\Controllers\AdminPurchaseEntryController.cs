﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class AdminPurchaseEntryController : Controller
    {
        private readonly Data _context;

        public AdminPurchaseEntryController(Data context)
        {
            _context = context;
        }

        // GET: AdminPurchaseEntry
        public async Task<IActionResult> Index()
        {
            var purchaseEntries = _context.PurchaseEntries
                .Include(p => p.Store)
                .Include(p => p.Supplier);
            return View(await purchaseEntries.ToListAsync());
        }

        // GET: AdminPurchaseEntry/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var purchaseEntry = await _context.PurchaseEntries.FindAsync(id);
            if (purchaseEntry == null)
            {
                return NotFound();
            }

            ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", purchaseEntry.StoreId);
            ViewData["SupplierId"] = new SelectList(_context.Suppliers, "Id", "Name", purchaseEntry.SupplierId);

            return View(purchaseEntry);
        }

        // POST: AdminPurchaseEntry/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,StoreId,InvoiceNumber,SupplierId,Gross,Vat,Net,Date")] PurchaseEntry purchaseEntry)
        {
            if (id != purchaseEntry.Id)
            {
                return NotFound();
            }

          //  if (ModelState.IsValid)
          //  {
                try
                {
                    _context.Update(purchaseEntry);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PurchaseEntryExists(purchaseEntry.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
           // }

          //  ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", purchaseEntry.StoreId);
          //  ViewData["SupplierId"] = new SelectList(_context.Suppliers, "Id", "Name", purchaseEntry.SupplierId);

          //  return View(purchaseEntry);
        }

        // GET: AdminPurchaseEntry/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var purchaseEntry = await _context.PurchaseEntries
                .Include(p => p.Store)
                .Include(p => p.Supplier)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (purchaseEntry == null)
            {
                return NotFound();
            }

            return View(purchaseEntry);
        }

        // POST: AdminPurchaseEntry/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var purchaseEntry = await _context.PurchaseEntries.FindAsync(id);
            _context.PurchaseEntries.Remove(purchaseEntry);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool PurchaseEntryExists(int id)
        {
            return _context.PurchaseEntries.Any(e => e.Id == id);
        }
    }
}
