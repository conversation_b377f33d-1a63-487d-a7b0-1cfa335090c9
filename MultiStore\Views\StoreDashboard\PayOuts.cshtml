﻿@model List<MultiStore.Controllers.StoreDashboardController.ItemViewModel>

@{
    ViewData["Title"] = "Create Items";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

    int storeId = ViewBag.StoreId;
    var items = Model ?? new List<MultiStore.Controllers.StoreDashboardController.ItemViewModel>();
}

<h1>Create Back Office Paid Outs</h1>

<form asp-action="PayOuts" method="post">
    <input type="hidden" name="storeId" value="@storeId" />
    <table class="table" id="itemsTable">
        <thead>
            <tr>
                <th>Name</th>
                <th>Amount</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            @for (int i = 0; i < items.Count; i++)
            {
                <tr>
                    <td><input type="text" name="items[@i].Name" value="@items[i].Name" class="form-control" /></td>
                    <td><input type="number" name="items[@i].Amount" step="0.01" value="@items[i].Amount" class="form-control" /></td>
                    <td><button type="button" class="btn btn-danger remove-row">Remove</button></td>
                </tr>
            }
            @for (int i = items.Count; i < items.Count + 3; i++)
            {
                <tr>
                    <td><input type="text" name="items[@i].Name" class="form-control" /></td>
                    <td><input type="number" name="items[@i].Amount" step="0.01" class="form-control" /></td>
                    <td><button type="button" class="btn btn-danger remove-row">Remove</button></td>
                </tr>
            }
        </tbody>
    </table>
    <button type="button" class="btn btn-primary" id="addRowButton">Add Row</button>
    <button type="submit" class="btn btn-success">Submit</button>
    <input type="button" value="Cancel" class="btn btn-dark" onclick="window.history.back()" />

</form>

@section Scripts {
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            let rowIndex = @items.Count + 3;

            document.getElementById("addRowButton").addEventListener("click", function () {
                const table = document.getElementById("itemsTable").getElementsByTagName("tbody")[0];
                const newRow = document.createElement("tr");

                newRow.innerHTML = `
                            <td><input type="text" name="items[${rowIndex}].Name" class="form-control" /></td>
                            <td><input type="number" name="items[${rowIndex}].Amount" class="form-control" /></td>
                            <td><button type="button" class="btn btn-danger remove-row">Remove</button></td>
                        `;

                table.appendChild(newRow);
                rowIndex++;
            });

            document.getElementById("itemsTable").addEventListener("click", function (event) {
                if (event.target && event.target.matches("button.remove-row")) {
                    const row = event.target.closest("tr");
                    row.remove();
                }
            });
        });
    </script>
}
