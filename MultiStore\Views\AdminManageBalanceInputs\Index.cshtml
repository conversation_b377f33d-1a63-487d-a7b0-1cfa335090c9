﻿@model IEnumerable<MultiStore.Persistance.StoreDay> // Adjust the namespace

@{
    ViewData["Title"] = "Store Balance Inputs";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Store Balance Inputs</h1>



<table class="table">
    <thead>
        <tr>
            <th>Day</th>
            <th>Store</th>
            <th>Atm</th>
            <th>Bank Deposit</th>
            <th>Paid In</th>
            <th>Atm Remark</th>
            <th>Bank Deposit Remark</th>
            <th>PaidIn Remark</th>
            <th>Atm Withdrawl</th>

            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.Day.ToShortDateString()</td>
                <td>@item.Store.Name</td>
                <td>@item.Atm</td>
                <td>@item.BankDeposit</td>
                <td>@item.PaidIn</td>
                <td>@item.AtmRemark</td>
                <td>@item.BankDepositRemark</td>
                <td>@item.PaidInRemark</td>
                <td>@item.AtmWithdrawl</td>
                <td>
                    <a href="@Url.Action("Edit", new { id = item.Id })" class="btn btn-primary">Edit</a>
                    <a href="@Url.Action("Delete", new { id = item.Id })" class="btn btn-danger">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
