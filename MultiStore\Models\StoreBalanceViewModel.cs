﻿using MultiStore.Persistance;
using System.Runtime.CompilerServices;

namespace MultiStore.Models
{
    public class StoreBalanceViewModel
    {
        public int StoreId { get; set; }
        public decimal Card { get; set; }
        public decimal Cash  { get; set; }

        public decimal PaidOut { get; set; }

        public decimal PaidIn { get; set; }

        public decimal Atm { get; set; }

        public decimal BankDeposit { get; set; }

        public decimal CarryForward { get; set; }

        public string? PaidInRemark { get; set; }

        public string? AtmRemark { get; set; }

        public decimal CashInHand { get; set; }

        public decimal Variance { get; set; }

        public string? BankDepositRemark { get; set; }

        public decimal StoreTransferAmount { get; set; }

        public Store? TransferredStore { get; set; }

        public List<Store> Stores { get; set; } // Add this to hold the list of stores

    }
}
