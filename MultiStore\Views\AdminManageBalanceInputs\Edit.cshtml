﻿@model MultiStore.Persistance.StoreDay

@{
    ViewData["Title"] = "Store Balance Inputs Edit";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<form asp-action="Edit">
    <div class="form-group">
        <label asp-for="Atm" class="control-label"></label>
        <input asp-for="Atm" class="form-control" />
        <span asp-validation-for="Atm" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="BankDeposit" class="control-label"></label>
        <input asp-for="BankDeposit" class="form-control" />
        <span asp-validation-for="BankDeposit" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="PaidIn" class="control-label"></label>
        <input asp-for="PaidIn" class="form-control" />
        <span asp-validation-for="PaidIn" class="text-danger"></span>
    </div>


    <!-- New Form Group on the Left -->
    <div class="form-group col-md-9">
        <label asp-for="PaidInRemark" class="control-label">Remarks (Paid In)</label>
        <input asp-for="PaidInRemark" class="form-control"  />
    </div>


    <!-- New Form Group on the Left -->
    <div class="form-group col-md-9">
        <label asp-for="AtmRemark" class="control-label">Remarks (Atm)</label>
        <input asp-for="AtmRemark" class="form-control" />
    </div>

    <div class="form-group col-md-9">
        <label asp-for="BankDepositRemark" class="control-label">Remarks (Bank Deposit)</label>
        <input asp-for="BankDepositRemark" *@ class="form-control" />
    </div>

    <div class="form-group col-md-9">
        <label asp-for="AtmWithdrawl" class="control-label">Atm Withdraw</label>
        <input asp-for="AtmWithdrawl" *@ class="form-control" />
    </div>

    <input type="hidden" asp-for="Id" />
    <div class="form-group">
        <input type="submit" value="Save" class="btn btn-primary" />
    </div>
</form>
