﻿using System.Runtime.CompilerServices;

namespace MultiStore.Persistance
{
    public class Store
    {
        public int Id { get; set; }
        public string Name { get; set; }

        public StoreType StoreType { get; set; }

        public ICollection<StoreProduct> StoreProducts { get; set; }

        public ICollection<StoreService> StoreServices { get; set; }

        public ICollection<StoreFuel> StoreFuels { get; set; }

        public ICollection<StoreScratchCard> StoreScratchCards { get; set; }

        public ICollection<EmailAddress> EmailAddresses { get; set; }


        public Company? Company { get; set; }

        public int? CompanyId { get; set; }

        public bool Active { get; set; }

        public decimal AtmInitialOpenValue { get; set; }

    }

    public enum StoreType
    {
        convience_store,
        store_with_fuel_station
    }
}
