﻿@model MultiStore.Models.StoreProductSalesViewModel

@{
    ViewData["Title"] = "Edit Store Product Sales";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Edit Store Product Sales</h1>

<h4>Store Product Sales</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="StoreProductSales.Id" />
            <div class="form-group">
                <label asp-for="StoreProductSales.StoreId" class="control-label">Store</label>
                <select asp-for="StoreProductSales.StoreId" class="form-control" asp-items="Model.Stores"></select>
                <span asp-validation-for="StoreProductSales.StoreId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="StoreProductSales.ProductId" class="control-label">Product</label>
                <select asp-for="StoreProductSales.ProductId" class="form-control" asp-items="Model.Products"></select>
                <span asp-validation-for="StoreProductSales.ProductId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="StoreProductSales.SaleAmount" class="control-label"></label>
                <input asp-for="StoreProductSales.SaleAmount" class="form-control" />
                <span asp-validation-for="StoreProductSales.SaleAmount" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="StoreProductSales.Day" class="control-label"></label>
                <input asp-for="StoreProductSales.Day" class="form-control" />
                <span asp-validation-for="StoreProductSales.Day" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
