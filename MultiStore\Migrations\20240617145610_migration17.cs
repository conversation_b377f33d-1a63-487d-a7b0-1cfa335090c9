﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MultiStore.Migrations
{
    public partial class migration17 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StoreServices_Services_ProductId",
                table: "StoreServices");

            migrationBuilder.DropForeignKey(
                name: "FK_StoreServices_Stores_StoreServiceId",
                table: "StoreServices");

            migrationBuilder.RenameColumn(
                name: "ProductId",
                table: "StoreServices",
                newName: "ServiceId");

            migrationBuilder.RenameColumn(
                name: "StoreServiceId",
                table: "StoreServices",
                newName: "StoreId");

            migrationBuilder.RenameIndex(
                name: "IX_StoreServices_ProductId",
                table: "StoreServices",
                newName: "IX_StoreServices_ServiceId");

            migrationBuilder.AddForeignKey(
                name: "FK_StoreServices_Services_ServiceId",
                table: "StoreServices",
                column: "ServiceId",
                principalTable: "Services",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_StoreServices_Stores_StoreId",
                table: "StoreServices",
                column: "StoreId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StoreServices_Services_ServiceId",
                table: "StoreServices");

            migrationBuilder.DropForeignKey(
                name: "FK_StoreServices_Stores_StoreId",
                table: "StoreServices");

            migrationBuilder.RenameColumn(
                name: "ServiceId",
                table: "StoreServices",
                newName: "ProductId");

            migrationBuilder.RenameColumn(
                name: "StoreId",
                table: "StoreServices",
                newName: "StoreServiceId");

            migrationBuilder.RenameIndex(
                name: "IX_StoreServices_ServiceId",
                table: "StoreServices",
                newName: "IX_StoreServices_ProductId");

            migrationBuilder.AddForeignKey(
                name: "FK_StoreServices_Services_ProductId",
                table: "StoreServices",
                column: "ProductId",
                principalTable: "Services",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_StoreServices_Stores_StoreServiceId",
                table: "StoreServices",
                column: "StoreServiceId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
