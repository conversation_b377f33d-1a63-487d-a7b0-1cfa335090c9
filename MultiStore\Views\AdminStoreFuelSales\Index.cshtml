﻿@model IEnumerable<MultiStore.Persistance.StoreFuelSales>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Index</h1>

<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Store)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Fuel)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.SaleAmount)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Day)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.Store.Name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Fuel.Name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.SaleAmount)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Day)
                </td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@item.Id">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.getElementById("deleteAllButton").addEventListener("click", function () {
            if (confirm("Are you sure you want to delete all records?")) {
                fetch('@Url.Action("DeleteAll", "AdminStoreFuelSales")', {
                    method: 'POST',
                    headers: {
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    }
                }).then(response => {
                    if (response.ok) {
                        window.location.href = '@Url.Action("Index", "AdminStoreFuelSales")';
                    } else {
                        alert('Failed to delete all records.');
                    }
                }).catch(error => {
                    alert('Error: ' + error);
                });
            }
        });
    </script>
}
