﻿@model MultiStore.Persistance.Store


@{
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h4>Store</h4>
<hr />
<div>
    <h4>Are you sure you want to delete this?</h4>
    <div>
        <h4>Name</h4>
        <div>
            @Html.DisplayFor(model => model.Name)
        </div>
    </div>
    <form asp-action="DeleteConfirmed">
        <input type="hidden" asp-for="Id" />
        <div class="form-group">
            <input type="submit" value="Delete" class="btn btn-danger" /> |
            <a asp-action="Index">Back to List</a>
        </div>
    </form>
</div>
    