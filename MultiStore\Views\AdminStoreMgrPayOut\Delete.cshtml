﻿@model MultiStore.Persistance.StoreMgrPayOut

@{
    ViewData["Title"] = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Delete Store Manager Payout</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Store Manager Payout</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            Store
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Store.Name)
        </dd>
        <dt class="col-sm-2">
            Recipient
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Recipient)
        </dd>
        <dt class="col-sm-2">
            Amount
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Amount)
        </dd>
        <dt class="col-sm-2">
            Date
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Date)
        </dd>
    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index" class="btn btn-secondary">Back to List</a>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
