﻿@model IEnumerable<MultiStore.Persistance.StoreProductSales>

@{
    ViewData["Title"] = "Store Product Sales";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Store Product Sales</h1>




<table class="table">
    <thead>
        <tr>
            <th>Store</th>
            <th>Product</th>
            <th>Sale Amount</th>
            <th>Day</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.Store.Name</td>
                <td>@item.Product.Name</td>
                <td>@item.SaleAmount</td>
                <td>@item.Day.ToShortDateString()</td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-primary">Edit</a>
                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
