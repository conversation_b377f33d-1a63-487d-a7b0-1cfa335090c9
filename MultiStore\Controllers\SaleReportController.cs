﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using static System.Formats.Asn1.AsnWriter;

namespace MultiStore.Controllers
{
    public class SaleReportController : Controller
    {


        private readonly Data _context;

        public SaleReportController(Data context)
        {
            _context = context;
        }


        public IActionResult Index()
        {
            SaleReportSelectViewModal reportViewModel = new();
            reportViewModel.Companies = _context.Companies.ToList();
            reportViewModel.Stores = _context.Stores.ToList();

            return View(reportViewModel);
        }

        [HttpPost]
        public IActionResult Submit(SaleReportSelectViewModal model)
        {
            try
            {
                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;

                if (model.StartDate != null && model.EndDate != null)
                {
                    if (model.StartDate > model.EndDate)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDate;
                        endDate = (DateTime)model.EndDate;
                    }
                }
                else if (model.SingleDate != null)
                {


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {
                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDate.Value.AddMonths(-2);
                            endDate = model.SingleDate.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }

                }

                if (model.SelectedStoreId != 0)
                {

                    List<SaleReportDTO> dtos = new List<SaleReportDTO>();
                    decimal allSales = 0;
                    decimal allSpendings = 0;

                    //from startDate to endDate
                    for (var date = startDate; date <= endDate; date = date.AddDays(1))
                    {

                        SaleReportDTO saleReportDTO = new SaleReportDTO();

                        //income

                        saleReportDTO.Date = date.ToString("yyyy/MM/dd");

                        var productTypes = _context.ProductTypes.ToList();
                        decimal productsSum = 0;

                        foreach (var productType in productTypes)
                        {
                            saleReportDTO.ProductCatagoriesSale[productType.Name] = _context.StoreProductSales
                            .Include(s => s.Product)
                            .ThenInclude(p => p.ProductType)
                            .Where(s => s.StoreId == model.SelectedStoreId && s.Day.Date == date.Date && s.Product.ProductType.Id == productType.Id)
                            .ToList().Sum(p => p.SaleAmount);
                            productsSum += saleReportDTO.ProductCatagoriesSale[productType.Name];
                        }

                        saleReportDTO.FuelSale = _context.StoreFuelSales.Where(s => s.StoreId == model.SelectedStoreId
                           && s.Day.Date == date.Date).ToList().Sum(f => f.SaleAmount);

                        var serviceTypes = _context.ServiceTypes.ToList();
                        decimal serviceSum = 0;


                        foreach (var serviceType in serviceTypes)
                        {
                            saleReportDTO.ServiceCatagoriesSale[serviceType.Name] = _context.StoreServiceSales
                            .Include(s => s.Service)
                            .ThenInclude(p => p.ServiceType)
                            .Where(s => s.StoreId == model.SelectedStoreId && s.Day.Date == date.Date && s.Service.ServiceType.Id == serviceType.Id)
                            .ToList().Sum(s => s.SalesCount * s.Service.Price);

                            serviceSum += saleReportDTO.ServiceCatagoriesSale[serviceType.Name];
                        }




                        saleReportDTO.ScratchCardsSale = _context.StoreScratchCardSales.Include(s => s.ScratchCard).Where(s => s.StoreId == model.SelectedStoreId
                             && s.Day.Date == date.Date).ToList().Sum(s => s.SalesCount * s.ScratchCard.Price);

                        //spendings
                        saleReportDTO.GeneralPayout = _context.StoreMgrPayOuts
                           .Where(s => s.StoreId == model.SelectedStoreId && s.Date.Date == date.Date).ToList().Sum(s => s.Amount);

                        saleReportDTO.SpecificPayout = _context.SpecificPayOuts
                            .Where(s => s.StoreId == model.SelectedStoreId && s.Date.Date == date.Date).ToList().Sum(s => s.Amount);

                        saleReportDTO.DaysSale = productsSum + saleReportDTO.FuelSale + serviceSum + saleReportDTO.ScratchCardsSale;
                        saleReportDTO.DaysSpendings = saleReportDTO.GeneralPayout + saleReportDTO.SpecificPayout;
                        saleReportDTO.StoreName = _context.Stores.First(s => s.Id == model.SelectedStoreId).Name;

                        dtos.Add(saleReportDTO);


                        allSales += saleReportDTO.DaysSale;
                        allSpendings += saleReportDTO.DaysSpendings;

                    }


                    SaleReportDisplayViewModal saleReportDisplayViewModal = new SaleReportDisplayViewModal();
                    saleReportDisplayViewModal.salesDtos = dtos;
                    saleReportDisplayViewModal.TotalSales = allSales;
                    saleReportDisplayViewModal.TotalSpendings = allSpendings;
                    saleReportDisplayViewModal.StartDate = startDate;
                    saleReportDisplayViewModal.EndDate = endDate;
                    saleReportDisplayViewModal.StoreName = _context.Stores.First(s => s.Id == model.SelectedStoreId).Name;
                    saleReportDisplayViewModal.CompnayName = _context.Companies.First(s => s.Id == _context.Stores.First(s => s.Id == model.SelectedStoreId).CompanyId).Name;

                    return View("SalesReport", saleReportDisplayViewModal);
                }
                else if (model.SelectedCompanyId != 0)
                {

                    List<SaleReportDTO> dtos = new List<SaleReportDTO>();
                    decimal allSales = 0;
                    decimal allSpendings = 0;

                    //get stores of the company
                    List<Store> stores = _context.Stores.Where(s => s.CompanyId == model.SelectedCompanyId).ToList();

                    foreach (var store in stores)
                    {
                        for (var date = startDate; date <= endDate; date = date.AddDays(1))
                        {

                            SaleReportDTO saleReportDTO = new SaleReportDTO();

                            //income

                            saleReportDTO.Date = date.ToString("yyyy/MM/dd");

                            var productTypes = _context.ProductTypes.ToList();
                            decimal productsSum = 0;

                            foreach(var productType in productTypes)
                            {
                                saleReportDTO.ProductCatagoriesSale[productType.Name] = _context.StoreProductSales
                                .Include(s => s.Product)
                                .ThenInclude(p => p.ProductType)
                                .Where(s => s.StoreId == store.Id && s.Day.Date == date.Date && s.Product.ProductType.Id == productType.Id)
                                .ToList().Sum(p => p.SaleAmount);
                                productsSum+= saleReportDTO.ProductCatagoriesSale[productType.Name];
                            }



                            saleReportDTO.FuelSale = _context.StoreFuelSales.Where(s => s.StoreId == store.Id
                               && s.Day.Date == date.Date).ToList().Sum(f => f.SaleAmount);

                          

                            var serviceTypes = _context.ServiceTypes.ToList();
                            decimal serviceSum = 0;


                            foreach (var serviceType in serviceTypes)
                            {
                                saleReportDTO.ServiceCatagoriesSale[serviceType.Name] = _context.StoreServiceSales
                                .Include(s => s.Service)
                                .ThenInclude(p => p.ServiceType)
                                .Where(s => s.StoreId == store.Id && s.Day.Date == date.Date && s.Service.ServiceType.Id == serviceType.Id)
                                .ToList().Sum(s => s.SalesCount * s.Service.Price);

                                serviceSum += saleReportDTO.ServiceCatagoriesSale[serviceType.Name];
                            }



                            saleReportDTO.ScratchCardsSale = _context.StoreScratchCardSales.Include(s => s.ScratchCard).Where(s => s.StoreId == store.Id
                                 && s.Day.Date == date.Date).ToList().Sum(s => s.SalesCount * s.ScratchCard.Price);

                            //spendings
                            saleReportDTO.GeneralPayout = _context.StoreMgrPayOuts
                               .Where(s => s.StoreId == store.Id && s.Date.Date == date.Date).ToList().Sum(s => s.Amount);

                            saleReportDTO.SpecificPayout = _context.SpecificPayOuts
                                .Where(s => s.StoreId == store.Id && s.Date.Date == date.Date).ToList().Sum(s => s.Amount);

                            saleReportDTO.DaysSale = productsSum + saleReportDTO.FuelSale + serviceSum + saleReportDTO.ScratchCardsSale;
                            saleReportDTO.DaysSpendings = saleReportDTO.GeneralPayout + saleReportDTO.SpecificPayout;
                            saleReportDTO.StoreName = _context.Stores.First(s => s.Id == store.Id).Name;

                            dtos.Add(saleReportDTO);


                            allSales += saleReportDTO.DaysSale;
                            allSpendings += saleReportDTO.DaysSpendings;

                        }

                    }

                    SaleReportDisplayViewModal saleReportDisplayViewModal = new SaleReportDisplayViewModal();
                    saleReportDisplayViewModal.salesDtos = dtos;
                    saleReportDisplayViewModal.TotalSales = allSales;
                    saleReportDisplayViewModal.TotalSpendings = allSpendings;
                    saleReportDisplayViewModal.StartDate = startDate;
                    saleReportDisplayViewModal.EndDate = endDate;
                    saleReportDisplayViewModal.StoreName = "All";
                    saleReportDisplayViewModal.CompnayName = _context.Companies.First(c => c.Id == model.SelectedCompanyId).Name;


                    return View("SalesReport", saleReportDisplayViewModal);


                }
                else //both compnay id and store id =0 
                {
                    List<SaleReportDTO> dtos = new List<SaleReportDTO>();
                    decimal allSales = 0;
                    decimal allSpendings = 0;

                    //get stores of the company
                    List<Store> stores = _context.Stores.ToList();

                    foreach (var store in stores)
                    {
                        for (var date = startDate; date <= endDate; date = date.AddDays(1))
                        {

                            SaleReportDTO saleReportDTO = new SaleReportDTO();

                            //income

                            saleReportDTO.Date = date.ToString("yyyy/MM/dd");

                            var productTypes = _context.ProductTypes.ToList();
                            decimal productsSum = 0;

                            foreach (var productType in productTypes)
                            {
                                saleReportDTO.ProductCatagoriesSale[productType.Name] = _context.StoreProductSales
                                .Include(s => s.Product)
                                .ThenInclude(p => p.ProductType)
                                .Where(s => s.StoreId == store.Id && s.Day.Date == date.Date && s.Product.ProductType.Id == productType.Id)
                                .ToList().Sum(p => p.SaleAmount);
                                productsSum += saleReportDTO.ProductCatagoriesSale[productType.Name];
                            }

                            saleReportDTO.FuelSale = _context.StoreFuelSales.Where(s => s.StoreId == store.Id
                               && s.Day.Date == date.Date).ToList().Sum(f => f.SaleAmount);


                            var serviceTypes = _context.ServiceTypes.ToList();
                            decimal serviceSum = 0;


                            foreach (var serviceType in serviceTypes)
                            {
                                saleReportDTO.ServiceCatagoriesSale[serviceType.Name] = _context.StoreServiceSales
                                .Include(s => s.Service)
                                .ThenInclude(p => p.ServiceType)
                                .Where(s => s.StoreId == store.Id && s.Day.Date == date.Date && s.Service.ServiceType.Id == serviceType.Id)
                                .ToList().Sum(s => s.SalesCount * s.Service.Price);

                                serviceSum += saleReportDTO.ServiceCatagoriesSale[serviceType.Name];
                            }


                            saleReportDTO.ScratchCardsSale = _context.StoreScratchCardSales.Include(s => s.ScratchCard).Where(s => s.StoreId == store.Id
                                 && s.Day.Date == date.Date).ToList().Sum(s => s.SalesCount * s.ScratchCard.Price);

                            //spendings
                            saleReportDTO.GeneralPayout = _context.StoreMgrPayOuts
                               .Where(s => s.StoreId == store.Id && s.Date.Date == date.Date).ToList().Sum(s => s.Amount);

                            saleReportDTO.SpecificPayout = _context.SpecificPayOuts
                                .Where(s => s.StoreId == store.Id && s.Date.Date == date.Date).ToList().Sum(s => s.Amount);

                            saleReportDTO.DaysSale = productsSum + saleReportDTO.FuelSale + serviceSum + saleReportDTO.ScratchCardsSale;
                            saleReportDTO.DaysSpendings = saleReportDTO.GeneralPayout + saleReportDTO.SpecificPayout;
                            saleReportDTO.StoreName = _context.Stores.First(s => s.Id == store.Id).Name;

                            dtos.Add(saleReportDTO);


                            allSales += saleReportDTO.DaysSale;
                            allSpendings += saleReportDTO.DaysSpendings;

                        }

                    }

                    SaleReportDisplayViewModal saleReportDisplayViewModal = new SaleReportDisplayViewModal();
                    saleReportDisplayViewModal.salesDtos = dtos;
                    saleReportDisplayViewModal.TotalSales = allSales;
                    saleReportDisplayViewModal.TotalSpendings = allSpendings;
                    saleReportDisplayViewModal.StartDate = startDate;
                    saleReportDisplayViewModal.EndDate = endDate;
                    saleReportDisplayViewModal.StoreName = "All";
                    saleReportDisplayViewModal.CompnayName = "All";


                    return View("SalesReport", saleReportDisplayViewModal);
                }










                return View("CommissionReport");


            }
            catch (Exception ex)
            {

                return View("Index");
            }
        }

        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }
    }
}
