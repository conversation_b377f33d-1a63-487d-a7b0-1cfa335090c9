﻿using System.ComponentModel.DataAnnotations;

namespace MultiStore.Models
{
    public class ScratchCardsSale
    {
        public int ScratchCardId { get; set; }

        public string Number { get; set; }  //name

        public decimal Price { get; set; }
        public int Opening { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Closing must be a non-negative number.")]
        public int Closing { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "Closing must be a non-negative number.")]
        public int Activate { get; set; }


    }

    public class ScratchCardSalesViewModel
    {

        public int StoreId { get; set; }
        public List<ScratchCardsSale> ScratchCardsSales { get; set; } = new List<ScratchCardsSale>();
    }
}
