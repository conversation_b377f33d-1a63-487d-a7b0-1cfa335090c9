﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using System.Linq;
using System.Threading.Tasks;

namespace MultiStore.Controllers
{
    public class AdminSpecificPayOutsController : Controller
    {
        private readonly Data _context;
        private readonly IUtilityService _utilityService;

        public AdminSpecificPayOutsController(Data context, IUtilityService utilityService)
        {
            _context = context;
            _utilityService = utilityService;
        }



        // GET: AdminSpecificPayOuts
        public async Task<IActionResult> Index(DateTime startDate, DateTime endDate)
        {
            var specificPayOuts = _context.SpecificPayOuts
                .Include(s => s.PayOutParty)
                .Include(s => s.Store)
                .Where(s => s.Date.Date >= startDate && s.Date.Date <= endDate);
            return View(await specificPayOuts.ToListAsync());
        }

        // GET: AdminSpecificPayOuts/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var specificPayOut = await _context.SpecificPayOuts.FindAsync(id);
            if (specificPayOut == null)
            {
                return NotFound();
            }

            ViewData["PayOutPartyId"] = new SelectList(_context.PayOutParties, "Id", "Name", specificPayOut.PayOutPartyId);
            ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", specificPayOut.StoreId);

            return View(specificPayOut);
        }

        // POST: AdminSpecificPayOuts/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,PayOutPartyId,StoreId,Amount,Date")] SpecificPayOut specificPayOut)
        {
            if (id != specificPayOut.Id)
            {
                return NotFound();
            }

            //   if (ModelState.IsValid)
            //   {
            try
            {
                _context.Update(specificPayOut);
                await _context.SaveChangesAsync();
                 _utilityService.UpdateStoreDaysToReflectChanges(specificPayOut.StoreId);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!SpecificPayOutExists(specificPayOut.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Index), "AdminStoreSales");

            return RedirectToAction(nameof(Index));
            //   }

            //   ViewData["PayOutPartyId"] = new SelectList(_context.PayOutParties, "Id", "Name", specificPayOut.PayOutPartyId);
            //   ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", specificPayOut.StoreId);

            //   return View(specificPayOut);
        }

        // GET: AdminSpecificPayOuts/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var specificPayOut = await _context.SpecificPayOuts
                .Include(s => s.PayOutParty)
                .Include(s => s.Store)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (specificPayOut == null)
            {
                return NotFound();
            }

            return View(specificPayOut);
        }

        // POST: AdminSpecificPayOuts/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var specificPayOut = await _context.SpecificPayOuts.FindAsync(id);
            _context.SpecificPayOuts.Remove(specificPayOut);
            await _context.SaveChangesAsync();
           _utilityService.UpdateStoreDaysToReflectChanges(specificPayOut.StoreId);

            return RedirectToAction(nameof(Index), "AdminStoreSales");

            return RedirectToAction(nameof(Index));
        }

        private bool SpecificPayOutExists(int id)
        {
            return _context.SpecificPayOuts.Any(e => e.Id == id);
        }


    }
}