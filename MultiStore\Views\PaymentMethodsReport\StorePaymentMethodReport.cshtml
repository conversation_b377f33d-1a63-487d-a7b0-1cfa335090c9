﻿@model IEnumerable<MultiStore.Persistance.StorePaymentMethod>

@{
    ViewData["Title"] = "Payment Methods Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed
    var totalAmount = Model.Sum(x => x.Amount);
}

<h1>Payment Methods Report</h1>

<table class="table">
    <thead>
        <tr>
            <th>Store</th>
            <th>Payment Card</th>
            <th>Amount</th>
            <th>Date</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.Store.Name</td>
                <td>@item.PaymentCard?.Name</td>
                <td>@item.Amount.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@item.Date.ToShortDateString()</td>
            </tr>
        }
        <tr>
            <td colspan="2"><strong>Total</strong></td>
            <td><strong>@totalAmount</strong></td>
            <td></td>
        </tr>
    </tbody>
</table>

