﻿using Microsoft.EntityFrameworkCore;

namespace MultiStore.Persistance
{
    public class CommisionEntry
    {
        public int Id { get; set; }


        public CommisionType CommisionType { get; set; }

        public Store Store { get; set; }

        public int StoreId { get; set; }

        public string InvoiceNumber { get; set; }

        public Supplier Supplier { get; set; }

        public int SupplierId { get; set; }


        public decimal Gross { get; set; }

        public decimal Vat { get; set; }

        public decimal Net { get; set; }

        public DateTime Date { get; set; }

    }

    public enum CommisionType
    {
        Income,
        Outcome
    }

    public class CommisionEntryDTO
    {
        public string Type { get; set; }

        public string StoreName { get; set; }

        public string InvoiceNumber { get; set; }


        public string SupplierName { get; set; }

        public decimal Gross { get; set; }

        public decimal Vat { get; set; }

        public decimal Net { get; set; }

        public DateTime Date { get; set; }

    }
}
