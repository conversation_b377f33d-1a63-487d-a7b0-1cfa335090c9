﻿@model MultiStore.Persistance.Store

@{
    ViewData["Title"] = "Edit Store";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Edit Store</h1>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit" method="post">
            <input type="hidden" asp-for="Id" />

            <!-- Store Name -->
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>

            <!-- Store Type -->
            <div class="form-group">
                <label asp-for="StoreType" class="control-label"></label>
                <select asp-for="StoreType" class="form-control" asp-items="Html.GetEnumSelectList<MultiStore.Persistance.StoreType>()"></select>
                <span asp-validation-for="StoreType" class="text-danger"></span>
            </div>

            <!-- Company -->
            <div class="form-group">
                <label asp-for="CompanyId" class="control-label"></label>
                <select asp-for="CompanyId" class="form-control" asp-items="ViewBag.CompanyId"></select>
                <span asp-validation-for="CompanyId" class="text-danger"></span>
            </div>

            <td>
                <label>Atm Initial Open Value</label>

                <input type="number" step="0.01" class="form-control" asp-for="AtmInitialOpenValue" />
            </td>

            <!-- Email Addresses -->
            <div class="form-group">
                <label>Email Addresses</label>
                <div id="emailAddresses">
                    @if (ViewBag.EmailAddresses != null)
                    {
                        var emailList = ViewBag.EmailAddresses as List<string>;
                        for (int i = 0; i < emailList.Count; i++)
                        {
                            <input type="text" name="emailAddresses[@i]" class="form-control mb-2" value="@emailList[i]" />
                        }
                    }
                </div>
                <button type="button" class="btn btn-secondary" onclick="addEmailField()">Add Email Address</button>
            </div>

            <!-- Submit Button -->
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    <script>
        function addEmailField() {
            var emailIndex = document.querySelectorAll('#emailAddresses input').length;
            var newEmailField = document.createElement('input');
            newEmailField.setAttribute('type', 'text');
            newEmailField.setAttribute('name', `emailAddresses[${emailIndex}]`);
            newEmailField.setAttribute('class', 'form-control mb-2');
            document.getElementById('emailAddresses').appendChild(newEmailField);
        }
    </script>
}
