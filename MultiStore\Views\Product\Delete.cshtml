﻿@model MultiStore.Persistance.Product

@{
    ViewData["Title"] = "Delete Product";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Delete Product</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Product</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Name)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.ProductType.Name)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.ProductType.Name)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.VatPercentage.Percentage)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.VatPercentage.Percentage)
        </dd>
    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
