﻿using Microsoft.AspNetCore.SignalR;
using System.Security.Claims;

namespace MultiStore
{
    public class NotificationHub : Hub
    {

        public override Task OnConnectedAsync()
        {
            // Optionally, you can log or handle connection events
            // Set the user identifier if needed
            return base.OnConnectedAsync();
        }

        public override Task OnDisconnectedAsync(Exception? exception)
        {
            // Optionally, you can log or handle disconnection events
            return base.OnDisconnectedAsync(exception);
        }

        // Send message to a specific user
        public async Task SendNotification(string userId, string message)
        {
            await Clients.User(userId).SendAsync("ReceiveNotification", message);
        }

    }
}
    