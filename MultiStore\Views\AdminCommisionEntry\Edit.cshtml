﻿@model MultiStore.Persistance.CommisionEntry

@{
    ViewData["Title"] = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2>Edit</h2>

<h4>CommisionEntry</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div class="form-group">
                <label asp-for="CommisionType" class="control-label"></label>
                <select asp-for="CommisionType" class="form-control">
                    <option value="Income">Income</option>
                    <option value="Outcome">Outcome</option>
                </select>
            </div>
            <div class="form-group">
                <label asp-for="StoreId" class="control-label"></label>
                <select asp-for="StoreId" class="form-control" asp-items="ViewBag.StoreId"></select>
            </div>
            <div class="form-group">
                <label asp-for="InvoiceNumber" class="control-label"></label>
                <input asp-for="InvoiceNumber" class="form-control" />
                <span asp-validation-for="InvoiceNumber" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="SupplierId" class="control-label"></label>
                <select asp-for="SupplierId" class="form-control" asp-items="ViewBag.SupplierId"></select>
            </div>
            <div class="form-group">
                <label asp-for="Gross" class="control-label"></label>
                <input asp-for="Gross" class="form-control" />
                <span asp-validation-for="Gross" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Vat" class="control-label"></label>
                <input asp-for="Vat" class="form-control" />
                <span asp-validation-for="Vat" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Net" class="control-label"></label>
                <input asp-for="Net" class="form-control" />
                <span asp-validation-for="Net" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Date" class="control-label"></label>
                <input asp-for="Date" class="form-control" />
                <span asp-validation-for="Date" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>
<div>
    <a asp-action="Index">Back to List</a>
</div>
