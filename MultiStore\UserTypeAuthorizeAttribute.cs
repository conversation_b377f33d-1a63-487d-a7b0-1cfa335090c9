﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;


namespace MultiStore
{
    public class UserTypeAuthorizeAttribute : Attribute, IAuthorizationFilter
    {
        private readonly string _userType;

        public UserTypeAuthorizeAttribute(string userType)
        {
            _userType = userType;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            var userType = context.HttpContext.Session.GetString("UserType");

            if (userType == null || userType != _userType)
            {
                context.Result = new ForbidResult();
            }
        }
    }
}
