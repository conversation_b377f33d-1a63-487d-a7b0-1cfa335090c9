﻿using System.Runtime.CompilerServices;

namespace MultiStore.Persistance
{
    public class StoreProductSales
    {

        public int Id { get; set; }

        public Store Store { get; set; }
        public int StoreId { get; set; }

        public int ProductId { get; set; }
        public Product Product { get; set; }

        public decimal SaleAmount{ get; set; }

        public DateTime Day { get; set; }


    }

   

}
