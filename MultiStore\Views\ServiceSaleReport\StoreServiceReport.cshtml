﻿@model IEnumerable<MultiStore.Persistance.StoreServiceSales>

@{
    ViewData["Title"] = "Service Sales Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed
                                                     // Update the layout path as needed

    // Extract unique store names and service names
    var storeNames = Model.Select(s => s.Store.Name).Distinct().ToList();
    var services = Model.Select(s => s.Service).Distinct().ToList();
    var serviceNames = Model.Select(s => s.Service.Name).Distinct().ToList();


    // Calculate total sales count for each service
    var totalSalesCountByService = serviceNames.ToDictionary(
        serviceName => serviceName,
        serviceName => Model.Where(s => s.Service.Name == serviceName).Sum(s => (int?)s.SalesCount * (s.Service.Price-s.Service.Discount)) ?? 0
    );
}

<h1>Service Sales Report</h1>

<div>
    <p><strong>Company:</strong> @ViewData["CompanyName"]</p>
    <p><strong>From:</strong> @ViewData["StartDate"] <strong>To:</strong> @ViewData["EndDate"]</p>
</div>

<table class="table">
    <thead>
        <tr>
            <th>Service</th>
            <th>Vat</th>

            @foreach (var store in storeNames)
            {
                <th>@store</th>
            }
            <th>Total</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var service in services)
        {
            <tr>
                <td>@service.Name</td>
                <td>@service.VatPercentage.Percentage</td>

                @foreach (var store in storeNames)
                {
                    <td>
                        @{
                            var salesCount = Model
                            .Where(s => s.Service.Name == service.Name && s.Store.Name == store)
                            .Sum(s => (int?)s.SalesCount * (s.Service.Price - s.Service.Discount)) ?? 0;
                            @salesCount
                        }
                    </td>
                }
                <td>
                    @totalSalesCountByService[service.Name]
                </td>
            </tr>
        }
    </tbody>
    <tfoot>
        <tr>
            <td><strong>Total</strong></td>
            <td><strong></strong></td>
            @foreach (var store in storeNames)
            {
                <td>
                    <strong>
                        @{
                            var totalSalesCount = Model
                            .Where(s => s.Store.Name == store)
                            .Sum(s => (int?)s.SalesCount * (s.Service.Price - s.Service.Discount)) ?? 0;
                            @totalSalesCount
                        }
                    </strong>
                </td>
            }
            <td>
                <strong>
                    @{
                        var grandTotal = Model.Sum(s => (int?)s.SalesCount * (s.Service.Price - s.Service.Discount)) ?? 0;
                        @grandTotal
                    }
                </strong>
            </td>
        </tr>
    </tfoot>
</table>
