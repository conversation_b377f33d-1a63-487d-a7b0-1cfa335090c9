﻿using Microsoft.AspNetCore.Mvc;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    [TypeFilter(typeof(AdminAuthorizationFilter))]
    public class AdminController : Controller
    {

        private readonly Data _context;

        public AdminController(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            return View();
        }

        [HttpGet]
        public IActionResult Update()
        {
            var admin = _context.Admins.FirstOrDefault();

            return View(admin);
        }


        [HttpPost]
        public IActionResult Update(Admin admin)
        {
            if(string.IsNullOrEmpty(admin.Password) == false)
            {
                _context.Admins.First().Password = admin.Password;
                _context.SaveChanges();

                return View("Index");
            }

            return RedirectToAction(nameof(Update));
        }
    }
}
