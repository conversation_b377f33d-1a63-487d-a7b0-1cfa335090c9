using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using MultiStore.Persistance;

namespace MultiStore
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddControllersWithViews();
            builder.Services.AddSingleton<ICompositeViewEngine, CompositeViewEngine>();
          //  builder.Services.AddSignalR(); // Add SignalR

            builder.Services.AddScoped<StoreManagerAuthorizationFilter>();
            builder.Services.AddScoped<IUtilityService, UtilityService>();


            // Register NotificationService with 
            builder.Services.AddHttpContextAccessor(); 

            builder.Services.AddDbContext<Data>(options =>
            {
                options.UseSqlServer(builder.Configuration.GetConnectionString("Local"));
            });

            builder.Services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromDays(1000);
                options.Cookie.HttpOnly = true;
                options.Cookie.IsEssential = true;
            });

            var app = builder.Build();

            // Configure the HTTP request pipeline.
            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Home/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseStaticFiles();

            app.UseRouting();

            app.UseAuthorization();

            app.UseSession();

            app.MapControllerRoute(
                name: "default",
                pattern: "{controller=Home}/{action=Index}/{id?}");

       //     app.MapHub<NotificationHub>("/notificationHub");

            app.Run();
        }
    }
}