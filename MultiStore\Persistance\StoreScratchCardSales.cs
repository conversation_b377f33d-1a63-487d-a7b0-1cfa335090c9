﻿namespace MultiStore.Persistance
{
    public class StoreScratchCardSales
    {

        public int Id { get; set; }

        public Store Store { get; set; }
        public int StoreId { get; set; }

        public int ScratchCardId { get; set; }
        public ScratchCard ScratchCard { get; set; }

        public int SalesCount { get; set; }

        public int CloseCount { get; set; }

        public int ActivationCount { get; set; }



        public DateTime Day { get; set; }


    }
}
