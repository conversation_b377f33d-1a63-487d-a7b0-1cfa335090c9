﻿@model MultiStore.Persistance.EmailAddress


@{
    ViewData["Title"] = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed


}

<h1>Edit</h1>

<h4>EmailAddress</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div class="form-group">
                <label asp-for="Address" class="control-label"></label>
                <input asp-for="Address" class="form-control" />
                <span asp-validation-for="Address" class="text-danger"></span>
            </div>
            <div class="form-group">
                <div class="form-check">
                    <input asp-for="Enabled" class="form-check-input" type="checkbox" />
                    <label asp-for="Enabled" class="form-check-label"></label>
                </div>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>
