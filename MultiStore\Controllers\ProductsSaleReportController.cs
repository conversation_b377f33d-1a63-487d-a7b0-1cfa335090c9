﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using System.Globalization;
using System.Runtime.CompilerServices;
using System.Security.Principal;
using System.Threading.Tasks.Dataflow;

namespace MultiStore.Controllers
{
    public class ProductsSaleReportController : Controller
    {

        private readonly Data _context;

        public ProductsSaleReportController(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            SaleReportSelectViewModal reportViewModel = new SaleReportSelectViewModal();
            reportViewModel.Stores = _context.Stores.ToList();
            reportViewModel.Companies = _context.Companies.ToList();

            return View(reportViewModel);
        }

        [HttpPost]
        public IActionResult Submit(SaleReportSelectViewModal model)
        {

            bool newMode = false;

            try
            {
                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;

                if (model.StartDate != null && model.EndDate != null)
                {
                    if (model.StartDate > model.EndDate)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDate;
                        endDate = (DateTime)model.EndDate;
                        newMode = true;
                    }
                }
                else if (model.SingleDate != null)
                {


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {
                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDate.Value.AddMonths(-2);
                            endDate = model.SingleDate.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }

                }

                List<StoreProductSales>? storeProductSales = new();


                if (model.SelectedStoreId != 0)
                {

                    storeProductSales = _context.StoreProductSales
                         .Include(s => s.Store)
                         .Include(s => s.Product)
                      .Where(p => p.StoreId == model.SelectedStoreId && p.Day.Date >= startDate.Date && p.Day.Date <= endDate.Date)
                      .ToList();


                }
                else if (model.SelectedCompanyId != 0)
                {


                    var stores = _context.Stores.Where(s => s.CompanyId == model.SelectedCompanyId)
                       .ToList();


                    foreach (var item in stores)
                    {
                        storeProductSales.AddRange(_context.StoreProductSales
                           .Include(s => s.Store)
                         .Include(s => s.Product)
                        .Where(p => p.StoreId == item.Id && p.Day.Date >= startDate.Date && p.Day.Date <= endDate.Date)
                        .ToList());
                    }

                }
                else //both compnay id and store id =0 
                {
                    storeProductSales = _context.StoreProductSales
                        .Include(s => s.Store)
                        .Include(s => s.Product)
                     .Where(p => p.Day.Date >= startDate.Date && p.Day.Date <= endDate.Date)
                     .ToList();
                }



                if (newMode && model.Period == "Month")
                {
                    var groupedByMonth = storeProductSales
          .GroupBy(r => new { r.Day.Year, r.Day.Month })
          .Select(g => new
          {
              Year = g.Key.Year,
              Month = g.Key.Month,
              Records = g.ToList()
          });


                    storeProductSales = new List<StoreProductSales>();


                    foreach (var group in groupedByMonth)
                    {
                        decimal amountSum = 0;

                        foreach (var record in group.Records)
                        {
                            amountSum += record.SaleAmount;
                        }

                        storeProductSales.Add(new StoreProductSales()
                        { SaleAmount = amountSum, Day = new DateTime(group.Year, group.Month, 1) });

                    }


                }
                else if (newMode && model.Period == "Week")
                {
                    var calendar = CultureInfo.InvariantCulture.Calendar;

                    var groupedByWeek = storeProductSales
                        .GroupBy(r => new { Year = r.Day.Year, Week = calendar.GetWeekOfYear(r.Day, CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday), id = r.ProductId })
                        .Select(g => new
                        {
                            Year = g.Key.Year,
                            Week = g.Key.Week,
                            Records = g.ToList()
                        });


                    ProductSaleWeeklyReportViewModel productSaleWeeklyReportViewModel = new();

                    var tempGroupList = groupedByWeek.ToList();

                    foreach (var group in groupedByWeek)
                    {

                        ProductSaleWeekRecord productSaleWeekRecord = new();



                        foreach (var record in group.Records)
                        {
                            productSaleWeekRecord.Total += record.SaleAmount;

                            productSaleWeekRecord.Store = record.Store;
                            productSaleWeekRecord.Product = record.Product;


                            if (record.Day.DayOfWeek == DayOfWeek.Monday) productSaleWeekRecord.Monday = record.SaleAmount;
                            if (record.Day.DayOfWeek == DayOfWeek.Tuesday) productSaleWeekRecord.Tuesday = record.SaleAmount;
                            if (record.Day.DayOfWeek == DayOfWeek.Wednesday) productSaleWeekRecord.Wednesday = record.SaleAmount;
                            if (record.Day.DayOfWeek == DayOfWeek.Thursday) productSaleWeekRecord.Thursday = record.SaleAmount;
                            if (record.Day.DayOfWeek == DayOfWeek.Friday) productSaleWeekRecord.Friday = record.SaleAmount;
                            if (record.Day.DayOfWeek == DayOfWeek.Saturday) productSaleWeekRecord.Saturday = record.SaleAmount;
                            if (record.Day.DayOfWeek == DayOfWeek.Sunday) productSaleWeekRecord.Sunday = record.SaleAmount;

                        }




                        var weekStartDate = FirstDateOfWeek(group.Records.FirstOrDefault()?
                            .Day ?? DateTime.MinValue, DayOfWeek.Monday);

                        productSaleWeekRecord.Day = weekStartDate;
                        productSaleWeeklyReportViewModel.Records.Add(productSaleWeekRecord);

                      
                    }

                    ViewData["StartDate"] = startDate.ToShortDateString();
                    ViewData["EndDate"] = endDate.ToShortDateString();

                    ViewData["StoreName"] = (model.SelectedStoreId != 0) ? _context.Stores
                     .Where(s => s.Id == model.SelectedStoreId)
                     .FirstOrDefault().Name : "All Stores";

                    ViewData["CompanyName"] = (model.SelectedCompanyId != 0) ? _context.Companies
                        .FirstOrDefault(c => c.Id == model.SelectedCompanyId).Name : "All Companies";

                    return View("StoreProductWeeklyReport", productSaleWeeklyReportViewModel);
                }




                ViewData["StartDate"] = startDate.ToShortDateString();
                ViewData["EndDate"] = endDate.ToShortDateString();

                ViewData["StoreName"] = (model.SelectedStoreId != 0) ? _context.Stores
                 .Where(s => s.Id == model.SelectedStoreId)
                 .FirstOrDefault().Name : "All Stores";

                ViewData["CompanyName"] = (model.SelectedCompanyId != 0) ? _context.Companies
                    .FirstOrDefault(c => c.Id == model.SelectedCompanyId).Name : "All Companies";

                return View("StoreProductReport", storeProductSales);
            }
            catch (Exception ex)
            {

                return View("Index");
            }
        }

        //represent a row in the store product report weekly table
        //
        public class ProductSaleWeeklyReportViewModel
        {
            public List<ProductSaleWeekRecord> Records { get; set; } = new List<ProductSaleWeekRecord>();

        }


        public class ProductSaleWeekRecord
        {

            public Store Store { get; set; }

            public Product Product { get; set; }


            public decimal Monday { get; set; }
            public decimal Tuesday { get; set; }

            public decimal Wednesday { get; set; }

            public decimal Thursday { get; set; }
            public decimal Friday { get; set; }

            public decimal Saturday { get; set; }
            public decimal Sunday { get; set; }

            public decimal Total { get; set; }

            public DateTime Day { get; set; } //week start date
        }

        public static DateTime FirstDateOfWeek(DateTime date, DayOfWeek startOfWeek)
        {
            int diff = (7 + (date.DayOfWeek - startOfWeek)) % 7;
            return date.AddDays(-1 * diff).Date;
        }

        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }
    }
}
