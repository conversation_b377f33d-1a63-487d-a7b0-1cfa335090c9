﻿@model MultiStore.Models.EditProductsViewModel

@{
    ViewData["Title"] = "Edit Products";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Edit Products for Store</h1>

<form asp-action="UpdateProducts" method="post">
    <input type="hidden" asp-for="StoreId" />
    <input type="hidden" id="selectedProductIds" name="selectedProductIds" />

    <div class="row">
        <div class="col-md-6">
            <h3>Selected Products</h3>
            <select multiple class="form-control" id="selectedProducts" size="10">
                @foreach (var product in Model.SelectedProducts)
                {
                    <option value="@product.Id">@product.Name</option>
                }
            </select>
            <button type="button" class="btn btn-primary" id="deselectAll">Deselect All</button>
        </div>
        <div class="col-md-6">
            <h3>Non-Selected Products</h3>
            <select multiple class="form-control" id="nonSelectedProducts" size="10">
                @foreach (var product in Model.NonSelectedProducts)
                {
                    <option value="@product.Id">@product.Name</option>
                }
            </select>
            <button type="button" class="btn btn-primary" id="selectAll">Select All</button>
        </div>
    </div>

    <div class="form-group mt-3">
        <input type="submit" value="Save" class="btn btn-primary" />
    </div>
</form>

@section Scripts {
    <script>
        document.getElementById("deselectAll").onclick = function () {
            var selectedProducts = document.getElementById("selectedProducts");
            var nonSelectedProducts = document.getElementById("nonSelectedProducts");
            while (selectedProducts.options.length > 0) {
                nonSelectedProducts.appendChild(selectedProducts.options[0]);
            }
        };

        document.getElementById("selectAll").onclick = function () {
            var selectedProducts = document.getElementById("selectedProducts");
            var nonSelectedProducts = document.getElementById("nonSelectedProducts");
            while (nonSelectedProducts.options.length > 0) {
                selectedProducts.appendChild(nonSelectedProducts.options[0]);
            }
        };

        document.getElementById("selectedProducts").ondblclick = function () {
            var selectedOptions = this.selectedOptions;
            var nonSelectedProducts = document.getElementById("nonSelectedProducts");
            for (var i = 0; i < selectedOptions.length; i++) {
                nonSelectedProducts.appendChild(selectedOptions[i]);
            }
        };

        document.getElementById("nonSelectedProducts").ondblclick = function () {
            var selectedOptions = this.selectedOptions;
            var selectedProducts = document.getElementById("selectedProducts");
            for (var i = 0; i < selectedOptions.length; i++) {
                selectedProducts.appendChild(selectedOptions[i]);
            }
        };

        document.querySelector("form").onsubmit = function () {
            var selectedProductIds = document.getElementById("selectedProductIds");
            var selectedProducts = document.getElementById("selectedProducts");

            var selectedIds = [];
            for (var i = 0; i < selectedProducts.options.length; i++) {
                selectedIds.push(selectedProducts.options[i].value);
            }

            selectedProductIds.value = selectedIds.join(",");
        };
    </script>
}
