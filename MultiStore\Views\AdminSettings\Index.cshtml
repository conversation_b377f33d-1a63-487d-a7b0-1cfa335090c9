﻿@model AdminSettingsViewModel


@{
    ViewData["Title"] = "Purchase Entries";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Settings</h1>

<form action="@Url.Action("FormSumbit", "AdminSettings")" method="post" onsubmit="return confirmSubmission();">

    <input type="date" name="StartDate" value="@DateTime.Now.ToString("yyyy-MM-dd")" />

    <label>Current Set Date: <b>@Model.CurrentStartDate</b></label>

    <h2>Services</h2>
    <table class="table">
        <thead>
            <tr>
                <th>Store Name</th>
                <th>Service Name</th>
                <th>Initial Open</th>
            </tr>
        </thead>
        <tbody>
            @for (int i = 0; i < Model.StoreServices.Count; i++)
            {
                <tr>
                    <td>
                        @Model.StoreServices[i].Store.Name
                    </td>
                    <td>
                        @Model.StoreServices[i].Service.Name
                    </td>
                    <td>
                        <input asp-for="@Model.StoreServices[i].InitialOpen" class="form-control" />
                        <input type="hidden" asp-for="@Model.StoreServices[i].StoreId" />
                        <input type="hidden" asp-for="@Model.StoreServices[i].ServiceId" />
                    </td>
                </tr>
            }
        </tbody>
    </table>

    <h2>Scratch Cards</h2>
    <table class="table">
        <thead>
            <tr>
                <th>Store Name</th>
                <th>Scratch Card Name</th>
                <th>Initial Open</th>
            </tr>
        </thead>
        <tbody>
            @for (int i = 0; i < Model.StoreScratchCards.Count; i++)
            {
                <tr>
                    <td>
                        @Model.StoreScratchCards[i].Store.Name
                    </td>
                    <td>
                        @Model.StoreScratchCards[i].ScratchCard.Name
                    </td>
                    <td>
                        <input asp-for="@Model.StoreScratchCards[i].InitialOpen" class="form-control" />
                        <input type="hidden" asp-for="@Model.StoreScratchCards[i].StoreId" />
                        <input type="hidden" asp-for="@Model.StoreScratchCards[i].ScratchCardId" />
                    </td>
                </tr>
            }
        </tbody>
    </table>



    <input type="submit" value="Reset" name="action" class="btn btn-success" />
    <input type="submit" value="Update Opening Values" name="action" class="btn btn-success" />

</form>


<script type="text/javascript">
    function confirmSubmission() {
        return confirm("Are you sure you want to reset?");
    }
</script>