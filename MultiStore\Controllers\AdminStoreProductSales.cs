﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;
using MultiStore.Models;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class AdminStoreProductSalesController : Controller
    {
        private readonly Data _context;
        private readonly IUtilityService _utilityService;

        public AdminStoreProductSalesController(Data context , IUtilityService utilityService)
        {
            _context = context;
            _utilityService = utilityService;
        }



        // GET: AdminStoreProductSales
        public async Task<IActionResult> Index(DateTime  startDate, DateTime endDate)
        {
            var sales = await _context.StoreProductSales
                .Include(s => s.Store)
                .Include(s => s.Product)
                .Where(s => s.Day.Date >= startDate && s.Day.Date <= endDate)
                .ToListAsync();
            return View(sales);
        }

        // GET: AdminStoreProductSales/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeProductSales = await _context.StoreProductSales.FindAsync(id);
            if (storeProductSales == null)
            {
                return NotFound();
            }

            var viewModel = new StoreProductSalesViewModel
            {
                StoreProductSales = storeProductSales,
                Stores = new SelectList(_context.Stores, "Id", "Name"),
                Products = new SelectList(_context.Products, "Id", "Name")
            };

            return View(viewModel);
        }

        // POST: AdminStoreProductSales/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, StoreProductSalesViewModel viewModel)
        {
            if (id != viewModel.StoreProductSales.Id)
            {
                return NotFound();
            }

            //if (ModelState.IsValid)
            //{
                try
                {
                    _context.Update(viewModel.StoreProductSales);
                    await _context.SaveChangesAsync();
                _utilityService.UpdateStoreDaysToReflectChanges(viewModel.StoreProductSales.StoreId);
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!StoreProductSalesExists(viewModel.StoreProductSales.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            return RedirectToAction(nameof(Index), "AdminStoreSales");

            //}

            //viewModel.Stores = new SelectList(_context.Stores, "Id", "Name");
            //viewModel.Products = new SelectList(_context.Products, "Id", "Name");
            //return View(viewModel);
        }

        // GET: AdminStoreProductSales/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeProductSales = await _context.StoreProductSales
                .Include(s => s.Store)
                .Include(s => s.Product)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (storeProductSales == null)
            {
                return NotFound();
            }

            return View(storeProductSales);
        }

        // POST: AdminStoreProductSales/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var storeProductSales = await _context.StoreProductSales.FindAsync(id);
            _context.StoreProductSales.Remove(storeProductSales);
            await _context.SaveChangesAsync();
            _utilityService. UpdateStoreDaysToReflectChanges(storeProductSales.StoreId);

            return RedirectToAction(nameof(Index), "AdminStoreSales");

        }

        // POST: AdminStoreProductSales/DeleteAll
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteAll()
        {
            var allSales = _context.StoreProductSales.ToList();
            _context.StoreProductSales.RemoveRange(allSales);
            await _context.SaveChangesAsync();

            return RedirectToAction(nameof(Index));
        }

        private bool StoreProductSalesExists(int id)
        {
            return _context.StoreProductSales.Any(e => e.Id == id);
        }
    }
}
