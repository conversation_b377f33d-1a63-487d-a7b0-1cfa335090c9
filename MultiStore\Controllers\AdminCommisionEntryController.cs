﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class AdminCommisionEntryController : Controller
    {
        private readonly Data _context;

        public AdminCommisionEntryController(Data context)
        {
            _context = context;
        }

        // GET: AdminCommisionEntry
        public async Task<IActionResult> Index()
        {
            var commisionEntries = _context.CommisionEntries
                .Include(c => c.Store)
                .Include(c => c.Supplier);
            return View(await commisionEntries.ToListAsync());
        }

        // GET: AdminCommisionEntry/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var commisionEntry = await _context.CommisionEntries.FindAsync(id);
            if (commisionEntry == null)
            {
                return NotFound();
            }

            ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", commisionEntry.StoreId);
            ViewData["SupplierId"] = new SelectList(_context.Suppliers, "Id", "Name", commisionEntry.SupplierId);

            return View(commisionEntry);
        }

        // POST: AdminCommisionEntry/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,CommisionType,StoreId,InvoiceNumber,SupplierId,Gross,Vat,Net,Date")] CommisionEntry commisionEntry)
        {
            if (id != commisionEntry.Id)
            {
                return NotFound();
            }

           // if (ModelState.IsValid)
           // {
                try
                {
                    _context.Update(commisionEntry);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CommisionEntryExists(commisionEntry.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
           // }

          //  ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", commisionEntry.StoreId);
          //  ViewData["SupplierId"] = new SelectList(_context.Suppliers, "Id", "Name", commisionEntry.SupplierId);

         //   return View(commisionEntry);
        }

        // GET: AdminCommisionEntry/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var commisionEntry = await _context.CommisionEntries
                .Include(c => c.Store)
                .Include(c => c.Supplier)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (commisionEntry == null)
            {
                return NotFound();
            }

            return View(commisionEntry);
        }

        // POST: AdminCommisionEntry/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var commisionEntry = await _context.CommisionEntries.FindAsync(id);
            _context.CommisionEntries.Remove(commisionEntry);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool CommisionEntryExists(int id)
        {
            return _context.CommisionEntries.Any(e => e.Id == id);
        }
    }
}
