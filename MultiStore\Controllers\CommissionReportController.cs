﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class CommissionReportController : Controller
    {
        private readonly Data _context;

        public CommissionReportController(Data context)
        {
            _context = context;
        }


        public IActionResult Index()
        {
            CommisionReportSelectViewModel reportViewModel = new();
            reportViewModel.Companies = _context.Companies.ToList();
            reportViewModel.Stores = _context.Stores.ToList();

            return View(reportViewModel);
        }

        [HttpPost]
        public IActionResult Submit(CommisionReportSelectViewModel model)
        {
            bool newMode = false;

            try
            {
                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;
                CommisionReportDisplayViewModel c = new();

                if (model.StartDate != null && model.EndDate != null)
                {
                    if (model.StartDate > model.EndDate)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDate;
                        endDate = (DateTime)model.EndDate;

                        newMode = true;
                    }
                }
                else if (model.SingleDate != null)
                {


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {
                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDate.Value.AddMonths(-2);
                            endDate = model.SingleDate.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }

                }

                if (model.SelectedStoreId != 0)
                {

                    var comissionEntries = _context.CommisionEntries
                         .Include(p => p.Supplier)
                      .Where(p => p.StoreId == model.SelectedStoreId && p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                      .ToList();

                    c.StartDate = startDate;
                    c.EndDate = endDate;

                    c.StoreName = _context.Stores
                     .Where(s => s.Id == model.SelectedStoreId)
                     .FirstOrDefault().Name;

                    c.CompnayName = _context.Companies
                     .Where(c => c.Id == _context.Stores
                     .Where(s => s.Id == model.SelectedStoreId)
                     .FirstOrDefault().CompanyId)
                     .FirstOrDefault().Name;

                    List<CommisionEntryDTO> comissionEntryDtos = new List<CommisionEntryDTO>();

                    foreach (var item in comissionEntries)
                    {
                        comissionEntryDtos.Add(new CommisionEntryDTO()
                        {
                            StoreName = c.StoreName
                            ,
                            Date = item.Date
                            ,
                             InvoiceNumber = item.InvoiceNumber
                            
                            ,
                            SupplierName = item.Supplier.Name
                            ,
                            Gross = item.Gross,
                            Vat = item.Vat
                            ,
                            Net = item.Net,

                            Type = item.CommisionType.ToString()
                        });
                    }


                    c.CommisionEntries = comissionEntryDtos;



                }
                else if (model.SelectedCompanyId != 0)
                {

                    List<CommisionEntry> commissionEntries = new();

                    var stores = _context.Stores.Where(s => s.CompanyId == model.SelectedCompanyId)
                        .ToList();

                    foreach (var store in stores)
                    {
                        commissionEntries.AddRange(_context.CommisionEntries
                        .Include(p => p.Supplier)
                     .Where(p => p.StoreId == store.Id && p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                        .ToList());

                    }



                    c.StartDate = startDate;
                    c.EndDate = endDate;

                    c.StoreName = "All";
                    c.CompnayName = _context.Companies.FirstOrDefault(c => c.Id == model.SelectedCompanyId).Name;


                    List<CommisionEntryDTO> commissionEntryDtos = new List<CommisionEntryDTO>();

                    foreach (var item in commissionEntries)
                    {
                        commissionEntryDtos.Add(new CommisionEntryDTO()
                        {
                            StoreName = c.StoreName
                            ,
                            InvoiceNumber = item.InvoiceNumber
                            ,
                            Date = item.Date
                            ,
                            SupplierName = item.Supplier.Name
                            ,
                            Gross = item.Gross,
                            Vat = item.Vat
                            ,
                            Net = item.Net,
                            Type = item.CommisionType.ToString()
                        });
                    }


                    c.CommisionEntries = commissionEntryDtos;


                }
                else //both compnay id and store id =0 
                {
                    List<CommisionEntry> commisionEntries = new();


                    commisionEntries.AddRange(_context.CommisionEntries
                        .Include(p => p.Supplier).Include(p => p.Store)
                        .Where(p => p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                        .ToList());




                    c.StartDate = startDate;
                    c.EndDate = endDate;

                    c.StoreName = "All";
                    c.CompnayName = "All";

                    List<CommisionEntryDTO> commissionEntryDtos = new List<CommisionEntryDTO>();

                    foreach (var item in commisionEntries)
                    {
                        commissionEntryDtos.Add(new CommisionEntryDTO()
                        {
                            StoreName = item.Store.Name
                            ,
                            Date = item.Date
                            ,
                            InvoiceNumber = item.InvoiceNumber
                             ,
                            SupplierName = item.Supplier.Name
                            ,
                            Gross = item.Gross,
                            Vat = item.Vat
                            ,
                            Net = item.Net,
                            Type = item.CommisionType.ToString()
                        });
                    }


                    c.CommisionEntries = commissionEntryDtos;


                }

                c.TransactionType = model.TransactionType;


                if (model.TransactionType == "income")
                {
                    c.CommisionEntries = c.CommisionEntries.Where(c => c.Type == "Income").ToList();
                }
                else if (model.TransactionType == "outcome")
                {
                    c.CommisionEntries = c.CommisionEntries.Where(c => c.Type == "Outcome").ToList();
                }



                return View("CommissionReport", c);


            }
            catch (Exception ex)
            {

                return View("Index");
            }
        }

        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }

    }
}
