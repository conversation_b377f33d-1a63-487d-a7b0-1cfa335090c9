﻿@model LoginViewModel

<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login</title>
    <link rel="stylesheet"
          href="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/css/bootstrap.min.css"
          integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T"
          crossorigin="anonymous" />
    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
            integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
            crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.14.7/dist/umd/popper.min.js"
            integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1"
            crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/js/bootstrap.min.js"
            integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM"
            crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.17.0/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validation-unobtrusive/3.2.11/jquery.validate.unobtrusive.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <style>
        body {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f8f9fa;
        }

        .login-container {
            width: 100%;
            max-width: 400px; /* Adjust the max-width as needed */
            background: #ffffff;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

            .login-container h2 {
                margin-bottom: 1.5rem;
                font-size: 1.5rem;
                text-align: center;
            }

            .login-container .form-group label {
                font-weight: bold;
            }

            .login-container .form-control {
                border-radius: 0.25rem;
            }

                .login-container .form-control:focus {
                    box-shadow: none;
                    border-color: #007bff;
                }

            .login-container .btn-primary {
                width: 100%;
                padding: 0.75rem;
                border-radius: 0.25rem;
            }

            .login-container .form-group {
                position: relative;
            }

                .login-container .form-group .fa {
                    position: absolute;
                    top: 50%;
                    left: 10px;
                    transform: translateY(-50%);
                    color: #007bff;
                }

                .login-container .form-group input {
                    padding-left: 2.5rem; /* Space for the icon */
                }
    </style>

</head>

<body>

    <div class="login-container">
        <div class="text-center mb-4">
            <img src="~/images/fusion.png" alt="Logo" class="img-fluid" style="max-width: 150px;">
        </div>
        
        <form asp-action="Login" method="post">
            <div asp-validation-summary="All" class="text-danger"></div>

            <div class="form-group">
                
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                    </div>
                    <input asp-for="UserName" class="form-control" placeholder="Username" />
                </div>
                <span asp-validation-for="UserName" class="text-danger"></span>
            </div>

            <div class="form-group">
                
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    </div>
                    <input asp-for="Password" type="password" class="form-control" placeholder="Password" />
                </div>
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>

            <button type="submit" class="btn btn-primary">Login</button>
        </form>
    </div>
</body>
</html>
