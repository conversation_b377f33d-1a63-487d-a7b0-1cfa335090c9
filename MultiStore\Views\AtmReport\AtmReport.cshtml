﻿@model IEnumerable<MultiStore.Controllers.AtmReportController.AtmModel>

@{
    ViewData["Title"] = "ATM Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed

    var totaldeposit = Model.Sum(item => item.deposit);
    var totalwithdraw = Model.Sum(item => item.withdraw);

    // Declare variables outside the loop for scope
    var currentStore = string.Empty;
    var storeDepositTotal = 0.0m;
    var storeWithdrawTotal = 0.0m;
}

<h1>ATM Report</h1>

<div>
    <p><strong>Store:</strong> @ViewData["StoreName"]</p>
    <p><strong>Company:</strong> @ViewData["CompanyName"]</p>
    <p><strong>From:</strong> @ViewData["StartDate"] <strong>To:</strong> @ViewData["EndDate"]</p>
</div>

<table class="table">
    <thead>
        <tr>
            <th>Store</th>
            <th>Open</th>
            <th>Deposit</th>
            <th>Withdraw</th>
            <th>Close</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            // Check if we are on a new store
            if (currentStore != item.StoreName)
            {
                // If not the first store, print the subtotal row
                if (!string.IsNullOrEmpty(currentStore))
                {
                    <tr>
                        <td colspan="2"><strong>Subtotal for @currentStore</strong></td>
                        <td><strong>@storeDepositTotal.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
                        <td><strong>@storeWithdrawTotal.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
                        <td></td>
                    </tr>

                    // Reset subtotal variables for the next store
                    storeDepositTotal = 0.0m;
                    storeWithdrawTotal = 0.0m;
                }

                // Update the current store name
                currentStore = item.StoreName;
            }

            // Add to the current store's subtotal
            storeDepositTotal += item.deposit;
            storeWithdrawTotal += item.withdraw;

            <tr>
                <td>@item?.StoreName</td>
                <td>@item?.open</td>
                <td>@item?.deposit</td>
                <td>@item?.withdraw</td>
                <td>@item?.close</td>
            </tr>
        }

        
        {
        <tr>
            <td colspan="2"><strong>Subtotal for @currentStore</strong></td>
            <td><strong>@storeDepositTotal.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
            <td><strong>@storeWithdrawTotal.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
            <td></td>
        </tr>
        }

        
        <tr>
            <td colspan="2"><strong>Total</strong></td>
            <td><strong>@totaldeposit.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
            <td><strong>@totalwithdraw.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
            <td></td>
        </tr>
    </tbody>
</table>
