﻿@model MultiStore.Models.BankPaymentsViewModel

@{
    ViewData["Title"] = "Bank Payments";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Bank Payments</h1>

<form asp-action="BankPayments" method="post">
    <table class="table" id="bank-payments-table">
        <thead>
            <tr>
                <th>Store</th>
                <th>Supplier</th>
                <th>Amount</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <select class="form-control" name="bankPayments[0].StoreId">
                        <option value="">Select Store</option>
                        @foreach (var store in Model.Stores)
                        {
                            <option value="@store.Id">@store.Name</option>
                        }
                    </select>
                </td>
                <td>
                    <select class="form-control" name="bankPayments[0].SupplierId">
                        <option value="">Select Supplier</option>
                        @foreach (var supplier in Model.Suppliers)
                        {
                            <option value="@supplier.Id">@supplier.Name</option>
                        }
                    </select>
                </td>
                <td>
                    <input type="number" step="0.01" class="form-control" name="bankPayments[0].Amount" />
                </td>
                <td>
                    <button type="button" class="btn btn-danger remove-row">Remove</button>
                </td>
            </tr>
        </tbody>
    </table>

    <input type="date" name="recordDate" value="@DateTime.Now.ToString("yyyy-MM-dd")" />

    <button type="button" class="btn btn-primary" id="add-row">Add Row</button>
    <input type="submit" value="Submit" class="btn btn-success" />
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        $(document).ready(function () {
            var rowIdx = 1;

            $('#add-row').click(function () {
                var newRow = `<tr>
                                        <td>
                                            <select class="form-control" name="bankPayments[${rowIdx}].StoreId">
                                                <option value="">Select Store</option>
        @foreach (var store in Model.Stores)
        {
                                                            <option value="@store.Id">@store.Name</option>
        }
                                            </select>
                                        </td>
                                        <td>
                                            <select class="form-control" name="bankPayments[${rowIdx}].SupplierId">
                                                <option value="">Select Supplier</option>
        @foreach (var supplier in Model.Suppliers)
        {
                                                            <option value="@supplier.Id">@supplier.Name</option>
        }
                                            </select>
                                        </td>
                                        <td>
                                            <input type="number" step="0.01" class="form-control" name="bankPayments[${rowIdx}].Amount" />
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-danger remove-row">Remove</button>
                                        </td>
                                    </tr>`;
                $('#bank-payments-table tbody').append(newRow);
                rowIdx++;
            });

            $('#bank-payments-table').on('click', '.remove-row', function () {
                $(this).closest('tr').remove();
            });
        });
    </script>
}
