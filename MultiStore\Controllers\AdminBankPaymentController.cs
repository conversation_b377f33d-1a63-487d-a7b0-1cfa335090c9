﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class AdminBankPaymentController : Controller
    {
        private readonly Data _context;

        public AdminBankPaymentController(Data context)
        {
            _context = context;
        }

        // GET: AdminBankPayment
        public async Task<IActionResult> Index()
        {
            var bankPayments = _context.BankPayments
                .Include(b => b.Store)
                .Include(b => b.Supplier);
            return View(await bankPayments.ToListAsync());
        }

        // GET: AdminBankPayment/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var bankPayment = await _context.BankPayments.FindAsync(id);
            if (bankPayment == null)
            {
                return NotFound();
            }

            ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", bankPayment.StoreId);
            ViewData["SupplierId"] = new SelectList(_context.Suppliers, "Id", "Name", bankPayment.SupplierId);

            return View(bankPayment);
        }

        // POST: AdminBankPayment/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,StoreId,SupplierId,Amount,Date")] BankPayment bankPayment)
        {
            if (id != bankPayment.Id)
            {
                return NotFound();
            }

            //   if (ModelState.IsValid)
            //   {
            try
            {
                _context.Update(bankPayment);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!BankPaymentExists(bankPayment.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Index));
            //   }

            //   ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", bankPayment.StoreId);
            //   ViewData["SupplierId"] = new SelectList(_context.Suppliers, "Id", "Name", bankPayment.SupplierId);

            //   return View(bankPayment);
        }

        // GET: AdminBankPayment/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var bankPayment = await _context.BankPayments
                .Include(b => b.Store)
                .Include(b => b.Supplier)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (bankPayment == null)
            {
                return NotFound();
            }

            return View(bankPayment);
        }

        // POST: AdminBankPayment/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var bankPayment = await _context.BankPayments.FindAsync(id);
            _context.BankPayments.Remove(bankPayment);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool BankPaymentExists(int id)
        {
            return _context.BankPayments.Any(e => e.Id == id);
        }
    }
}
