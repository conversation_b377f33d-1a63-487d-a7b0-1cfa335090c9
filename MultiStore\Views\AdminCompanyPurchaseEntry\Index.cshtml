﻿@model IEnumerable<MultiStore.Persistance.CompanyPurchaseEntry>

@{
    ViewData["Title"] = "Company Purchase Entries";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Company Purchase Entries</h1>


<table class="table">
    <thead>
        <tr>
            <th>Company</th>
            <th>Invoice Number</th>
            <th>Supplier</th>
            <th>Gross</th>
            <th>Vat</th>
            <th>Net</th>
            <th>Date</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.Company.Name</td>
                <td>@item.InvoiceNumber</td>

                <td>@item.Supplier.Name</td>
                <td>@item.Gross</td>
                <td>@item.Vat</td>
                <td>@item.Net</td>
                <td>@item.Date.ToShortDateString()</td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@item.Id">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
