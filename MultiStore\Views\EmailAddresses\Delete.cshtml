﻿@model MultiStore.Persistance.EmailAddress

@{
    ViewData["Title"] = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>EmailAddress</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Address)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Address)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Enabled)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Enabled)
        </dd>
    </dl>

    <form asp-action="DeleteConfirmed">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
