﻿using MultiStore.Persistance;

namespace MultiStore.Models
{
    public class DaysProductSaleViewModal
    {
        public List<DaysProduct> DaysProducts { get; set; }

        public int StoreId { get; set; }


        public List<ProductType> ProductTypes { get; set; }
    }

    public class DaysFuelSaleViewModal
    {
        public List<DaysFuel> DaysFuels { get; set; }

        public int StoreId { get; set; }


        public List<FuelType> FuelTypes { get; set; }
    }



        public class DaysServiceSaleViewModal
        {
            public List<DaysService> DaysServices { get; set; }

            public int StoreId { get; set; }


            public List<ServiceType> ServiceTypes { get; set; }
        }
}
