﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;
using MultiStore.Models;
using System.Linq;
using System.Threading.Tasks;

namespace MultiStore.Controllers
{
    public class AdminStoreMgrPayOutController : Controller
    {
        private readonly Data _context;
        private readonly IUtilityService _utilityService;


        public AdminStoreMgrPayOutController(Data context, IUtilityService utilityService)
        {
            _context = context;
            _utilityService = utilityService;
        }

        public async Task<IActionResult> Index(DateTime startDate, DateTime endDate)
        {
            var storeMgrPayOuts = await _context.StoreMgrPayOuts
                .Include(s => s.Store)
                .Where(s => s.Date.Date >= startDate && s.Date.Date <= endDate)
                .ToListAsync();
            return View(storeMgrPayOuts);
        }

        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeMgrPayOut = await _context.StoreMgrPayOuts.FindAsync(id);
            if (storeMgrPayOut == null)
            {
                return NotFound();
            }

            ViewData["Stores"] = new SelectList(_context.Stores, "Id", "Name", storeMgrPayOut.StoreId);
            return View(storeMgrPayOut);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, StoreMgrPayOut storeMgrPayOut)
        {
            if (id != storeMgrPayOut.Id)
            {
                return NotFound();
            }

            //   if (ModelState.IsValid)
            //   {
            try
            {
                _context.Update(storeMgrPayOut);
                await _context.SaveChangesAsync();
                _utilityService.UpdateStoreDaysToReflectChanges(storeMgrPayOut.StoreId);

            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StoreMgrPayOutExists(storeMgrPayOut.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Index), "AdminStoreSales");

            // }

            //  ViewData["Stores"] = new SelectList(_context.Stores, "Id", "Name", storeMgrPayOut.StoreId);
            //  return View(storeMgrPayOut);
        }

        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeMgrPayOut = await _context.StoreMgrPayOuts
                .Include(s => s.Store)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (storeMgrPayOut == null)
            {
                return NotFound();
            }

            return View(storeMgrPayOut);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var storeMgrPayOut = await _context.StoreMgrPayOuts.FindAsync(id);
            _context.StoreMgrPayOuts.Remove(storeMgrPayOut);
            await _context.SaveChangesAsync();
           _utilityService. UpdateStoreDaysToReflectChanges(storeMgrPayOut.StoreId);

            return RedirectToAction(nameof(Index), "AdminStoreSales");

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteAll()
        {
            var allRecords = _context.StoreMgrPayOuts.ToList();
            _context.StoreMgrPayOuts.RemoveRange(allRecords);
            await _context.SaveChangesAsync();

            return RedirectToAction(nameof(Index));
        }

        private bool StoreMgrPayOutExists(int id)
        {
            return _context.StoreMgrPayOuts.Any(e => e.Id == id);
        }


    }
}
