﻿using MultiStore.Persistance;

namespace MultiStore.Models
{
    public class SaleReportSelectViewModal
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? SingleDate { get; set; }
        public string Period { get; set; }

        public List<Store> Stores { get; set; }

        public List<Company> Companies { get; set; }

        public int SelectedStoreId { get; set; }
        public int SelectedCompanyId { get; set; }

    }

    public class SaleReportDisplayViewModal
    {

        public string CompnayName { get; set; }

        public string StoreName { get; set; }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public List<SaleReportDTO> salesDtos { get; set; }

        public decimal TotalSales { get; set; }

        public decimal TotalSpendings { get; set; }


    }


    public class SaleReportDTO
    {
        public string StoreName { get; set; }

        public string Date { get; set; }


        public Dictionary<string, decimal> ProductCatagoriesSale { get; set; } = new Dictionary<string, decimal>();


        public decimal FuelSale { get; set; }


        public Dictionary<string, decimal> ServiceCatagoriesSale { get; set; } = new Dictionary<string, decimal>();


        public decimal ScratchCardsSale { get; set; }

        public decimal GeneralPayout { get; set; }

        public decimal SpecificPayout { get; set; }

        public decimal DaysSale { get; set; }

        public decimal DaysSpendings { get; set; }
    }

}
