﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MultiStore.Migrations
{
    public partial class migration1300 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AtmRemark",
                table: "StoreDays",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BankDepositRemark",
                table: "StoreDays",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PaidInRemark",
                table: "StoreDays",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AtmRemark",
                table: "StoreDays");

            migrationBuilder.DropColumn(
                name: "BankDepositRemark",
                table: "StoreDays");

            migrationBuilder.DropColumn(
                name: "PaidInRemark",
                table: "StoreDays");
        }
    }
}
