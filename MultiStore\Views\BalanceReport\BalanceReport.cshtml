﻿@model IEnumerable<MultiStore.Persistance.StoreDay>

@{
    ViewData["Title"] = "Balance Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed


    // Group data by store and calculate totals
    var storeGroups = Model
        .GroupBy(item => item.Store)
        .Select(g => new
        {
            Store = g.Key,
            TotalPaidIn = g.Sum(item => item.PaidIn),
            TotalBankDeposit = g.Sum(item => item.BankDeposit),
            TotalAtm = g.Sum(item => item.Atm),
            CashInHand = g.OrderByDescending(item => item.Day).FirstOrDefault()?.CashInHand ?? 0,
            StoreTransferAmount = g.Sum(item => item.StoreTransferAmount)
        })
        .ToList();

    // Calculate overall totals
    var totalPaidIn = storeGroups.Sum(g => g.TotalPaidIn);
    var totalBankDeposit = storeGroups.Sum(g => g.TotalBankDeposit);
    var totalAtm = storeGroups.Sum(g => g.TotalAtm);
    var cashInHand = storeGroups.LastOrDefault()?.CashInHand ?? 0;
    var totalStoreTransferAmount = storeGroups.Sum(g => g.StoreTransferAmount);
}

<h1>Balance Report</h1>

<div>
    <p><strong>Store:</strong> @ViewData["StoreName"]</p>
    <p><strong>Company:</strong> @ViewData["CompanyName"]</p>
    <p><strong>From:</strong> @ViewData["StartDate"] <strong>To:</strong> @ViewData["EndDate"]</p>
</div>

<table class="table">
    <thead>
        <tr>
            <th>
                Store
            </th>
            <th>
                Paid In
            </th>
            <th>
                Bank Deposit
            </th>
            <th>
                Atm
            </th>
            <th>
                Cash in Store
            </th>
            <th>
                Store Transfer Amount
            </th>
        </tr>
    </thead>
    <tbody>
        @foreach (var group in storeGroups)
        {
            <tr>
                <td>
                    @group.Store.Name
                </td>
                <td>
                    @group.TotalPaidIn.ToString("C", new System.Globalization.CultureInfo("en-GB"))
                </td>
                <td>
                    @group.TotalBankDeposit.ToString("C", new System.Globalization.CultureInfo("en-GB"))
                </td>
                <td>
                    @group.TotalAtm.ToString("C", new System.Globalization.CultureInfo("en-GB"))
                </td>
                <td>
                    @group.CashInHand.ToString("C", new System.Globalization.CultureInfo("en-GB"))
                </td>
                <td>
                    @group.StoreTransferAmount.ToString("C", new System.Globalization.CultureInfo("en-GB"))
                </td>
            </tr>
        }
        <tr>
            <td><strong>Total:</strong></td>
            <td><strong>@totalPaidIn.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
            <td><strong>@totalBankDeposit.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
            <td><strong>@totalAtm.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
            <td><strong>@cashInHand.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
            <td><strong>@totalStoreTransferAmount.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>

        </tr>
    </tbody>
</table>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.getElementById("deleteAllButton").addEventListener("click", function () {
            if (confirm("Are you sure you want to delete all records?")) {
                fetch('@Url.Action("DeleteAll", "AdminStoreFuelSales")', {
                    method: 'POST',
                    headers: {
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    }
                }).then(response => {
                    if (response.ok) {
                        window.location.href = '@Url.Action("Index", "AdminStoreFuelSales")';
                    } else {
                        alert('Failed to delete all records.');
                    }
                }).catch(error => {
                    alert('Error: ' + error);
                });
            }
        });
    </script>
}
