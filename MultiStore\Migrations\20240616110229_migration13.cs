﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MultiStore.Migrations
{
    public partial class migration13 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Passowrd",
                table: "StoreManagers",
                newName: "Password");

            migrationBuilder.RenameColumn(
                name: "Passowrd",
                table: "BackOffices",
                newName: "Password");

            migrationBuilder.RenameColumn(
                name: "Passowrd",
                table: "Admins",
                newName: "Password");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Password",
                table: "StoreManagers",
                newName: "Passowrd");

            migrationBuilder.RenameColumn(
                name: "Password",
                table: "BackOffices",
                newName: "Passowrd");

            migrationBuilder.RenameColumn(
                name: "Password",
                table: "Admins",
                newName: "Passowrd");
        }
    }
}
