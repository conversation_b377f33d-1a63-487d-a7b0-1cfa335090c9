﻿@model MultiStore.Persistance.PaymentCard

@{
    ViewData["Title"] = "Edit Payment Card";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Edit Payment Card</h1>

<h4>Payment Card</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="CardTypeId" class="control-label"></label>
                <select asp-for="CardTypeId" class="form-control" asp-items="ViewBag.CardTypeId"></select>
            </div>

            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
