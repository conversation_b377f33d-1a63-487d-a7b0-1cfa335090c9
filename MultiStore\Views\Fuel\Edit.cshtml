﻿@model MultiStore.Persistance.Fuel

@{
    ViewData["Title"] = "Edit Fuel";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Edit Fuel</h1>

<h4>Fuel</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="FuelTypeId" class="control-label">Fuel Type</label>
                <select asp-for="FuelTypeId" class="form-control" asp-items="ViewBag.FuelTypeId"></select>
            </div>
            <div class="form-group">
                <label asp-for="VatPercentageId" class="control-label">VAT Percentage</label>
                <select asp-for="VatPercentageId" class="form-control" asp-items="ViewBag.VatPercentageId"></select>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

@if (ViewData["Message"] != null)
{
    <div class="alert alert-danger">
        @ViewData["Message"]
    </div>
}

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
