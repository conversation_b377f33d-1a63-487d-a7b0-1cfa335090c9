﻿@model MultiStore.Persistance.Supplier

@{
    ViewData["Title"] = "Edit Supplier";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Edit Supplier</h1>

<h4>Supplier</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <input type="hidden" asp-for="Id" />
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
}
