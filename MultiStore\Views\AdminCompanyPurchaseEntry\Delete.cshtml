﻿@model MultiStore.Persistance.CompanyPurchaseEntry

@{
    ViewData["Title"] = "Delete Company Purchase Entry";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Delete Company Purchase Entry</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Company Purchase Entry</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            Company
        </dt>
        <dd class="col-sm-10">
            @Model.Company.Name
        </dd>
        <dt class="col-sm-2">
            Supplier
        </dt>
        <dd class="col-sm-10">
            @Model.Supplier.Name
        </dd>
        <dt class="col-sm-2">
            Gross
        </dt>
        <dd class="col-sm-10">
            @Model.Gross
        </dd>
        <dt class="col-sm-2">
            Vat
        </dt>
        <dd class="col-sm-10">
            @Model.Vat
        </dd>
        <dt class="col-sm-2">
            Net
        </dt>
        <dd class="col-sm-10">
            @Model.Net
        </dd>
        <dt class="col-sm-2">
            Date
        </dt>
        <dd class="col-sm-10">
            @Model.Date.ToShortDateString()
        </dd>
    </dl>

    <form asp-action="DeleteConfirmed">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
