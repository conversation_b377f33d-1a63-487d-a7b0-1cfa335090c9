﻿@model MultiStore.Persistance.Store

@{
    ViewData["Title"] = "Create";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h4>Store</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="StoreType" class="control-label"></label>
                <select asp-for="StoreType" class="form-control" asp-items="Html.GetEnumSelectList<MultiStore.Persistance.StoreType>()"></select>
                <span asp-validation-for="StoreType" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CompanyId" class="control-label"></label>
                <select asp-for="CompanyId" class="form-control" asp-items="ViewBag.CompanyId"></select>
                <span asp-validation-for="CompanyId" class="text-danger"></span>
            </div>

            <td>
                <label>Atm Initial Open Value</label>
                <input type="number" step="0.01" class="form-control" asp-for="AtmInitialOpenValue" />
            </td>

            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>
