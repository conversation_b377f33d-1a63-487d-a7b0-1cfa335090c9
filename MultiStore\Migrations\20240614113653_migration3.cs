﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MultiStore.Migrations
{
    public partial class migration3 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StoreService_Service_ProductId",
                table: "StoreService");

            migrationBuilder.DropForeignKey(
                name: "FK_StoreService_Stores_StoreServiceId",
                table: "StoreService");

            migrationBuilder.DropPrimaryKey(
                name: "PK_StoreService",
                table: "StoreService");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Service",
                table: "Service");

            migrationBuilder.RenameTable(
                name: "StoreService",
                newName: "StoreServices");

            migrationBuilder.RenameTable(
                name: "Service",
                newName: "Services");

            migrationBuilder.RenameIndex(
                name: "IX_StoreService_ProductId",
                table: "StoreServices",
                newName: "IX_StoreServices_ProductId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_StoreServices",
                table: "StoreServices",
                columns: new[] { "StoreServiceId", "ProductId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_Services",
                table: "Services",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_StoreServices_Services_ProductId",
                table: "StoreServices",
                column: "ProductId",
                principalTable: "Services",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_StoreServices_Stores_StoreServiceId",
                table: "StoreServices",
                column: "StoreServiceId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StoreServices_Services_ProductId",
                table: "StoreServices");

            migrationBuilder.DropForeignKey(
                name: "FK_StoreServices_Stores_StoreServiceId",
                table: "StoreServices");

            migrationBuilder.DropPrimaryKey(
                name: "PK_StoreServices",
                table: "StoreServices");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Services",
                table: "Services");

            migrationBuilder.RenameTable(
                name: "StoreServices",
                newName: "StoreService");

            migrationBuilder.RenameTable(
                name: "Services",
                newName: "Service");

            migrationBuilder.RenameIndex(
                name: "IX_StoreServices_ProductId",
                table: "StoreService",
                newName: "IX_StoreService_ProductId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_StoreService",
                table: "StoreService",
                columns: new[] { "StoreServiceId", "ProductId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_Service",
                table: "Service",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_StoreService_Service_ProductId",
                table: "StoreService",
                column: "ProductId",
                principalTable: "Service",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_StoreService_Stores_StoreServiceId",
                table: "StoreService",
                column: "StoreServiceId",
                principalTable: "Stores",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
