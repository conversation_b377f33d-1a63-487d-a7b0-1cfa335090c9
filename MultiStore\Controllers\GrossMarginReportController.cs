﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class GrossMarginReportController : Controller
    {

        private readonly Data _context;

        public GrossMarginReportController(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            ReportViewModel reportViewModel = new ReportViewModel();
            reportViewModel.Companies = _context.Companies.ToList();
            reportViewModel.Stores = _context.Stores.ToList();

            return View(reportViewModel);
        }

        [HttpPost]
        public IActionResult Submit(ReportViewModel model)
        {
            try
            {
                bool DueDate = false;

                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;
                PurchaseReportViewModel p = new();

                if (model.StartDate != null && model.EndDate != null)
                {
                    if (model.StartDate > model.EndDate)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDate;
                        endDate = (DateTime)model.EndDate;
                    }
                }
                else if (model.SingleDate != null)
                {


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {
                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDate.Value.AddMonths(-2);
                            endDate = model.SingleDate.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }

                }


                List<GrossMarginViewModel> list = new List<GrossMarginViewModel>();

                if (model.SelectedStoreId != 0)
                {
                  list =  GetGrossMarginData(model.SelectedStoreId, startDate, endDate);

                }
                else if (model.SelectedCompanyId != 0)
                {
                    var stores = _context.Stores.Where(s => s.CompanyId == model.SelectedCompanyId).ToList();

                    foreach(var store in stores)
                    {
                        list.AddRange(GetGrossMarginData(store.Id, startDate, endDate));
                    }

                }
                else //both compnay id and store id =0 
                {
                    var stores = _context.Stores.ToList();

                    foreach (var store in stores)
                    {
                        list.AddRange(GetGrossMarginData(store.Id, startDate, endDate));
                    }
                }

                ViewData["StartDate"] = startDate.ToShortDateString();
                ViewData["EndDate"] = endDate.ToShortDateString();

                ViewData["StoreName"] = (model.SelectedStoreId != 0) ? _context.Stores
                 .Where(s => s.Id == model.SelectedStoreId)
                 .FirstOrDefault().Name : "All Stores";

                ViewData["CompanyName"] = (model.SelectedCompanyId != 0) ? _context.Companies
                    .FirstOrDefault(c => c.Id == model.SelectedCompanyId).Name : "All Companies";

                return View("GrossMargin", list);

            }
            catch (Exception ex)
            {

                return View("Index");
            }
        }


        public List<GrossMarginViewModel> GetGrossMarginData(int storeId, DateTime startDate, DateTime endDate)
        {
            var store = _context.Stores.Include(s => s.Company).First(s => s.Id == storeId);
            //
            var productssales = _context.StoreProductSales.Where(s => s.Day.Date >= startDate && s.Day.Date <= endDate
            && s.StoreId == storeId).ToList();

            var serviceSales = _context.StoreServiceSales.Include(s => s.Service)
                .Where(s => s.Day.Date >= startDate && s.Day.Date <= endDate
                && s.StoreId == storeId).ToList();

            var fuelSales = _context.StoreFuelSales
                .Where(s => s.Day.Date >= startDate && s.Day.Date <= endDate
                && s.StoreId == storeId).ToList();

            var scrachcardSales = _context.StoreScratchCardSales.Include(s => s.ScratchCard)
                .Where(s => s.Day.Date >= startDate && s.Day.Date <= endDate
                && s.StoreId == storeId).ToList();

            var purchaces = _context.PurchaseEntries.Where(p => p.Date.Date >= startDate && p.Date.Date <= endDate
            && p.StoreId == storeId).ToList();


            var list = new List<GrossMarginViewModel>();

            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                string storeName = store.Name;
                decimal productSale = productssales.Where(p => p.Day.Date == date).Sum(p => p.SaleAmount);
                decimal fuelSale = fuelSales.Where(p => p.Day.Date == date).Sum(p => p.SaleAmount);

                var serviceSaleList = serviceSales.Where(p => p.Day.Date == date).ToList();

                decimal serviceSale = 0;
                foreach (var item in serviceSaleList)
                {
                    serviceSale += item.SalesCount * (item.Service.Price - item.Service.Discount);
                }

                var scracthCardSaleList = scrachcardSales.Where(p => p.Day.Date == date).ToList();

                decimal scracthCardSale = 0;
                foreach (var item in scracthCardSaleList)
                {
                    scracthCardSale += item.SalesCount * (item.ScratchCard.Price);
                }

                var salesForDay = productSale + fuelSale + serviceSale + scracthCardSale;

                var purchaseGross = purchaces.Where(p => p.Date.Date == date).Sum(p => p.Gross);
                var purchaseVat = purchaces.Where(p => p.Date.Date == date).Sum(p => p.Vat);
                var purchaseNet = purchaces.Where(p => p.Date.Date == date).Sum(p => p.Net);


                list.Add(new GrossMarginViewModel()
                {
                    Date = date,
                    FuelSales = fuelSale,
                    ProductSales = productSale,
                    PurchaseGross = purchaseGross,
                    PurchaseNet = purchaseNet,
                    PurchaseVat = purchaseVat,
                    ScratchCardSales = scracthCardSale,
                    ServiceSales = serviceSale,
                    StoreName = storeName,
                    CompnayName = store.Company.Name,
                    TotalSales = salesForDay
                });
            }

            return list;
        }

        public class GrossMarginViewModel
        {
            public DateTime Date { get; set; }
            public string StoreName { get; set; }

            public string CompnayName { get; set; }

            public decimal ProductSales { get; set; }
            public decimal FuelSales { get; set; }
            public decimal ServiceSales { get; set; }
            public decimal ScratchCardSales { get; set; }
            public decimal TotalSales { get; set; }
            public decimal PurchaseGross { get; set; }
            public decimal PurchaseVat { get; set; }
            public decimal PurchaseNet { get; set; }
        }


        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }
    }
}
