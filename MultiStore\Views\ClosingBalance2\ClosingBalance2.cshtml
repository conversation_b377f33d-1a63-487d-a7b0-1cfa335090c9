﻿@model MultiStore.Controllers.ClosingBalance2Controller.PagedList<MultiStore.Controllers.ClosingBalance2Controller.ClosingBalanceViewModel>

@{
    ViewData["Title"] = "Closing Balances";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed

    // Retrieve the date range for filtering
    var fromDate = ViewData["FromDate"] as DateTime?;
    var toDate = ViewData["ToDate"] as DateTime?;
    var currentDate = ViewData["CurrentDate"] as DateTime? ?? DateTime.Now; // Default to current date if not set
    var totalPages = Model.TotalPages;
    var currentPage = Model.CurrentPage;
}

<h2>@ViewData["Title"]</h2>

<p>Current Date: @currentDate.ToString("yyyy-MM-dd")</p>

<form method="get" action="Submit">
   @*  <div class="form-row">
        <div class="form-group col-md-3">
            <label for="fromDate">From Date</label>
            <input type="date" class="form-control" id="fromDate" name="fromDate" value="@fromDate?.ToString("yyyy-MM-dd")" />
        </div>
        <div class="form-group col-md-3">
            <label for="toDate">To Date</label>
            <input type="date" class="form-control" id="toDate" name="toDate" value="@toDate?.ToString("yyyy-MM-dd")" />
        </div>
        <div class="form-group col-md-3">
            <button type="submit" class="btn btn-primary mt-4">Filter</button>
        </div>
    </div> *@
</form>

<table class="table table-bordered table-striped">
    <thead class="thead-dark">
        <tr>
            <th>Date</th>
            <th>Store Name</th>
            <th>Paid In Remark</th>
            <th>Atm Remark</th>
            <th>Bank Deposit Remark</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model.Items)
        {
            <tr>
                <td>@item.Date.ToString("yyyy-MM-dd")</td>
                <td>@item.StoreName</td>
                <td>@item.PaidInRemark</td>
                <td>@item.AtmRemark</td>
                <td>@item.BankDepositRemark</td>
            </tr>
        }
    </tbody>
</table>


@if (totalPages > 1)
{
    <nav aria-label="Page navigation">
        <ul class="pagination">
            @{
                // Prepare parameters to maintain in pagination links
                var routeValues = new
                {
                    startDate = fromDate,
                    endDate = toDate,
                    singleDate = ViewData["SingleDate"],  // Add SingleDate if necessary
                    period = ViewData["Period"],          // Add Period if necessary
                    selectedCompanyId = ViewData["SelectedCompanyId"],
                    selectedStoreId = ViewData["SelectedStoreId"]
                };
            }

            @if (currentPage > 1)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Submit", new { page = currentPage - 1 }.Merge(routeValues))" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
            }

            @for (var i = 1; i <= totalPages; i++)
            {
                <li class="page-item @(i == currentPage ? "active" : "")">
                    <a class="page-link" href="@Url.Action("Submit", new { page = i }.Merge(routeValues))">@i</a>
                </li>
            }

            @if (currentPage < totalPages)
            {
                <li class="page-item">
                    <a class="page-link" href="@Url.Action("Submit", new { page = currentPage + 1 }.Merge(routeValues))" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            }
        </ul>
    </nav>
}

