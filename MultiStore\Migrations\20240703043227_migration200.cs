﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MultiStore.Migrations
{
    public partial class migration200 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BackOfficePayOuts");

            migrationBuilder.CreateTable(
                name: "SpecificPayOuts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    StoreId = table.Column<int>(type: "int", nullable: false),
                    PayOutPartyId = table.Column<int>(type: "int", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SpecificPayOuts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SpecificPayOuts_PayOutParties_PayOutPartyId",
                        column: x => x.PayOutPartyId,
                        principalTable: "PayOutParties",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SpecificPayOuts_Stores_StoreId",
                        column: x => x.StoreId,
                        principalTable: "Stores",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SpecificPayOuts_PayOutPartyId",
                table: "SpecificPayOuts",
                column: "PayOutPartyId");

            migrationBuilder.CreateIndex(
                name: "IX_SpecificPayOuts_StoreId",
                table: "SpecificPayOuts",
                column: "StoreId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SpecificPayOuts");

            migrationBuilder.CreateTable(
                name: "BackOfficePayOuts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PayOutPartyId = table.Column<int>(type: "int", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BackOfficePayOuts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BackOfficePayOuts_PayOutParties_PayOutPartyId",
                        column: x => x.PayOutPartyId,
                        principalTable: "PayOutParties",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BackOfficePayOuts_PayOutPartyId",
                table: "BackOfficePayOuts",
                column: "PayOutPartyId");
        }
    }
}
