﻿@model MultiStore.Persistance.StoreProductSales

@{
    ViewData["Title"] = "Delete Store Product Sales";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Delete Store Product Sales</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Store Product Sales</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            Store
        </dt>
        <dd class="col-sm-10">
            @Model.Store.Name
        </dd>
        <dt class="col-sm-2">
            Product
        </dt>
        <dd class="col-sm-10">
            @Model.Product.Name
        </dd>
        <dt class="col-sm-2">
            Sale Amount
        </dt>
        <dd class="col-sm-10">
            @Model.SaleAmount
        </dd>
        <dt class="col-sm-2">
            Day
        </dt>
        <dd class="col-sm-10">
            @Model.Day.ToShortDateString()
        </dd>
    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
