﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;
using System.Linq;
using System.Threading.Tasks;

namespace MultiStore.Controllers
{
    public class StoreManagersController : Controller
    {
        private readonly Data _context;

        public StoreManagersController(Data context)
        {
            _context = context;
        }

        // GET: StoreManagers
        public async Task<IActionResult> Index()
        {
            return View(await _context.StoreManagers.Include(sm => sm.Store).ToListAsync());
        }

        // GET: StoreManagers/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeManager = await _context.StoreManagers
                .Include(sm => sm.Store)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (storeManager == null)
            {
                return NotFound();
            }

            return View(storeManager);
        }

        // GET: StoreManagers/Create
        public IActionResult Create()
        {
            ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name");
            return View();
        }

        // POST: StoreManagers/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,FirstName,LastName,UserName,Password,StoreId")] StoreManager storeManager)
        {

            if (_context.StoreManagers.Any(b => b.UserName ==storeManager.UserName))
            {
                return RedirectToAction(nameof(Index));

            }


            //  if (ModelState.IsValid)
            // {
            _context.Add(storeManager);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
            //  }
            //  ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", storeManager.StoreId);
            //   return View(storeManager);
        }

        // GET: StoreManagers/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeManager = await _context.StoreManagers.FindAsync(id);
            if (storeManager == null)
            {
                return NotFound();
            }
            ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", storeManager.StoreId);
            return View(storeManager);
        }

        // POST: StoreManagers/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,FirstName,LastName,UserName,Password,StoreId")] StoreManager storeManager)
        {
            if (id != storeManager.Id)
            {
                return NotFound();
            }


            if (!_context.StoreManagers.Any(b => b.UserName == storeManager.UserName))
            {
                return RedirectToAction(nameof(Index));

            }

            // if (ModelState.IsValid)
            //{
            try
                {
                    _context.Update(storeManager);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!StoreManagerExists(storeManager.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
           // }
          //  ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", storeManager.StoreId);
           // return View(storeManager);
        }

        // GET: StoreManagers/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeManager = await _context.StoreManagers
                .Include(sm => sm.Store)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (storeManager == null)
            {
                return NotFound();
            }

            return View(storeManager);
        }

        // POST: StoreManagers/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var storeManager = await _context.StoreManagers.FindAsync(id);
            _context.StoreManagers.Remove(storeManager);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool StoreManagerExists(int id)
        {
            return _context.StoreManagers.Any(e => e.Id == id);
        }
    }
}
