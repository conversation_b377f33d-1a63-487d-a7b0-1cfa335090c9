﻿namespace MultiStore.Models
{
    public class WeeklyReportViewModel
    {
        public string CompanyName { get; set; }
        public string StoreName { get; set; }
        public decimal TotalSales { get; set; }
        public decimal ShopSales { get; set; }
        public decimal TotalSpendings { get; set; }
        public decimal PaidIns { get; set; }
        public decimal Atm { get; set; }
        public decimal BankDeposit { get; set; }
        public decimal StoreTransfer { get; set; }
        public decimal ClearedDeposit { get; set; }
        public decimal CardPayments { get; set; }
        public decimal BankEntry { get; set; }
        public decimal Variance { get; set; }
        public decimal CashInHand { get; set; }
        public decimal ClearedCash { get; set; }
        public decimal WeeklyPercentage { get; set; }

        public decimal YearlyClearedCash { get; set; }

        public decimal YearlyPercentage { get; set; }

    }
}
