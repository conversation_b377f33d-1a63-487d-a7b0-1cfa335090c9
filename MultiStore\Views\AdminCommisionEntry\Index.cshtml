﻿@model IEnumerable<MultiStore.Persistance.CommisionEntry>

@{
    ViewData["Title"] = "Comission Entries";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2>Comission Entries</h2>


<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Store.Name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.InvoiceNumber)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Supplier.Name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.CommisionType)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Gross)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Vat)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Net)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Date)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.Store.Name)
                </td>
                <th>
                    @Html.DisplayFor(modelItem => item.InvoiceNumber)
                </th>
                <td>
                    @Html.DisplayFor(modelItem => item.Supplier.Name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.CommisionType)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Gross)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Vat)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Net)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Date)
                </td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@item.Id">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
