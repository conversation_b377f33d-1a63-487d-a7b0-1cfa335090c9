﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using System.Diagnostics.CodeAnalysis;
using static MultiStore.Controllers.StoreDashboardController;

namespace MultiStore.Controllers
{
    [TypeFilter(typeof(BackOfficeAuthorizationFilter))]
    public class BackOfficeDashboardController : Controller
    {
        private readonly Data _context;

        public BackOfficeDashboardController(Data context)
        {
            _context = context;
        }


        public IActionResult Index()
        {
            //if admin


            return View();
        }

        [HttpGet]
        public IActionResult BankPayments()
        {
            var viewModel = new BankPaymentsViewModel
            {
                Stores = _context.Stores.ToList(),
                Suppliers = _context.Suppliers.ToList()
            };

            return View(viewModel);
        }


        [HttpPost]
        public IActionResult BankPayments(BankPayment[] bankPayments, DateTime recordDate)
        {
            foreach (var bankPayment in bankPayments)
            {
                if (bankPayment.Amount > 0 && bankPayment.SupplierId > 0 && bankPayment.StoreId > 0)
                {

                 

                    bankPayment.Date = recordDate;
                    _context.BankPayments.Add(bankPayment);
                }
            }
            _context.SaveChanges();

            return View(nameof(Index));
        }


        [HttpGet]
        public IActionResult CommissionEntries()
        {

            var viewModel = new CommisionEntryViewModel
            {
                Stores = _context.Stores.ToList(),
                Suppliers = _context.Suppliers.ToList()
            };

            return View(viewModel);
        }

        [HttpPost]
        public IActionResult CommissionEntries(CommisionEntry[] commisionEntries,DateTime recordDate)
        {
            foreach (var commisionEntry in commisionEntries)
            {
                if (commisionEntry.SupplierId > 0 && commisionEntry.StoreId > 0)
                {

                    if (_context.CommisionEntries.Any(c => c.InvoiceNumber == commisionEntry.InvoiceNumber)
                     || _context.PurchaseEntries.Any(c => c.InvoiceNumber == commisionEntry.InvoiceNumber)
                     || _context.CompanyPurchaseEntries.Any(c => c.InvoiceNumber == commisionEntry.InvoiceNumber))
                    {
                        ViewBag.ErrorMessage = "Duplicate Invoice Number Detected, Previous Record Not added to system. Please Check and Try Again.";

                        return View("Index");
                    }


                        commisionEntry.Date = recordDate;
                    _context.CommisionEntries.Add(commisionEntry);
                }
            }

           _context.SaveChanges();
            return RedirectToAction(nameof(Index));

        }


        public class BackOfficeCardPaymentViewModel
        {
            public List<Store> Stores { get; set; }

            public List<CardType> CardTypes { get; set; }

        }


        [HttpGet]
        public IActionResult BackOfficeCardPaymentEntries()
        {
            var viewModel = new BackOfficeCardPaymentViewModel
            {
                CardTypes = _context.CardTypes.ToList(),
                Stores = _context.Stores.ToList()
            };

            return View(viewModel);
        }

        [HttpPost]

        public IActionResult BackOfficeCardPaymentEntries(BackOfficeCardPaymentEntry[] BackOfficeCardPaymentEntries)
        {

            foreach (var entry in BackOfficeCardPaymentEntries)
            {
                if (entry.CardTypeId > 0 && entry.StoreId > 0 && entry.Amount > 0)
                {
                    
                    _context.BackOfficeCardPaymentEntries.Add(entry);
                }
            }


            _context.SaveChanges();
            return RedirectToAction(nameof(Index));

        }





        [HttpGet]
        public IActionResult PurchaseEntries()
        {

            var viewModel = new PurchaseEntryViewModel
            {
                Stores = _context.Stores.ToList(),
                Suppliers = _context.Suppliers.ToList()
            };

            return View(viewModel);
        }

        [HttpPost]
        public IActionResult PurchaseEntries(PurchaseEntry[] purchaseEntries)
        {
            foreach (var purchaceEntry in purchaseEntries)
            {
                if (purchaceEntry.SupplierId > 0 && purchaceEntry.StoreId > 0)
                {

                    if (_context.CommisionEntries.Any(c => c.InvoiceNumber == purchaceEntry.InvoiceNumber)
                     || _context.PurchaseEntries.Any(c => c.InvoiceNumber == purchaceEntry.InvoiceNumber)
                     || _context.CompanyPurchaseEntries.Any(c => c.InvoiceNumber == purchaceEntry.InvoiceNumber))
                    {
                        ViewBag.ErrorMessage = "Duplicate Invoice Number Detected, Previous Record Not added to system. Please Check and Try Again.";
                        return View("Index");
                    }

                 //   purchaceEntry.Date = recordDate;
                    _context.PurchaseEntries.Add(purchaceEntry);
                }
            }


            _context.SaveChanges();
            return RedirectToAction(nameof(Index));

        }

        [HttpGet]
        public IActionResult CompanyPurchaseEntries()
        {

            var viewModel = new CompanyPurchaseEntryViewModel
            {
                Companies = _context.Companies.ToList(),
                Suppliers = _context.Suppliers.ToList()
            };

            return View(viewModel);
        }

        [HttpPost]
        public IActionResult CompanyPurchaseEntries(CompanyPurchaseEntry[] purchaseEntries, DateTime recordDate)
        {


            foreach (var purchaceEntry in purchaseEntries)
            {
                if (purchaceEntry.CompanyId > 0)
                {

                    if (_context.CommisionEntries.Any(c => c.InvoiceNumber == purchaceEntry.InvoiceNumber)
                    || _context.PurchaseEntries.Any(c => c.InvoiceNumber == purchaceEntry.InvoiceNumber)
                    || _context.CompanyPurchaseEntries.Any(c => c.InvoiceNumber == purchaceEntry.InvoiceNumber))
                    {
                        ViewBag.ErrorMessage = "Duplicate Invoice Number Detected, Previous Record Not added to system. Please Check and Try Again.";

                        return View("Index");
                    }

                    purchaceEntry.Date = recordDate;
                    _context.CompanyPurchaseEntries.Add(purchaceEntry);
                }
            }


            _context.SaveChanges();
            return RedirectToAction(nameof(Index));

        }


        public IActionResult Commission()
        {
            //if admin


            return View();
        }

        public IActionResult Purchase()
        {
            //if admin


            return View();
        }
    }
}
