﻿@model MultiStore.Models.EditServicesViewModel

@{
    ViewData["Title"] = "Edit Services";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Edit Products for Store</h1>

<form asp-action="UpdateServices" method="post">
    <input type="hidden" asp-for="StoreId" />
    <input type="hidden" id="selectedServiceIds" name="selectedServiceIds" />

    <div class="row">
        <div class="col-md-6">
            <h3>Selected Services</h3>
            <select multiple class="form-control" id="selectedServices" size="10">
                @foreach (var service in Model.SelectedServices)
                {
                    <option value="@service.Id">@service.Name</option>
                }
            </select>
            <button type="button" class="btn btn-primary" id="deselectAll">Deselect All</button>
        </div>
        <div class="col-md-6">
            <h3>Non-Selected Products</h3>
            <select multiple class="form-control" id="nonSelectedServices" size="10">
                @foreach (var service in Model.NonSelectedServices)
                {
                    <option value="@service.Id">@service.Name</option>
                }
            </select>
            <button type="button" class="btn btn-primary" id="selectAll">Select All</button>
        </div>
    </div>

    <div class="form-group mt-3">
        <input type="submit" value="Save" class="btn btn-primary" />
    </div>
</form>

@section Scripts {
    <script>
        document.getElementById("deselectAll").onclick = function () {
            var selectedServices = document.getElementById("selectedServices");
            var nonSelectedServices = document.getElementById("nonSelectedServices");
            while (selectedServices.options.length > 0) {
                nonSelectedServices.appendChild(selectedServices.options[0]);
            }
        };

        document.getElementById("selectAll").onclick = function () {
            var selectedServices = document.getElementById("selectedServices");
            var nonSelectedServices = document.getElementById("nonSelectedServices");
            while (nonSelectedServices.options.length > 0) {
                selectedServices.appendChild(nonSelectedServices.options[0]);
            }
        };

        document.getElementById("selectedServices").ondblclick = function () {
            var selectedOptions = this.selectedOptions;
            var nonSelectedServices = document.getElementById("nonSelectedServices");
            for (var i = 0; i < selectedOptions.length; i++) {
                nonSelectedServices.appendChild(selectedOptions[i]);
            }
        };

        document.getElementById("nonSelectedServices").ondblclick = function () {
            var selectedOptions = this.selectedOptions;
            var selectedServices = document.getElementById("selectedServices");
            for (var i = 0; i < selectedOptions.length; i++) {
                selectedServices.appendChild(selectedOptions[i]);
            }
        };

        document.querySelector("form").onsubmit = function () {
            var selectedServiceIds = document.getElementById("selectedServiceIds");
            var selectedServices = document.getElementById("selectedServices");

            var selectedIds = [];
            for (var i = 0; i < selectedServices.options.length; i++) {
                selectedIds.push(selectedServices.options[i].value);
            }

            selectedServiceIds.value = selectedIds.join(",");
        };
    </script>
}
