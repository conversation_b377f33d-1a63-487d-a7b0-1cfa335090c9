﻿@model MultiStore.Persistance.Service

@{
    ViewData["Title"] = "Create Service";


    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>Create Service</h1>

<h4>Service</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Price" class="control-label"></label>
                <input asp-for="Price" class="form-control" />
                <span asp-validation-for="Price" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="ServiceTypeId" class="control-label">Service Type</label>
                <select asp-for="ServiceTypeId" class="form-control" asp-items="ViewBag.ServiceTypeId"></select>
                <span asp-validation-for="ServiceTypeId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="VatPercentageId" class="control-label"></label>
                <select asp-for="VatPercentageId" class="form-control" asp-items="ViewBag.VatPercentageId"></select>
            </div>
            <div class="form-group">
                <label asp-for="Discount" class="control-label"></label>
                <input asp-for="Discount" class="form-control" />
                <span asp-validation-for="Discount" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

@if (ViewData["Message"] != null)
{
    <div class="alert alert-danger">
        @ViewData["Message"]
    </div>
}


<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
