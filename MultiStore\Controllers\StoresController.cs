﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using System.Linq;

namespace MultiStore.Controllers
{
    public class StoresController : Controller
    {
        private readonly Data _context;

        public StoresController(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            return View(_context.Stores.ToList());
        
        }

        public IActionResult Deactivate(int Id)
        {
            _context.Stores.Find(Id).Active = false;
            _context.SaveChanges();


            return View("Index",_context.Stores.ToList());

        }


        public IActionResult Activate(int Id)
        {

            _context.Stores.Find(Id).Active = true;
            _context.SaveChanges();


            return View("Index",_context.Stores.ToList());

        }




        public async Task<IActionResult> EditProducts(int id)
        {
            var store = await _context.Stores
                .Include(s => s.StoreProducts)
                .ThenInclude(sp => sp.Product)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (store == null)
            {
                return NotFound();
            }

            var products = await _context.Products.ToListAsync();

            var model = new EditProductsViewModel
            {
                StoreId = id,
                SelectedProducts = store.StoreProducts.Select(sp => sp.Product).ToList(),
                NonSelectedProducts = products.Except(store.StoreProducts.Select(sp => sp.Product)).ToList()
            };

            return View(model);
        }


        public async Task<IActionResult> EditFuel(int id)
        {
            var store = await _context.Stores
                .Include(s => s.StoreFuels)
                .ThenInclude(sp => sp.Fuel)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (store == null)
            {
                return NotFound();
            }

            var fuels = await _context.Fuels.ToListAsync();

            var model = new EditFuelsViewModel
            {
                StoreId = id,
                SelectedFuels = store.StoreFuels.Select(sp => sp.Fuel).ToList(),
                NonSelectedFuels = fuels.Except(store.StoreFuels.Select(sp => sp.Fuel)).ToList()
            };

            return View(model);
        }

        public async Task<IActionResult> EditServices(int id)
        {
            var store = await _context.Stores
                .Include(s => s.StoreServices)
                .ThenInclude(sp => sp.Service)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (store == null)
            {
                return NotFound();
            }

            var services = await _context.Services.ToListAsync();

            var model = new EditServicesViewModel
            {
                StoreId = id,
                SelectedServices = store.StoreServices.Select(sp => sp.Service).ToList(),
                NonSelectedServices = services.Except(store.StoreServices.Select(sp => sp.Service)).ToList()
            };

            return View(model);
        }


        public async Task<IActionResult> EditScratchCards(int id)
        {
            var store = await _context.Stores
                .Include(s => s.StoreScratchCards)
                .ThenInclude(sp => sp.ScratchCard)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (store == null)
            {
                return NotFound();
            }

            var scratchCards = await _context.ScratchCards.ToListAsync();

            var model = new EditScratchCardsViewModel
            {
                StoreId = id,
                SelectedScratchCards = store.StoreScratchCards.Select(sp => sp.ScratchCard).ToList(),
                NonSelectedScratchCards = scratchCards.Except(store.StoreScratchCards.Select(sp => sp.ScratchCard)).ToList()
            };

            return View(model);
        }


        //
        [HttpPost]
        public async Task<IActionResult> UpdateFuels(int storeId, string selectedFuelIds)
        {
            var store = await _context.Stores
                .Include(s => s.StoreFuels)
                .FirstOrDefaultAsync(s => s.Id == storeId);

            if (store == null)
            {
                return NotFound();
            }

            var selectedFuelIdList = selectedFuelIds?.Split(',').Select(int.Parse).ToList() ?? new List<int>();

            // Remove existing products
            store.StoreFuels.Clear();

            // Add selected products
            foreach (var fuelId in selectedFuelIdList)
            {
                store.StoreFuels.Add(new StoreFuel { StoreId = storeId, FuelId = fuelId });
            }

            await _context.SaveChangesAsync();

            return RedirectToAction(nameof(Index));
        }
        [HttpPost]
        public async Task<IActionResult> UpdateProducts(int storeId, string selectedProductIds)
        {
            var store = await _context.Stores
                .Include(s => s.StoreProducts)
                .FirstOrDefaultAsync(s => s.Id == storeId);

            if (store == null)
            {
                return NotFound();
            }

            var selectedProductIdList = selectedProductIds?.Split(',').Select(int.Parse).ToList() ?? new List<int>();

            // Remove existing products
            store.StoreProducts.Clear();

            // Add selected products
            foreach (var productId in selectedProductIdList)
            {
                store.StoreProducts.Add(new StoreProduct { StoreId = storeId, ProductId = productId });
            }

            await _context.SaveChangesAsync();

            return RedirectToAction(nameof(Index));
        }

        public async Task<IActionResult> UpdateScratchCards(int storeId, string selectedScratchCardIds)
        {
            var store = await _context.Stores
                .Include(s => s.StoreScratchCards)
                .FirstOrDefaultAsync(s => s.Id == storeId);

            if (store == null)
            {
                return NotFound();
            }

            var selectedScratchCardsIdList = selectedScratchCardIds?.Split(',').Select(int.Parse).ToList() ?? new List<int>();

            // Remove existing products
            store.StoreScratchCards.Clear();

            // Add selected products
            foreach (var scratchCardId in selectedScratchCardsIdList)
            {
                store.StoreScratchCards.Add(new StoreScratchCard { StoreId = storeId, ScratchCardId = scratchCardId });
            }

            await _context.SaveChangesAsync();

            return RedirectToAction(nameof(Index));
        }


        public async Task<IActionResult> UpdateServices(int storeId, string selectedServiceIds)
        {
            var store = await _context.Stores
                .Include(s => s.StoreServices)
                .FirstOrDefaultAsync(s => s.Id == storeId);

            if (store == null)
            {
                return NotFound();
            }

            var selectedServiceIdList = selectedServiceIds?.Split(',').Select(int.Parse).ToList() ?? new List<int>();

            // Remove existing products
            store.StoreServices.Clear();

            // Add selected products
            foreach (var serviceId in selectedServiceIdList)
            {
                store.StoreServices.Add(new StoreService { StoreId = storeId, ServiceId = serviceId });
            }

            await _context.SaveChangesAsync();

            return RedirectToAction(nameof(Index));
        }


        // GET: Stores/Create
        public IActionResult Create()
        {
            ViewData["CompanyId"] = new SelectList(_context.Companies, "Id", "Name");
            return View();
        }

        // POST: Stores/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Name,StoreType,CompanyId,AtmInitialOpenValue")] Store store)
        {
           // if (ModelState.IsValid)
           // {
                 store.Active = true;
                _context.Add(store);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
          //  }
          //  ViewData["CompanyId"] = new SelectList(_context.Companies, "Id", "Name", store.CompanyId);
          // return View(store);
        }


        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var store = await _context.Stores
                .Include(s => s.EmailAddresses)  // Include EmailAddresses to fetch related email addresses
                .FirstOrDefaultAsync(m => m.Id == id);

            if (store == null)
            {
                return NotFound();
            }

            // Pass email addresses to the view using ViewBag
            ViewBag.EmailAddresses = store.EmailAddresses.Select(e => e.Address).ToList();
            ViewData["CompanyId"] = new SelectList(_context.Companies, "Id", "Name", store.CompanyId);

            return View(store);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,StoreType,CompanyId,AtmInitialOpenValue")] Store store, List<string> emailAddresses)
        {
            if (id != store.Id)
            {
                return NotFound();
            }

          //  if (ModelState.IsValid)
          //  {
                try
                {
                    // Update store
                    _context.Update(store);

                    // Update email addresses
                    var existingEmails = _context.EmailAddresses.Where(e => e.Store.Id == store.Id).ToList();
                    _context.EmailAddresses.RemoveRange(existingEmails); // Remove existing email addresses

                    foreach (var email in emailAddresses)
                    {
                        if (!string.IsNullOrWhiteSpace(email))
                        {
                            _context.EmailAddresses.Add(new EmailAddress { Address = email, Store = store });
                        }
                    }

                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!StoreExists(store.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
         //   }

            // If model state is invalid, reload the email addresses to maintain state
            ViewBag.EmailAddresses = emailAddresses ?? new List<string>();
            ViewData["CompanyId"] = new SelectList(_context.Companies, "Id", "Name", store.CompanyId);

            return View(store);
        }



        // Existing methods...

        private bool StoreExists(int id)
        {
            return _context.Stores.Any(e => e.Id == id);
        }

        // GET: PaymentCard/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var store = await _context.Stores
                .FirstOrDefaultAsync(m => m.Id == id);
            if (store == null)
            {
                return NotFound();
            }

            return View(store);
        }

        // POST: PaymentCard/Delete/5
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var store = await _context.Stores.FindAsync(id);
                _context.Stores.Remove(store);
                await _context.SaveChangesAsync();
            }
            catch(Exception ex)
            { 
            
            }

            return RedirectToAction(nameof(Index));
        }

    }

}
