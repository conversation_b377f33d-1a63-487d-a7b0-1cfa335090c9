﻿@model MultiStore.Persistance.StoreDay 

@{
    ViewData["Title"] = "Store Balance Inputs Delete";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h2>Are you sure you want to delete this record?</h2>

<div>
    <h4>StoreDay</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            Day
        </dt>
        <dd class="col-sm-10">
            @Model.Day.ToShortDateString()
        </dd>
        <dt class="col-sm-2">
            Store
        </dt>
        <dd class="col-sm-10">
            @Model.Store.Name
        </dd>
        <dt class="col-sm-2">
            ATM
        </dt>
        <dd class="col-sm-10">
            @Model.Atm
        </dd>
        <dt class="col-sm-2">
            Bank Deposit
        </dt>
        <dd class="col-sm-10">
            @Model.BankDeposit
        </dd>
        <dt class="col-sm-2">
            Paid In
        </dt>
        <dd class="col-sm-10">
            @Model.PaidIn
        </dd>
    </dl>

    <form asp-action="DeleteConfirmed">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index" class="btn btn-secondary">Back to List</a>
    </form>
</div>
