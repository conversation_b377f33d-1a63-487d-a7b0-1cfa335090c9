﻿@model MultiStore.Models.CompnayPurchaseReportViewModel

@{
    ViewData["Title"] = "Purchase Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed
                                                     // Update the layout path as needed

}

<h2>Purchase Report</h2>

<div>
    <p><strong>Company:</strong> @Model.CompnayName</p>
    <p><strong>Store:</strong> @Model.StoreName</p>
    <p><strong>From:</strong> @Model.StartDate?.ToString("MM/dd/yyyy") <strong>To:</strong> @Model.EndDate?.ToString("MM/dd/yyyy")</p>
</div>

<table class="table table-bordered">
    <thead>
        <tr>
            <th>Date</th>
            <th>Company Name</th>
            <th>Invoice Number</th>
            <th>Supplier Name</th>
            <th>Gross</th>
            <th>Vat</th>
            <th>Net</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var entry in Model.PurchaseEntries)
        {
            <tr>
                <td>@entry.Date.ToString("MM/dd/yyyy")</td>
                <td>@entry.CompnayName</td>
                <td>@entry.InvoiceNumber</td>
                <td>@entry.SupplierName</td>
                <td>@entry.Gross.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@entry.Vat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@entry.Net.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            </tr>
        }
    </tbody>
    <tfoot>
        <tr>
            <td colspan="3" class="text-right"><strong>Total:</strong></td>
            <td>@Model.PurchaseEntries.Sum(e => e.Gross).ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            <td>@Model.PurchaseEntries.Sum(e => e.Vat).ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            <td>@Model.PurchaseEntries.Sum(e => e.Net).ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
        </tr>
    </tfoot>
</table>
