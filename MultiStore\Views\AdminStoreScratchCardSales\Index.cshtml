﻿@model IEnumerable<MultiStore.Persistance.StoreScratchCardSales>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2>Index</h2>


<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Store.Name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.ScratchCard.Name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.SalesCount)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.CloseCount)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.ActivationCount)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Day)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.Store.Name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.ScratchCard.Name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.SalesCount)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.CloseCount)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.ActivationCount)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Day)
                </td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@item.Id">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
