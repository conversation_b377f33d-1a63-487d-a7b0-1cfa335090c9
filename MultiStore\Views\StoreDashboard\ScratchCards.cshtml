﻿@model MultiStore.Models.ScratchCardSalesViewModel

@{
    ViewData["Title"] = "ScratchCard Sales";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>ScratchCard Sales</h1>

<form asp-action="ScratchCards" method="post">
    <input type="hidden" asp-for="StoreId" />

    <table class="table">
        <thead>
            <tr>
                <th>Name</th>
                <th>Price</th>
                <th>Opening</th>
                <th>Activate</th>
                <th>Closing</th>
                <th>Sales</th>
                <th>Amount</th>
            </tr>
        </thead>
        <tbody>
            @for (int i = 0; i < Model.ScratchCardsSales.Count; i++)
            {
                var sale = Model.ScratchCardsSales[i];
                <tr>
                    <td>
                        @Html.DisplayFor(m => sale.Number)
                        <input type="hidden" asp-for="ScratchCardsSales[i].ScratchCardId" />
                    </td>
                    <td>
                        <span class="price" data-index="@i">@sale.Price</span>
                    </td>
                    <td>
                        <span class="opening" data-index="@i">@sale.Opening</span>
                        <input type="hidden" asp-for="ScratchCardsSales[i].Opening" />

                    </td>
                    <td>
                        <input asp-for="ScratchCardsSales[i].Activate" class="form-control activate-input" data-index="@i" />
                    </td>
                    <td>
                        <input asp-for="ScratchCardsSales[i].Closing" class="form-control closing-input" data-index="@i" />
                    </td>
                   
                    <td class="sales-column" id="sales-@i">0</td>
                    <td class="amount-column" id="amount-@i">0</td>
                </tr>
            }
            <tr>
                <td colspan="6" class="text-right"><strong>Total Amount</strong></td>
                <td id="total-amount">0</td>
            </tr>
        </tbody>
    </table>

    <div class="form-group">
        <input type="submit" value="Submit" class="btn btn-primary" />
    </div>
    <input type="button" value="Cancel" class="btn btn-dark" onclick="window.history.back()" />

</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />

    <script>
        document.querySelectorAll(".closing-input, .activate-input").forEach(input => {
            input.addEventListener("input", updateSalesAndAmounts);
        });

        function updateSalesAndAmounts() {
            let totalAmount = 0;

            document.querySelectorAll("tbody tr").forEach((row, index) => {
                const openingElem = row.querySelector(`.opening[data-index='${index}']`);
                const closingInput = row.querySelector(`.closing-input[data-index='${index}']`);
                const activateInput = row.querySelector(`.activate-input[data-index='${index}']`);
                const salesColumn = row.querySelector(`#sales-${index}`);
                const amountColumn = row.querySelector(`#amount-${index}`);
                const priceElem = row.querySelector(`.price[data-index='${index}']`);

                if (openingElem && closingInput && activateInput && salesColumn && amountColumn && priceElem) {
                    const opening = parseInt(openingElem.textContent);
                    const price = parseFloat(priceElem.textContent);

                    const closing = parseInt(closingInput.value) || 0;
                    const activate = parseInt(activateInput.value) || 0;
                    const sales =  opening + activate - closing
                    const amount = sales * price;

                    salesColumn.textContent = sales;
                    amountColumn.textContent = amount.toFixed(2);

                    totalAmount += amount;
                }
            });

            document.getElementById("total-amount").textContent = totalAmount.toFixed(2);
        }

        // Call updateSalesAndAmounts initially to set values
        updateSalesAndAmounts();
    </script>
}
