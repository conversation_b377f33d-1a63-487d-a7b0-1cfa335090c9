﻿@model MultiStore.Persistance.CommisionEntry

@{
    ViewData["Title"] = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2>Delete</h2>

<h4>CommisionEntry</h4>
<hr />
<div>
    <h4>Are you sure you want to delete this?</h4>
    <div>
        <h5>@Model.Store.Name</h5>
        <h5>@Model.Supplier.Name</h5>
        <h5>@Model.CommisionType</h5>
        <h5>@Model.Gross</h5>
        <h5>@Model.Vat</h5>
        <h5>@Model.Net</h5>
        <h5>@Model.Date</h5>
    </div>
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
