﻿using System.Reflection.Metadata.Ecma335;
using System.Runtime.CompilerServices;

namespace MultiStore.Persistance
{
    public class Service : Item
    {
        public ICollection<StoreService> StoreServices { get; set; }

        public ServiceType ServiceType { get; set; }

        public int? ServiceTypeId { get; set; }


        public VatPercentage VatPercentage { get; set; }

        public int? VatPercentageId { get; set; }


        public decimal Discount { get; set; }


        public Decimal Price { get; set; } 
    }
}
