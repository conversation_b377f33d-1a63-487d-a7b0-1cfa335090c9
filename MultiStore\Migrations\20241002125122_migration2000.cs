﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MultiStore.Migrations
{
    public partial class migration2000 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "CardTypeId",
                table: "PaymentCards",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "CardTypes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CardTypes", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PaymentCards_CardTypeId",
                table: "PaymentCards",
                column: "CardTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_PaymentCards_CardTypes_CardTypeId",
                table: "PaymentCards",
                column: "CardTypeId",
                principalTable: "CardTypes",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PaymentCards_CardTypes_CardTypeId",
                table: "PaymentCards");

            migrationBuilder.DropTable(
                name: "CardTypes");

            migrationBuilder.DropIndex(
                name: "IX_PaymentCards_CardTypeId",
                table: "PaymentCards");

            migrationBuilder.DropColumn(
                name: "CardTypeId",
                table: "PaymentCards");
        }
    }
}
