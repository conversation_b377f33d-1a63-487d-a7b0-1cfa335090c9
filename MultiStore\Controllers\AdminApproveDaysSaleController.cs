﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class AdminApproveDaysSaleController : Controller
    {

        private readonly Data _context;
        private readonly ICompositeViewEngine _viewEngine;


        public AdminApproveDaysSaleController(Data context, ICompositeViewEngine viewEngine)
        {
            _context = context;
            _viewEngine = viewEngine;
        }

        public IActionResult Index()
        {
            var viewData =
                 _context.StoreDays
                 .Include(s => s.Store)
                 .Where(sd => sd.IsFinalised == false && sd.FinalizeApplied == true && sd.FinalizeAccepted == false)
                 .Select(sd => new { storeId= sd.Store.Id, store = sd.Store.Name , Day = sd.Day });


            return View(viewData);
        }
    }
}
