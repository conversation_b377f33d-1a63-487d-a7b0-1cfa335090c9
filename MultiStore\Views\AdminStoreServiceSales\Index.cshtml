﻿@model IEnumerable<MultiStore.Persistance.StoreServiceSales>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Store Service Sales</h1>



<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Store.Name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Service.Name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.SalesCount)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Day)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.Store.Name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Service.Name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.SalesCount)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Day)
                </td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-primary">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-danger">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
