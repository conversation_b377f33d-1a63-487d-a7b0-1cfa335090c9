﻿@model MultiStore.Persistance.BackOfficeCardPaymentEntry

@{
    ViewData["Title"] = "BackOffice Card Payment Entries";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2>BackOffice Card Payment Entries</h2>

<h4>Edit</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div class="form-group">
                <label asp-for="StoreId" class="control-label"></label>
                <select asp-for="StoreId" class="form-control" asp-items="ViewBag.StoreId"></select>
            </div>
            <div class="form-group">
                <label asp-for="CardTypeId" class="control-label"></label>
                <select asp-for="CardTypeId" class="form-control" asp-items="ViewBag.CardTypeId"></select>
            </div>
            <div class="form-group">
                <label asp-for="Amount" class="control-label"></label>
                <input asp-for="Amount" class="form-control" />
                <span asp-validation-for="Amount" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Date" class="control-label"></label>
                <input asp-for="Date" class="form-control" />
                <span asp-validation-for="Date" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>
<div>
    <a asp-action="Index">Back to List</a>
</div>
