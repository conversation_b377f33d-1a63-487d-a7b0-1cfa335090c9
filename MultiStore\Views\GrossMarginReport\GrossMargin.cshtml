﻿@model List<MultiStore.Controllers.GrossMarginReportController.GrossMarginViewModel>

@{
    ViewData["Title"] = "Gross Margin List";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

    var totalProductSales = Model.Sum(item => item.ProductSales);
    var totalFuelSales = Model.Sum(item => item.FuelSales);
    var totalServiceSales = Model.Sum(item => item.ServiceSales);
    var totalScratchCardSales = Model.Sum(item => item.ScratchCardSales);
    var totalTotalSales = Model.Sum(item => item.TotalSales);
    var totalPurchaseGross = Model.Sum(item => item.PurchaseGross);
    var totalPurchaseVat = Model.Sum(item => item.PurchaseVat);
    var totalPurchaseNet = Model.Sum(item => item.PurchaseNet);
}

<div>
    <p><strong>Store:</strong> @ViewData["StoreName"]</p>

    <p><strong>Company:</strong> @ViewData["CompanyName"]</p>
    <p><strong>From:</strong> @ViewData["StartDate"] <strong>To:</strong> @ViewData["EndDate"]</p>
</div>


<div class="container mt-4">
    <h2 class="mb-4">Gross Margin List</h2>
    <table class="table table-bordered table-striped">
        <thead class="table-dark">
            <tr>
                <th>Date</th>
                <th>Store Name</th>
                <th>Company Name</th>
                <th>Product Sales</th>
                <th>Fuel Sales</th>
                <th>Service Sales</th>
                <th>Scratch Card Sales</th>
                <th>Total Sales</th>
                <th>Purchase Gross</th>
                <th>Purchase Vat</th>
                <th>Purchase Net</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                <tr>
                    <td>@item.Date.ToString("yyyy-MM-dd")</td>
                    <td>@item.StoreName</td>
                    <td>@item.CompnayName</td>
                    <td>@item.ProductSales.ToString("C")</td>
                    <td>@item.FuelSales.ToString("C")</td>
                    <td>@item.ServiceSales.ToString("C")</td>
                    <td>@item.ScratchCardSales.ToString("C")</td>
                    <td>@item.TotalSales.ToString("C")</td>
                    <td>@item.PurchaseGross.ToString("C")</td>
                    <td>@item.PurchaseVat.ToString("C")</td>
                    <td>@item.PurchaseNet.ToString("C")</td>
                </tr>
            }
        </tbody>
        <tfoot class="table-secondary">
            <tr>
                <td colspan="3" class="text-end"><strong>Total</strong></td>
                <td>@totalProductSales.ToString("C")</td>
                <td>@totalFuelSales.ToString("C")</td>
                <td>@totalServiceSales.ToString("C")</td>
                <td>@totalScratchCardSales.ToString("C")</td>
                <td>@totalTotalSales.ToString("C")</td>
                <td>@totalPurchaseGross.ToString("C")</td>
                <td>@totalPurchaseVat.ToString("C")</td>
                <td>@totalPurchaseNet.ToString("C")</td>
            </tr>
        </tfoot>
    </table>
</div>
