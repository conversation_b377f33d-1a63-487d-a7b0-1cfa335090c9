﻿@model MultiStore.Persistance.StoreScratchCardSales

@{
    ViewData["Title"] = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2>Edit</h2>

<h4>StoreScratchCardSales</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div class="form-group">
                <label asp-for="StoreId" class="control-label"></label>
                <select asp-for="StoreId" class="form-control" asp-items="ViewBag.StoreId"></select>
            </div>
            <div class="form-group">
                <label asp-for="ScratchCardId" class="control-label"></label>
                <select asp-for="ScratchCardId" class="form-control" asp-items="ViewBag.ScratchCardId"></select>
            </div>
            <div class="form-group">
                <label asp-for="SalesCount" class="control-label"></label>
                <input asp-for="SalesCount" class="form-control" />
                <span asp-validation-for="SalesCount" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CloseCount" class="control-label"></label>
                <input asp-for="CloseCount" class="form-control" />
                <span asp-validation-for="CloseCount" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="ActivationCount" class="control-label"></label>
                <input asp-for="ActivationCount" class="form-control" />
                <span asp-validation-for="ActivationCount" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Day" class="control-label"></label>
                <input asp-for="Day" class="form-control" />
                <span asp-validation-for="Day" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>
<div>
    <a asp-action="Index">Back to List</a>
</div>
