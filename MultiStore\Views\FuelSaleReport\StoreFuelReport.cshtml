﻿@model IEnumerable<MultiStore.Persistance.StoreFuelSales>

@{
    ViewData["Title"] = "Fuel Sales Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed


    var saleAmountSum = Model.Sum(item => item.SaleAmount);
}

<h1>Fuel Sales Report</h1>

<div>
    <p><strong>Store:</strong> @ViewData["StoreName"]</p>

    <p><strong>Company:</strong> @ViewData["CompanyName"]</p>
    <p><strong>From:</strong> @ViewData["StartDate"] <strong>To:</strong> @ViewData["EndDate"]</p>
</div>

<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Store)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Fuel)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.SaleAmount)
            </th>
            <th>
                Time Span
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.Store.Name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Fuel.Name)
                </td>
                <td>
                    @item.SaleAmount.ToString("C", new System.Globalization.CultureInfo("en-GB"))
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Day)
                </td>
            </tr>
        }
        <tr>
            <td colspan="2"><strong>Total</strong></td>
            <td><strong>@saleAmountSum</strong></td>
            <td></td>
        </tr>
    </tbody>
</table>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.getElementById("deleteAllButton").addEventListener("click", function () {
            if (confirm("Are you sure you want to delete all records?")) {
                fetch('@Url.Action("DeleteAll", "AdminStoreFuelSales")', {
                    method: 'POST',
                    headers: {
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    }
                }).then(response => {
                    if (response.ok) {
                        window.location.href = '@Url.Action("Index", "AdminStoreFuelSales")';
                    } else {
                        alert('Failed to delete all records.');
                    }
                }).catch(error => {
                    alert('Error: ' + error);
                });
            }
        });
    </script>
}
