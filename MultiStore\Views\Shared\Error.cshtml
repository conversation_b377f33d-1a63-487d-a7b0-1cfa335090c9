﻿@model ErrorViewModel
@{
    ViewData["Title"] = "Error";
}

<div class="text-center">
    <h1 class="text-danger"><i class="fa fa-exclamation-triangle"></i></h1>
    <h2 class="text-danger">An error occurred while processing your request.</h2>
    <p>Please contact support if the issue persists.</p>
</div>

@if (Model.ShowRequestId)
{
    <p class="text-center">
        <strong>Request ID:</strong> <code>@Model.RequestId</code>
    </p>
}

<h3 class="text-center">Development Mode</h3>
<p class="text-center">
    Swapping to <strong>Development</strong> environment will display more detailed information about the error that occurred.
</p>
<p class="text-center">
    <strong>The Development environment shouldn't be enabled for deployed applications.</strong>
    It can result in displaying sensitive information from exceptions to end users.
    For local debugging, enable the <strong>Development</strong> environment by setting the <strong>ASPNETCORE_ENVIRONMENT</strong> environment variable to <strong>Development</strong>
    and restarting the app.
</p>
