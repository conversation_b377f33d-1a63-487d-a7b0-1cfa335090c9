﻿@model MultiStore.Models.SectionsViewModel

@{
    ViewData["Title"] = "Home";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}


<h2>Input Date = @Model.InputDate.Date.ToString("dd/MM/yyyy")</h2>


@if (ViewData["PendingMessage"] != null)
{
    <div class="alert alert-danger">
        @ViewData["PendingMessage"]
    </div>
}

@if (ViewData["Rejected"] != null)
{
    <div class="alert alert-danger">
        @ViewData["Rejected"]
    </div>
}


@if (ViewData["Buttons"] != null)
{
    <a asp-controller="StoreDashboard" asp-action="AcceptFinalize" asp-route-storeId="@Model.StoreId" class="btn btn-success">Accept</a>
    <a asp-controller="StoreDashboard" asp-action="RejectFinalize" asp-route-storeId="@Model.StoreId" class="btn btn-secondary">Reject</a>



}


@foreach (var storePaidIn in Model.storePaidIns)
{
    <div>
        <label>Paid In Confirmation Pending..</label>
        <div>
            From: @Model.keyValuePairs[storePaidIn.TransferredFromStoreDayId].Name
        </div>
        <div>
            Amount: @storePaidIn.Amount
        </div>
        <div>
            Tranfered Date: @storePaidIn.TransferredFromDate
        </div>

        <a asp-controller="StoreDashboard" asp-action="AcceptStorePaidIn" asp-route-storePaidInId="@storePaidIn.Id" class="btn btn-success">Accept</a>
        <a asp-controller="StoreDashboard" asp-action="RejectStorePaidIn" asp-route-storePaidInId="@storePaidIn.Id" class="btn btn-secondary">Reject</a>
    </div>
    <hr />
}


<div class="col-md-3">
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="StoreDashboard" asp-action="SectionView" asp-route-storeId="@Model.StoreId" asp-route-section="@nameof(MultiStore.Persistance.Product)" class="btn btn-primary btn-block">Products</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="StoreDashboard" asp-action="SectionView" asp-route-storeId="@Model.StoreId" asp-route-section="@nameof(MultiStore.Persistance.Service)" class="btn btn-primary btn-block">Services</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="StoreDashboard" asp-action="SectionView" asp-route-storeId="@Model.StoreId" asp-route-section="@nameof(MultiStore.Persistance.Fuel)" class="btn btn-primary btn-block">Fuel</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="StoreDashboard" asp-action="SectionView" asp-route-storeId="@Model.StoreId" asp-route-section="@nameof(MultiStore.Persistance.ScratchCard)" class="btn btn-primary btn-block">Scratch Cards</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="StoreDashboard" asp-action="SectionView" asp-route-storeId="@Model.StoreId" asp-route-section="@nameof(MultiStore.Persistance.PayOutParty)" class="btn btn-primary btn-block">Back Office Payouts</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="StoreDashboard" asp-action="SectionView" asp-route-storeId="@Model.StoreId" asp-route-section="@nameof(MultiStore.Persistance.PaymentCard)" class="btn btn-primary btn-block">Card Payment Methods</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="StoreDashboard" asp-action="SectionView" asp-route-storeId="@Model.StoreId" asp-route-section="@nameof(MultiStore.Persistance.SpecificPayOut)" class="btn btn-primary btn-block">Till Payout</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="StoreDashboard" asp-action="SectionView" asp-route-storeId="@Model.StoreId" asp-route-section="Atm" class="btn btn-primary btn-block">ATM</a>

    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="StoreDashboard" asp-action="SectionView" asp-route-storeId="@Model.StoreId" asp-route-section="@nameof(MultiStore.Persistance.StoreDay)" class="btn btn-primary btn-block">Balance</a>

</div>



@if(Model.storePaidIns.Count() > 0 &&  Model.IsThereAPendingPaidInThisScoreTransfered)
{
    <form action="@Url.Action("Finalise", "StoreDashboard")
            " onsubmit="return confirmSubmission();">
        <input type="hidden" asp-for="@Model.StoreId" />
        <input disabled style="margin-top:10px" class="btn btn-danger" type="submit" value="Wait for Store Tranfer Confirmation" />
        <input type="hidden" asp-for="CashInHand" />
    </form>
}
else if (Model.storePaidIns.Count() > 0)
{
    <form action="@Url.Action("Finalise", "StoreDashboard")
        " onsubmit="return confirmSubmission();" >
        <input type="hidden" asp-for="@Model.StoreId" />
        <input disabled style="margin-top:10px" class="btn btn-danger" type="submit" value="Finalise for @Model.InputDate.Date.ToString("dd/MM/yyyy")" />
        <input type="hidden" asp-for="CashInHand" />
    </form>
}
else
{
    <form action="@Url.Action("Finalise", "StoreDashboard")" onsubmit="return confirmSubmission();">
        <input type="hidden" asp-for="@Model.StoreId" />
        <input style="margin-top:10px" class="btn btn-danger" type="submit" value="Finalise for @Model.InputDate.Date.ToString("dd/MM/yyyy")" />
        <input type="hidden" asp-for="CashInHand" />
    </form>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger">
        @TempData["ErrorMessage"]
    </div>
}



<script type="text/javascript">
    function confirmSubmission() {
        return confirm("Are you sure you want to finalise for This date?");
    }
</script>


