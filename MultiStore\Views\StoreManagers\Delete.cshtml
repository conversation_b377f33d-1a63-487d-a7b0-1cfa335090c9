﻿@model MultiStore.Persistance.StoreManager

@{
    ViewData["Title"] = "Delete Store Manager";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Delete Store Manager</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Store Manager</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.FirstName)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.FirstName)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.LastName)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.LastName)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.UserName)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.UserName)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.Store.Name)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Store.Name)
        </dd>
    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index" class="btn btn-secondary">Back to List</a>
    </form>
</div>

