﻿@model MultiStore.Persistance.StoreFuelSales

@{
    ViewData["Title"] = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Delete Store Fuel Sales</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Store Fuel Sales</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            Store
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Store.Name)
        </dd>
        <dt class="col-sm-2">
            Fuel
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Fuel.Name)
        </dd>
        <dt class="col-sm-2">
            Sale Amount
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.SaleAmount)
        </dd>
        <dt class="col-sm-2">
            Day
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Day)
        </dd>
    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index" class="btn btn-secondary">Back to List</a>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
