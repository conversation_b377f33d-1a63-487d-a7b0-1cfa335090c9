﻿using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using System.Diagnostics;

namespace MultiStore.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;

        private readonly Data _dbContext;



        public HomeController(ILogger<HomeController> logger, Data context)
        {
            _logger = logger;
            _dbContext = context;


            //UtilityService utilityService = new UtilityService(context);
            //utilityService.UpdateStoreDaysToReflectChanges(9);
        }

        public IActionResult Index()
        {



            if (HttpContext.Session.Keys.Count() == 0)
            {
                return RedirectToAction("Login", "Home");
            }

            if (HttpContext.Session.GetString("UserType") != null && HttpContext.Session.GetString("UserType") == "Admin")
            {
                return RedirectToAction("Index", "Admin");
            }
            else if (HttpContext.Session.GetString("UserType") != null && HttpContext.Session.GetString("UserType") == "StoreManager")
            {
                return RedirectToAction("Index", "StoreDashboard");
            }
            else if (HttpContext.Session.GetString("UserType") != null && HttpContext.Session.GetString("UserType") == "BackOffice")
            {
                return RedirectToAction("Index", "BackOfficeDashboard");
            }


            return View();
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }

        [HttpGet]
        public IActionResult Login()
        {

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginViewModel model)
        {
            if (ModelState.IsValid)
            {
                if (_dbContext.Admins.Any(a => a.UserName == model.UserName.Trim()))
                {
                    var admin = _dbContext.Admins.First(a => a.UserName == model.UserName.Trim());
                    if (admin.Password == model.Password)
                    {
                        Request.HttpContext.Session.SetInt32("UserId", admin.Id);
                        Request.HttpContext.Session.SetString("UserType", "Admin");
                        Request.HttpContext.Session.SetString("FirstName", admin.FirstName);

                        return RedirectToAction("Index", "Admin");
                    }
                    else
                    {
                        ModelState.AddModelError(string.Empty, "Invalid login attempt.");
                    }
                }
                else if (_dbContext.BackOffices.Any(p => p.UserName == model.UserName.Trim()))
                {
                    var backoffice = _dbContext.BackOffices.First(p => p.UserName == model.UserName.Trim());
                    if (backoffice.Password == model.Password)
                    {
                        Request.HttpContext.Session.SetInt32("UserId", backoffice.Id);
                        Request.HttpContext.Session.SetString("UserType", "BackOffice");
                        Request.HttpContext.Session.SetString("FirstName", backoffice.FirstName);

                        return RedirectToAction("Index", "BackOfficeDashboard");

                    }
                    else
                    {
                        ModelState.AddModelError(string.Empty, "Invalid login attempt.");
                    }

                }
                else if (_dbContext.StoreManagers.Any(p => p.UserName == model.UserName.Trim()))
                {
                    var storeManager = _dbContext.StoreManagers.First(p => p.UserName == model.UserName.Trim());
                    if (storeManager.Password == model.Password)
                    {


                        Request.HttpContext.Session.SetInt32("UserId", storeManager.Id);
                        Request.HttpContext.Session.SetString("UserType", "StoreManager");
                        Request.HttpContext.Session.SetInt32("UserStoreId", (int)storeManager.StoreId);

                        Request.HttpContext.Session.SetString("FirstName", storeManager.FirstName);

                        return RedirectToAction("Index", "StoreDashboard");

                    }
                    else
                    {
                        ModelState.AddModelError(string.Empty, "Invalid login attempt.");
                    }

                }
                else
                {
                    ModelState.AddModelError(string.Empty, "Invalid login attempt.");

                }



            }

            return View(model);

            //data.Practitioners.Find(Request.HttpContext.Session.GetInt32("UserId"));
        }


        public async Task<IActionResult> LogOut(LoginViewModel model)
        {
            Request.HttpContext.Session.Clear();
            return RedirectToAction("Login", "Home");

        }


        [HttpGet]
        public IActionResult Restricted()
        {

            return View();
        }


        [HttpGet]
        public IActionResult GetFinalizeNotifications()
        {

            var count = _dbContext.StoreDays.Where(s => s.FinalizeAccepted == false && s.FinalizeRejected == false && s.FinalizeApplied == true).Count().ToString();
            return Content(count);
        }



    }
}