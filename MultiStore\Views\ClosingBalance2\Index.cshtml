﻿@{
    ViewData["Title"] = "Closing Balance 2 Report";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h2>Closing Balance 2 Report</h2>

<form id="dateForm" asp-action="Submit" method="post">
    <div class="form-group">
        <label for="datepicker1">Start Date</label>
        <input type="date" id="datepicker1" name="StartDate" class="form-control" />
    </div>
    <div class="form-group">
        <label for="datepicker2">End Date</label>
        <input type="date" id="datepicker2" name="EndDate" class="form-control" />
    </div>

    <hr />

    <div class="form-group">
        <label for="datepicker3">Pick a Date</label>
        <input type="date" id="datepicker3" name="SingleDate" class="form-control" />
    </div>
    <div class="form-group">
        <label for="period">Period</label>
        <select id="period" name="Period" class="form-control">
            <option value="">Select a period</option>
            <option value="Week">Week</option>
            <option value="Month">Month</option>
            <option value="3Months">3 Months</option>
            <option value="Year">Year</option>
        </select>
    </div>

    <div class="form-group">
        <label for="companySelect">Company</label>
        <select id="companySelect" name="SelectedCompanyId" class="form-control">
            <option value="">-- Select Company --</option>
            @foreach (var company in (List<MultiStore.Persistance.Company>)ViewData["Companies"])
            {
                <option value="@company.Id">@company.Name</option>
            }
        </select>
    </div>
    <div class="form-group">
        <label for="storeSelect">Store</label>
        <select id="storeSelect" name="SelectedStoreId" class="form-control">
            <option value="">-- Select Store --</option>
            @foreach (var store in (List<MultiStore.Persistance.Store>)ViewData["Stores"])
            {
                <option value="@store.Id" data-company-id="@store.CompanyId">@store.Name</option>
            }
        </select>
    </div>

    <button type="submit" class="btn btn-primary">Submit</button>
</form>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#companySelect').change(function () {
                var selectedCompanyId = $(this).val();
                $('#storeSelect option').each(function () {
                    var storeCompanyId = $(this).data('company-id');
                    if (selectedCompanyId === "" || storeCompanyId == selectedCompanyId) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
                $('#storeSelect').val('');
            });
        });

        $("#dateForm").submit(function (event) {
            var startDate = $("#datepicker1").val();
            var endDate = $("#datepicker2").val();
            var singleDate = $("#datepicker3").val();
            var period = $("#period").val();

            if ((startDate && endDate) || (singleDate && period)) {
                return true;
            } else {
                alert("Please fill either the start and end dates or the single date and period.");
                event.preventDefault();
                return false;
            }
        });
    </script>
}
