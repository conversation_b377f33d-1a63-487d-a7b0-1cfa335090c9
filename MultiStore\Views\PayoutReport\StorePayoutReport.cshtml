﻿@model IEnumerable<MultiStore.Persistance.StoreMgrPayOut>

@{
    ViewData["Title"] = "Payouts Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed

    var totalAmount = Model.Sum(m => m.Amount); // Overall total for all stores
}

<h1>BACK OFFICE Payouts Report</h1>

<div>
    <p><strong>Store:</strong> @ViewData["StoreName"]</p>
    <p><strong>Company:</strong> @ViewData["CompanyName"]</p>
    <p><strong>From:</strong> @ViewData["StartDate"] <strong>To:</strong> @ViewData["EndDate"]</p>
</div>

<table class="table">
    <thead>
        <tr>
            <th>
                Store
            </th>
            <th>
                Date
            </th>
            <th>
                Recipient
            </th>
            <th>
                Amount
            </th>
        </tr>
    </thead>
    <tbody>
        @foreach (var storeGroup in Model.GroupBy(m => m.Store.Name))
        {
            // Display the store name as a group header
            <tr>
                <td colspan="4" style="background-color: #f2f2f2;">
                    <strong>@storeGroup.Key</strong>
                </td>
            </tr>

            // Loop through the individual payouts for each store
            foreach (var item in storeGroup)
            {
                <tr>
                    <td>
                        @item.Store.Name
                    </td>
                    <td>
                        @item.Date.ToShortDateString()
                    </td>
                    <td>
                        @item.Recipient
                    </td>
                    <td>
                        @item.Amount.ToString("C", new System.Globalization.CultureInfo("en-GB"))
                    </td>
                </tr>
            }

            // Display the subtotal for the current store
            var storeTotal = storeGroup.Sum(m => m.Amount);
            <tr>
                <td colspan="3"><strong>Subtotal for @storeGroup.Key</strong></td>
                <td><strong>@storeTotal.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
            </tr>
        }

        
        <tr>
            <td colspan="3"><strong>Total</strong></td>
            <td><strong>@totalAmount.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
        </tr>
    </tbody>
</table>
