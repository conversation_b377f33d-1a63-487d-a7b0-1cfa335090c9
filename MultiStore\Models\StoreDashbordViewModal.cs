﻿using MultiStore.Persistance;

namespace MultiStore.Models
{
    public class DaysSalesInputViewModal
    {
        public int StoreId { get; set; }
        public List<DaysProduct> DaysProducts { get; set; } = new List<DaysProduct>();
        public List<DaysService> DaysServices { get; set; } = new List<DaysService>();

        public List<ServiceType> ServiceTypes { get; set; } = new List<ServiceType>();

      
      //  public Tuple<List<DaysProduct>, List<DaysService>> DaysProductsAndServices => new Tuple<List<DaysProduct>, List<DaysService>>(DaysProducts, DaysServices);>
    }
}
