﻿@model MultiStore.Models.SpecificPayOutViewModal

@{
    ViewData["Title"] = "Specific PayOut";
    var payOutParties = ViewBag.PayOutParties as List<MultiStore.Persistance.PayOutParty>;
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2>Till PayOuts</h2>

<form asp-action="SpecificPayOut" method="post">

    <input type="hidden" asp-for="StoreId" />

    <table class="table" id="payoutTable">
        <thead>
            <tr>
                <th>Payout Party</th>
                <th>Amount</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            @for (int i = 0; i < Model.PayOutRows.Count; i++)
            {
                <tr>
                    <td>
                        <select asp-for="@Model.PayOutRows[i].PayOutPartyId" class="form-control">
                            @foreach (var party in payOutParties)
                            {
                                <option value="@party.Id">@party.Name</option>
                            }
                        </select>
                    </td>
                    <td>
                        <input asp-for="@Model.PayOutRows[i].Amount" class="form-control" />
                    </td>
                    <td>
                        <button type="button" class="btn btn-danger" onclick="removeRow(this)">Remove</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
    <button type="button" class="btn btn-secondary" onclick="addRow()">Add Row</button>
    <div class="form-group mt-2">
        <button type="submit" class="btn btn-primary">Submit</button>
    </div>
    <input type="button" value="Cancel" class="btn btn-dark" onclick="window.history.back()" />

</form>

@section Scripts {
    <script>
        function addRow() {
            var table = document.getElementById('payoutTable').getElementsByTagName('tbody')[0];
            var newRow = table.insertRow();

            var cell1 = newRow.insertCell(0);
            var cell2 = newRow.insertCell(1);
            var cell3 = newRow.insertCell(2);

            var partyOptions = '';
        @foreach (var party in payOutParties)
        {
            <text>
                    partyOptions += '<option value="@party.Id">@party.Name</option>';
            </text>
        }

                cell1.innerHTML = '<select name="PayOutRows[0].PayOutPartyId" class="form-control">' + partyOptions + '</select>';
            cell2.innerHTML = '<input type="number" name="PayOutRows[0].Amount" class="form-control" />';
            cell3.innerHTML = '<button type="button" class="btn btn-danger" onclick="removeRow(this)">Remove</button>';

            updateRowIndices();
        }

        function removeRow(button) {
            var row = button.closest('tr');
            row.parentNode.removeChild(row);
            updateRowIndices();
        }

        function updateRowIndices() {
            var rows = document.getElementById('payoutTable').getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            for (var i = 0; i < rows.length; i++) {
                var select = rows[i].getElementsByTagName('select')[0];
                var input = rows[i].getElementsByTagName('input')[0];
                select.name = `PayOutRows[${i}].PayOutPartyId`;
                input.name = `PayOutRows[${i}].Amount`;
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            updateRowIndices();
        });
    </script>
}
