﻿@model MultiStore.Persistance.ScratchCard

@{
    ViewData["Title"] = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Delete</h1>

<h4>ScratchCard</h4>
<hr />
<div>
    <h4>Are you sure you want to delete this?</h4>
    <div>
        <h4>ScratchCard</h4>
        <hr />
        <dl class="row">
            <dt class="col-sm-2">
                @Html.DisplayNameFor(model => model.Name)
            </dt>
            <dd class="col-sm-10">
                @Html.DisplayFor(model => model.Name)
            </dd>
            <dt class="col-sm-2">
                @Html.DisplayNameFor(model => model.Price)
            </dt>
            <dd class="col-sm-10">
                @Html.DisplayFor(model => model.Price)
            </dd>
        </dl>
    </div>
    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index" class="btn btn-secondary">Back to List</a>
    </form>
</div>
