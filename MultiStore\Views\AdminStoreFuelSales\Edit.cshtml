﻿@model MultiStore.Models.StoreFuelSalesViewModel

@{
    ViewData["Title"] = "Edit Store Fuel Sales";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Edit Store Fuel Sales</h1>

<h4>Store Fuel Sales</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="StoreFuelSales.Id" />
            <div class="form-group">
                <label asp-for="StoreFuelSales.StoreId" class="control-label">Store</label>
                <select asp-for="StoreFuelSales.StoreId" class="form-control" asp-items="Model.Stores"></select>
                <span asp-validation-for="StoreFuelSales.StoreId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="StoreFuelSales.FuelId" class="control-label">Fuel</label>
                <select asp-for="StoreFuelSales.FuelId" class="form-control" asp-items="Model.Fuels"></select>
                <span asp-validation-for="StoreFuelSales.FuelId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="StoreFuelSales.SaleAmount" class="control-label"></label>
                <input asp-for="StoreFuelSales.SaleAmount" class="form-control" />
                <span asp-validation-for="StoreFuelSales.SaleAmount" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="StoreFuelSales.Day" class="control-label"></label>
                <input asp-for="StoreFuelSales.Day" class="form-control" />
                <span asp-validation-for="StoreFuelSales.Day" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
