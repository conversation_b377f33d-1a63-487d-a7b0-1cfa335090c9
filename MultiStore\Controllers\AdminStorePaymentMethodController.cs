﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using System.Linq;
using System.Threading.Tasks;

namespace MultiStore.Controllers
{
    public class AdminStorePaymentMethodController : Controller
    {
        private readonly Data _context;
        private readonly IUtilityService _utilityService;


        public AdminStorePaymentMethodController(Data context, IUtilityService utilityService)
        {
            _context = context;
            _utilityService = utilityService;
        }

        public async Task<IActionResult> Index(DateTime startDate, DateTime endDate)
        {
            var payments = await _context.StorePaymentMethods
                .Include(spm => spm.Store)
                .Include(spm => spm.PaymentCard)
                .Where(s => s.Date.Date >= startDate && s.Date.Date <= endDate)
                .ToListAsync();

            ViewData["CardPayments"] = payments.ToList();

            return View(payments);
        }

        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var paymentMethod = await _context.StorePaymentMethods.FindAsync(id);
            if (paymentMethod == null)
            {
                return NotFound();
            }

            ViewData["Stores"] = new SelectList(_context.Stores, "Id", "Name", paymentMethod.StoreId);
            ViewData["PaymentCards"] = new SelectList(_context.PaymentCards, "Id", "Name", paymentMethod.PaymentCardId);
            return View(paymentMethod);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, StorePaymentMethod storePaymentMethod)
        {
            if (id != storePaymentMethod.Id)
            {
                return NotFound();
            }

            // if (ModelState.IsValid)
            // {
            try
            {
                _context.Update(storePaymentMethod);
                await _context.SaveChangesAsync();
                _utilityService.UpdateStoreDaysToReflectChanges(storePaymentMethod.StoreId);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StorePaymentMethodExists(storePaymentMethod.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

             return RedirectToAction(nameof(Index), "AdminStoreSales");


            // }

            // ViewData["Stores"] = new SelectList(_context.Stores, "Id", "Name", storePaymentMethod.StoreId);
            // ViewData["PaymentCards"] = new SelectList(_context.PaymentCards, "Id", "Name", storePaymentMethod.PaymentCardId);
            // return View(storePaymentMethod);
        }

        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storePaymentMethod = await _context.StorePaymentMethods
                .Include(s => s.Store)
                .Include(s => s.PaymentCard)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (storePaymentMethod == null)
            {
                return NotFound();
            }

            return View(storePaymentMethod);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var storePaymentMethod = await _context.StorePaymentMethods.FindAsync(id);
            _context.StorePaymentMethods.Remove(storePaymentMethod);
            await _context.SaveChangesAsync();
            _utilityService. UpdateStoreDaysToReflectChanges(storePaymentMethod.StoreId);


            return RedirectToAction(nameof(Index), "AdminStoreSales");

            return RedirectToAction(nameof(Index));

            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteAll()
        {
            var allRecords = _context.StorePaymentMethods.ToList();
            _context.StorePaymentMethods.RemoveRange(allRecords);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool StorePaymentMethodExists(int id)
        {
            return _context.StorePaymentMethods.Any(e => e.Id == id);
        }


    }
}
