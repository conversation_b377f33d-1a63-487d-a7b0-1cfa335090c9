﻿@model MultiStore.Models.EditScratchCardsViewModel

@{
    ViewData["Title"] = "Edit ScratchCards";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Edit Products for Store</h1>

<form asp-action="UpdateScratchCards" method="post">
    <input type="hidden" asp-for="StoreId" />
    <input type="hidden" id="selectedScratchCardIds" name="selectedScratchCardIds" />

    <div class="row">
        <div class="col-md-6">
            <h3>Selected ScratchCards</h3>
            <select multiple class="form-control" id="selectedScratchCards" size="10">
                @foreach (var ScratchCard in Model.SelectedScratchCards)
                {
                    <option value="@ScratchCard.Id">@ScratchCard.Name</option>
                }
            </select>
            <button type="button" class="btn btn-primary" id="deselectAll">Deselect All</button>
        </div>
        <div class="col-md-6">
            <h3>Non-Selected Products</h3>
            <select multiple class="form-control" id="nonSelectedScratchCards" size="10">
                @foreach (var ScratchCard in Model.NonSelectedScratchCards)
                {
                    <option value="@ScratchCard.Id">@ScratchCard.Name</option>
                }
            </select>
            <button type="button" class="btn btn-primary" id="selectAll">Select All</button>
        </div>
    </div>

    <div class="form-group mt-3">
        <input type="submit" value="Save" class="btn btn-primary" />
    </div>
</form>

@section Scripts {
    <script>
        document.getElementById("deselectAll").onclick = function () {
            var selectedScratchCards = document.getElementById("selectedScratchCards");
            var nonSelectedScratchCards = document.getElementById("nonSelectedScratchCards");
            while (selectedScratchCards.options.length > 0) {
                nonSelectedScratchCards.appendChild(selectedScratchCards.options[0]);
            }
        };

        document.getElementById("selectAll").onclick = function () {
            var selectedScratchCards = document.getElementById("selectedScratchCards");
            var nonSelectedScratchCards = document.getElementById("nonSelectedScratchCards");
            while (nonSelectedScratchCards.options.length > 0) {
                selectedScratchCards.appendChild(nonSelectedScratchCards.options[0]);
            }
        };

        document.getElementById("selectedScratchCards").ondblclick = function () {
            var selectedOptions = this.selectedOptions;
            var nonSelectedScratchCards = document.getElementById("nonSelectedScratchCards");
            for (var i = 0; i < selectedOptions.length; i++) {
                nonSelectedScratchCards.appendChild(selectedOptions[i]);
            }
        };

        document.getElementById("nonSelectedScratchCards").ondblclick = function () {
            var selectedOptions = this.selectedOptions;
            var selectedScratchCards = document.getElementById("selectedScratchCards");
            for (var i = 0; i < selectedOptions.length; i++) {
                selectedScratchCards.appendChild(selectedOptions[i]);
            }
        };

        document.querySelector("form").onsubmit = function () {
            var selectedScratchCardIds = document.getElementById("selectedScratchCardIds");
            var selectedScratchCards = document.getElementById("selectedScratchCards");

            var selectedIds = [];
            for (var i = 0; i < selectedScratchCards.options.length; i++) {
                selectedIds.push(selectedScratchCards.options[i].value);
            }

            selectedScratchCardIds.value = selectedIds.join(",");
        };
    </script>
}
