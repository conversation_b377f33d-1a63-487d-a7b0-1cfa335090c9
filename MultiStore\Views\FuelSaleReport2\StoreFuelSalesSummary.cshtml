﻿@model IEnumerable<MultiStore.Controllers.FuelSaleReport2Controller.StoreFuelSalesViewModel>

@{
    ViewData["Title"] = "Fuel Sales Report 2";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed
                                                     // Update the layout path as needed

}

<h2>Fuel Sales Report 2</h2>

<div>
    <p><strong>Company:</strong> @ViewData["CompanyName"]</p>
    <p><strong>From:</strong> @ViewData["StartDate"] <strong>To:</strong> @ViewData["EndDate"]</p>
</div>



<table class="table">
    <thead>
        <tr>
            <th>Product</th>
            <th>Vat</th>

            @foreach (var store in Model.First().StoreSales.Keys)
            {
                <th>@store</th>
            }
            <th>Total</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.FuelName</td> <!-- Product name -->
                <td>@item.Vat</td>

                @foreach (var sale in item.StoreSales)
                {
                    <td>@sale.Value.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td> <!-- Store sales -->
                }
                <td>@item.TotalSales.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td> <!-- Total sales for product -->
            </tr>
        }
    </tbody>
    <tfoot>
        <tr>
            <td><strong>Total</strong></td>
            <td><strong></strong></td>

            @foreach (var store in Model.First().StoreSales.Keys)
            {
                <td>
                    <strong>@Model.Sum(m => m.StoreSales[store]).ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong> <!-- Total sales for each store -->
                </td>
            }
            <td>
                <strong>@Model.Sum(m => m.TotalSales).ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong> <!-- Total sales across all stores -->
            </td>
        </tr>
    </tfoot>
</table>
