﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using System.Linq;
using System.Threading.Tasks;

namespace MultiStore.Controllers
{
    public class AdminStoreScratchCardSalesController : Controller
    {
        private readonly Data _context;
        private readonly IUtilityService _utilityService;


        public AdminStoreScratchCardSalesController(Data context, IUtilityService utilityService)
        {
            _context = context;
            _utilityService = utilityService;
        }

        // GET: AdminStoreScratchCardSales
        public async Task<IActionResult> Index(DateTime startDate, DateTime endDate)
        {
            var storeScratchCardSales = _context.StoreScratchCardSales
                .Include(s => s.Store)
                .Include(s => s.ScratchCard)
                .Where(s => s.Day.Date >= startDate && s.Day.Date <= endDate);
            return View(await storeScratchCardSales.ToListAsync());
        }

        // GET: AdminStoreScratchCardSales/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeScratchCardSale = await _context.StoreScratchCardSales.FindAsync(id);
            if (storeScratchCardSale == null)
            {
                return NotFound();
            }

            ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", storeScratchCardSale.StoreId);
            ViewData["ScratchCardId"] = new SelectList(_context.ScratchCards, "Id", "Name", storeScratchCardSale.ScratchCardId);

            return View(storeScratchCardSale);
        }

        // POST: AdminStoreScratchCardSales/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,StoreId,ScratchCardId,SalesCount,CloseCount,ActivationCount,Day")] StoreScratchCardSales storeScratchCardSale)
        {
            if (id != storeScratchCardSale.Id)
            {
                return NotFound();
            }

            //   if (ModelState.IsValid)
            //   {
            try
            {
                _context.Update(storeScratchCardSale);
                await _context.SaveChangesAsync();
               _utilityService. UpdateStoreDaysToReflectChanges(storeScratchCardSale.StoreId);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StoreScratchCardSalesExists(storeScratchCardSale.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return RedirectToAction(nameof(Index), "AdminStoreSales");

            // }

            //            ViewData["StoreId"] = new SelectList(_context.Stores, "Id", "Name", storeScratchCardSale.StoreId);
            //            ViewData["ScratchCardId"] = new SelectList(_context.ScratchCards, "Id", "Name", storeScratchCardSale.ScratchCardId);

            //            return View(storeScratchCardSale);
        }

        // GET: AdminStoreScratchCardSales/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeScratchCardSale = await _context.StoreScratchCardSales
                .Include(s => s.Store)
                .Include(s => s.ScratchCard)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (storeScratchCardSale == null)
            {
                return NotFound();
            }

            return View(storeScratchCardSale);
        }

        // POST: AdminStoreScratchCardSales/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var storeScratchCardSale = await _context.StoreScratchCardSales.FindAsync(id);
            _context.StoreScratchCardSales.Remove(storeScratchCardSale);
            await _context.SaveChangesAsync();
          _utilityService.  UpdateStoreDaysToReflectChanges(storeScratchCardSale.StoreId);

            return RedirectToAction(nameof(Index), "AdminStoreSales");

        }

        private bool StoreScratchCardSalesExists(int id)
        {
            return _context.StoreScratchCardSales.Any(e => e.Id == id);
        }



    }
}