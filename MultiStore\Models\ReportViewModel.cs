﻿using MultiStore.Persistance;
using System.Reflection.Metadata.Ecma335;
using System.Security.Principal;

namespace MultiStore.Models
{
    public class ReportViewModel
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? SingleDate { get; set; }

        public DateTime? StartDateDue { get; set; }
        public DateTime? EndDateDue { get; set; }
        public DateTime? SingleDateDue { get; set; }

        public string Period { get; set; }

        public List<Store> Stores{ get; set; }

        public List<Company> Companies { get; set; }

        public int SelectedStoreId { get; set; }
        public int SelectedCompanyId { get; set; }
    }

    public class PurchaseReportViewModel
    {
        public string CompnayName { get; set; }

        public string StoreName { get; set; }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public List<PurchaseEntryDTO> PurchaseEntries { get; set; }
    }

    public class BankReportViewModel
    {
        public string CompnayName { get; set; }

        public string StoreName { get; set; }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public List<BankEntryDTO> BankEntries { get; set; }
    }

    public class CompnayPurchaseReportViewModel
    {
        public string CompnayName { get; set; }

        public string StoreName { get; set; }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public List<CompanyPurchaseEntryDTO> PurchaseEntries { get; set; }
    }

}
