﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;
using MultiStore.Models;
using MultiStore.Persistance;
using System.Linq;
using System.Threading.Tasks;

namespace MultiStore.Controllers
{
    public class AdminStoreServiceSalesController : Controller
    {
        private readonly Data _context;
        IUtilityService _utilityService;

        public AdminStoreServiceSalesController(Data context, IUtilityService utilityService)
        {
            _context = context;
            _utilityService = utilityService;
        }

        public async Task<IActionResult> Index(DateTime startDate, DateTime endDate)
        {
            var storeServiceSales = await _context.StoreServiceSales
                .Include(s => s.Store)
                .Include(s => s.Service)
                .Where(s => s.Day.Date >= startDate && s.Day.Date <= endDate)
                .ToListAsync();
            return View(storeServiceSales);
        }

        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeServiceSales = await _context.StoreServiceSales.FindAsync(id);
            if (storeServiceSales == null)
            {
                return NotFound();
            }

            ViewData["Stores"] = new SelectList(_context.Stores, "Id", "Name", storeServiceSales.StoreId);
            ViewData["Services"] = new SelectList(_context.Services, "Id", "Name", storeServiceSales.ServiceId);
            return View(storeServiceSales);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, StoreServiceSales storeServiceSales)
        {
            if (id != storeServiceSales.Id)
            {
                return NotFound();
            }

           // if (ModelState.IsValid)
           // {
                try
                {
                    _context.Update(storeServiceSales);
                    await _context.SaveChangesAsync();
                     _utilityService.UpdateStoreDaysToReflectChanges(storeServiceSales.StoreId);
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!StoreServiceSalesExists(storeServiceSales.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index), "AdminStoreSales");
           // }

           // ViewData["Stores"] = new SelectList(_context.Stores, "Id", "Name", storeServiceSales.StoreId);
           // ViewData["Services"] = new SelectList(_context.Services, "Id", "Name", storeServiceSales.ServiceId);
           // return View(storeServiceSales);
        }

        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeServiceSales = await _context.StoreServiceSales
                .Include(s => s.Store)
                .Include(s => s.Service)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (storeServiceSales == null)
            {
                return NotFound();
            }

            return View(storeServiceSales);
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var storeServiceSales = await _context.StoreServiceSales.FindAsync(id);
            _context.StoreServiceSales.Remove(storeServiceSales);
            await _context.SaveChangesAsync();
            _utilityService.UpdateStoreDaysToReflectChanges(storeServiceSales.StoreId);

            return RedirectToAction(nameof(Index), "AdminStoreSales");

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteAll()
        {
            var allRecords = _context.StoreServiceSales.ToList();
            _context.StoreServiceSales.RemoveRange(allRecords);
            await _context.SaveChangesAsync();
          //  UpdateStoreDaysToReflectChanges(storeServiceSales.StoreId);


            return RedirectToAction(nameof(Index));
        }

        private bool StoreServiceSalesExists(int id)
        {
            return _context.StoreServiceSales.Any(e => e.Id == id);
        }



    }
}
