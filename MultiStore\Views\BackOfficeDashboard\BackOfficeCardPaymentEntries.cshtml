﻿@model MultiStore.Controllers.BackOfficeDashboardController.BackOfficeCardPaymentViewModel

@{
    ViewData["Title"] = "Card Payments";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed
}

<h1>Card Payments</h1>

<form asp-action="BackOfficeCardPaymentEntries" method="post">
    <table class="table" id="bank-payments-table">
        <thead>
            <tr>
                <th>Store</th>
                <th>Card Type</th>
                <th>Amount</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <select class="form-control" name="BackOfficeCardPaymentEntries[0].StoreId">
                        <option value="">Select Store</option>
                        @foreach (var store in Model.Stores)
                        {
                            <option value="@store.Id">@store.Name</option>
                        }
                    </select>
                </td>
                <td>
                    <select class="form-control" name="BackOfficeCardPaymentEntries[0].CardTypeId">
                        <option value="">Select Card Type</option>
                        @foreach (var cardType in Model.CardTypes)
                        {
                            <option value="@cardType.Id">@cardType.Name</option>
                        }
                    </select>
                </td>
                <td>
                    <input type="number" step="0.01" class="form-control" name="BackOfficeCardPaymentEntries[0].Amount" />
                </td>
                <td>
                    <input type="date" name="BackOfficeCardPaymentEntries[0].Date" value="@DateTime.Now.ToString("yyyy-MM-dd")" />
                </td>
                <td>
                    <button type="button" class="btn btn-danger remove-row">Remove</button>
                </td>
            </tr>
        </tbody>
    </table>


    <button type="button" class="btn btn-primary" id="add-row">Add Row</button>
    <input type="submit" value="Submit" class="btn btn-success" />
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        $(document).ready(function () {
            var rowIdx = 1;

            $('#add-row').click(function () {
                var newRow = `<tr>
                                                        <td>
                                                            <select class="form-control" name="BackOfficeCardPaymentEntries[${rowIdx}].StoreId">
                                                                <option value="">Select Store</option>
        @foreach (var store in Model.Stores)
        {
                                                                                    <option value="@store.Id">@store.Name</option>
        }
                                                            </select>
                                                        </td>
                                                        <td>
                                                                <select class="form-control" name="BackOfficeCardPaymentEntries[${rowIdx}].CardTypeId">
                                                                <option value="">Select Supplier</option>
        @foreach (var cardType in Model.CardTypes)
        {
                                                                                    <option value="@cardType.Id">@cardType.Name</option>
        }
                                                            </select>
                                                        </td>
                                                        <td>
                                                            <input type="number" step="0.01" class="form-control" name="BackOfficeCardPaymentEntries[${rowIdx}].Amount" />
                                                        </td>
                                                                <td>
                                                                     <input type="date" name="BackOfficeCardPaymentEntries[${rowIdx}].Date" value="@DateTime.Now.ToString("yyyy-MM-dd")" />
                                                                </td>
                                                        <td>
                                                            <button type="button" class="btn btn-danger remove-row">Remove</button>
                                                        </td>
                                                    </tr>`;
                $('#bank-payments-table tbody').append(newRow);
                rowIdx++;
            });

            $('#bank-payments-table').on('click', '.remove-row', function () {
                $(this).closest('tr').remove();
            });
        });
    </script>
}
