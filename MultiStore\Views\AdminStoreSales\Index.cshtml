﻿@model MultiStore.Controllers.AdminStoreSalesController.AdminSaleViewModal
@{
    ViewData["Title"] = "Sales";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2>Sales</h2>

<form id="dateForm" asp-action="Index" method="post">
    <div class="form-group">
        <label for="datepicker1">Start Date</label>
        <input type="date" id="datepicker1" name="StartDate" class="form-control" />
    </div>
    <div class="form-group">
        <label for="datepicker2">End Date</label>
        <input type="date" id="datepicker2" name="EndDate" class="form-control" />
    </div>

    <hr />

    <div class="form-group">
        <label for="datepicker3">Pick a Date</label>
        <input type="date" id="datepicker3" name="SingleDate" class="form-control" />
    </div>
    <div class="form-group">
        <label for="period">Period</label>
        <select id="period" name="Period" class="form-control">
            <option value="">Select a period</option>
            <option value="Week">Week</option>
            <option value="Month">Month</option>
            <option value="3Months">3 Months</option>
            <option value="Year">Year</option>
        </select>
    </div>

    <button type="submit" name="submitButton" value="Product" style="width: 100%;margin-bottom: 0.5rem" class="btn btn-primary btn-block"> Manage Product Sales</button>
    <button type="submit" name="submitButton" value="Service" style="width: 100%;margin-bottom: 0.5rem" class="btn btn-primary btn-block"> Manage Service Sales</button>
    <button type="submit" name="submitButton" value="ScratchCard" style="width: 100%;margin-bottom: 0.5rem" class="btn btn-primary btn-block"> Scratch Ticket Sales</button>
    <button type="submit" name="submitButton" value="Fuel" style="width: 100%;margin-bottom: 0.5rem" class="btn btn-primary btn-block"> Manage Fuel Sales</button>


</form>

