﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MultiStore.Migrations
{
    public partial class migration94 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StorePaymentMethods_PaymentCards_PaymentCardId",
                table: "StorePaymentMethods");

            migrationBuilder.AlterColumn<int>(
                name: "PaymentCardId",
                table: "StorePaymentMethods",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddForeignKey(
                name: "FK_StorePaymentMethods_PaymentCards_PaymentCardId",
                table: "StorePaymentMethods",
                column: "PaymentCardId",
                principalTable: "PaymentCards",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StorePaymentMethods_PaymentCards_PaymentCardId",
                table: "StorePaymentMethods");

            migrationBuilder.AlterColumn<int>(
                name: "PaymentCardId",
                table: "StorePaymentMethods",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_StorePaymentMethods_PaymentCards_PaymentCardId",
                table: "StorePaymentMethods",
                column: "PaymentCardId",
                principalTable: "PaymentCards",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
