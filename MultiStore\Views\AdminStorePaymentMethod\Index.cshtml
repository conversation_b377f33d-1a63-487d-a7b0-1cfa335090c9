﻿@model IEnumerable<MultiStore.Persistance.StorePaymentMethod>

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Store Payment Methods</h1>

<h2>Card Payments</h2>
<table class="table">
    <thead>
        <tr>
            <th>Store</th>
            <th>Payment Card</th>
            <th>Amount</th>
            <th>Date</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in (List<MultiStore.Persistance.StorePaymentMethod>)ViewData["CardPayments"])
        {
            <tr>
                <td>@item.Store.Name</td>
                <td>@item.PaymentCard?.Name</td>
                <td>@item.Amount</td>
                <td>@item.Date.ToShortDateString()</td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-primary">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-danger">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>

