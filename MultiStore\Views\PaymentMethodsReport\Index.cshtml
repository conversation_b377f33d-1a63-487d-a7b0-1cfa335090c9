﻿@model MultiStore.Models.SaleReportSelectViewModal
@{
    ViewData["Title"] = "Payment Methods Report";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h2> Payment Methods Report</h2>

<form id="dateForm" asp-action="Submit" method="post">
    <div class="form-group">
        <label for="datepicker1">Start Date</label>
        <input type="date" id="datepicker1" name="StartDate" class="form-control" />
    </div>
    <div class="form-group">
        <label for="datepicker2">End Date</label>
        <input type="date" id="datepicker2" name="EndDate" class="form-control" />
    </div>

    <hr />

    <div class="form-group">
        <label for="datepicker3">Pick a Date</label>
        <input type="date" id="datepicker3" name="SingleDate" class="form-control" />
    </div>
    <div class="form-group">
        <label for="period">Period</label>
        <select id="period" name="Period" class="form-control">
            <option value="">Select a period</option>
            <option value="Week">Week</option>
            <option value="Month">Month</option>
            <option value="3Months">3 Months</option>
            <option value="Year">Year</option>
        </select>
    </div>

    <div class="form-group">
        <label for="companySelect">Company</label>
        <select id="companySelect" class="form-control" asp-for="SelectedCompanyId">
            <option value="">-- Select Company --</option>
            @foreach (var company in Model.Companies)
            {
                <option value="@company.Id">@company.Name</option>
            }
        </select>
    </div>
    <div class="form-group">
        <label for="storeSelect">Store</label>
        <select id="storeSelect" class="form-control" asp-for="SelectedStoreId">
            <option value="">-- Select Store --</option>
            @foreach (var store in Model.Stores)
            {
                <option value="@store.Id" data-company-id="@store.CompanyId">@store.Name</option>
            }
        </select>
    </div>

    <button type="submit" class="btn btn-primary">Submit</button>
</form>

@section Scripts {
    <script>

        $(document).ready(function () {
            $('#companySelect').change(function () {
                var selectedCompanyId = $(this).val();
                $('#storeSelect option').each(function () {
                    var storeCompanyId = $(this).data('company-id');
                    if (selectedCompanyId === "" || storeCompanyId == selectedCompanyId) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
                $('#storeSelect').val('');
            });
        });

        $(function () {
            $("#datepicker1, #datepicker2, #datepicker3").datepicker({
                dateFormat: 'yy-mm-dd'
            });

            $("#dateForm").submit(function (event) {
                var startDate = $("#datepicker1").val();
                var endDate = $("#datepicker2").val();
                var singleDate = $("#datepicker3").val();
                var period = $("#period").val();

                if ((startDate && endDate) || (singleDate && period)) {
                    return true;
                } else {
                    alert("Please fill either the start and end dates or the single date and period.");
                    event.preventDefault();
                    return false;
                }
            });
        });
    </script>
}
