﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class EmailAddressesController : Controller
    {
        private readonly Data _context;

        public EmailAddressesController(Data context)
        {
            _context = context;
        }

        // GET: EmailAddresses
        public async Task<IActionResult> Index()
        {
            return View(await _context.EmailAddresses.ToListAsync());
        }

        // GET: EmailAddresses/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var emailAddress = await _context.EmailAddresses
                .FirstOrDefaultAsync(m => m.Id == id);
            if (emailAddress == null)
            {
                return NotFound();
            }

            return View(emailAddress);
        }

        // GET: EmailAddresses/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: EmailAddresses/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Address,Enabled")] EmailAddress emailAddress)
        {
            //  if (ModelState.IsValid)
            //  {
            _context.Add(emailAddress);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
            //   }
            return View(emailAddress);
        }

        // GET: EmailAddresses/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var emailAddress = await _context.EmailAddresses.FindAsync(id);
            if (emailAddress == null)
            {
                return NotFound();
            }
            return View(emailAddress);
        }

        // POST: EmailAddresses/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Address,Enabled")] EmailAddress emailAddress)
        {
            if (id != emailAddress.Id)
            {
                return NotFound();
            }

            //    if (ModelState.IsValid)
            //    {
            try
            {
                _context.Update(emailAddress);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmailAddressExists(emailAddress.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Index));
            
        }

        // GET: EmailAddresses/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var emailAddress = await _context.EmailAddresses
                .FirstOrDefaultAsync(m => m.Id == id);
            if (emailAddress == null)
            {
                return NotFound();
            }

            return View(emailAddress);
        }

        // POST: EmailAddresses/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var emailAddress = await _context.EmailAddresses.FindAsync(id);
            _context.EmailAddresses.Remove(emailAddress);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool EmailAddressExists(int id)
        {
            return _context.EmailAddresses.Any(e => e.Id == id);
        }
    }
}
