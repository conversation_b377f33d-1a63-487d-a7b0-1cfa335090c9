﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using System.Runtime.InteropServices;
using static MultiStore.Controllers.GrossMarginReportController;
using static System.Formats.Asn1.AsnWriter;

namespace MultiStore.Controllers
{
    public class FuelLetersReportController : Controller
    {

        private readonly Data _context;

        public FuelLetersReportController(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            ReportViewModel reportViewModel = new ReportViewModel();
            reportViewModel.Companies = _context.Companies.ToList();
            reportViewModel.Stores = _context.Stores.ToList();

            return View(reportViewModel);
        }

        [HttpPost]
        public IActionResult Submit(ReportViewModel model)
        {
            try
            {
                bool DueDate = false;

                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;
                PurchaseReportViewModel p = new();

                if (model.StartDate != null && model.EndDate != null)
                {
                    if (model.StartDate > model.EndDate)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDate;
                        endDate = (DateTime)model.EndDate;
                    }
                }
                else if (model.SingleDate != null)
                {


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {
                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDate.Value.AddMonths(-2);
                            endDate = model.SingleDate.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }

                }

                List<FuelLitersViewModel> list = new List<FuelLitersViewModel>();


                if (model.SelectedStoreId != 0)
                {


                  list = GetFuelLitersByStoreId(model.SelectedStoreId, startDate, endDate);



                }
                else if (model.SelectedCompanyId != 0)
                {
                    var stores = _context.Stores.Where(s => s.CompanyId == model.SelectedCompanyId).ToList();
                    foreach (var store in stores)
                    {
                        list.AddRange(GetFuelLitersByStoreId(store.Id, startDate, endDate));
                    }

                }
                else //both compnay id and store id =0 
                {
                    var stores = _context.Stores.ToList();
                    foreach (var store in stores)
                    {
                        list.AddRange(GetFuelLitersByStoreId(store.Id, startDate, endDate));
                    }
                }



                ViewData["StartDate"] = startDate.ToShortDateString();
                ViewData["EndDate"] = endDate.ToShortDateString();

                ViewData["StoreName"] = (model.SelectedStoreId != 0) ? _context.Stores
                 .Where(s => s.Id == model.SelectedStoreId)
                 .FirstOrDefault().Name : "All Stores";

                ViewData["CompanyName"] = (model.SelectedCompanyId != 0) ? _context.Companies
                    .FirstOrDefault(c => c.Id == model.SelectedCompanyId).Name : "All Companies";

                return View("FuelLitersReport", list);

            }
            catch (Exception ex)
            {

                return View("Index");
            }
        }

        public List<FuelLitersViewModel> GetFuelLitersByStoreId(int storeId, DateTime startDate, DateTime endDate)
        {
            var store = _context.Stores.Include(s => s.Company)
                .First(s => s.Id == storeId);

            var fuelSales = _context.StoreFuelSales
                .Where(s => s.StoreId == storeId && s.Day.Date >= startDate && s.Day.Date <= endDate).ToList();

            var fuels = _context.Fuels.ToList();

            var result = new List<FuelLitersViewModel>();

            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                foreach (var fuel in fuels)
                {
                    var liters = fuelSales.Where(s => s.Day.Date == date.Date && s.FuelId == fuel.Id).Sum(s => s.Liters);

                    result.Add(new FuelLitersViewModel()
                    {
                        CompanyName = store.Company.Name,
                        FuelName = fuel.Name,
                        Liters = liters,
                        StoreName = store.Name,
                        Date = date
                    });
                }
            }

            return result;
        }

        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }


        public class FuelLitersViewModel
        {
            public string StoreName { get; set; }
            public string CompanyName { get; set; }

            public string FuelName { get; set; }

            public decimal Liters { get; set; }
            public DateTime Date { get; set; }

        }
    }
}
