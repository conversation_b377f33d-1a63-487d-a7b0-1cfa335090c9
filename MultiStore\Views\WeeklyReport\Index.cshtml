﻿@model MultiStore.Controllers.WeeklyReportController.YourViewModel

@{
    ViewData["Title"] = "Select a Week";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}




<form asp-action="SubmitWeek" method="post">


    <div class="form-group">
        <label for="companySelect">Company</label>
        <select id="companySelect" class="form-control" name="SelectedCompanyId">
            <option value="">-- Select Company --</option>
            @foreach (var company in (SelectList)ViewData["Companies"])
            {
                <option value="@company.Value">@company.Text</option>
            }
        </select>
    </div>


    <h2>Select a Week</h2>


    <div class="form-group">
        <label for="weekSelection">Select a week:</label>
        <input type="week" id="weekSelection" name="SelectedWeek" class="form-control" required />
    </div>

    <div>
        @Html.CheckBoxFor(model => model.IsChecked) <!-- Checkbox input -->
        <label asp-for="IsChecked">Yearly Columns?</label>
    </div>

    <button type="submit" class="btn btn-primary">Submit</button>
</form>

