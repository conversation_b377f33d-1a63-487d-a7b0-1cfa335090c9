﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class CompanyPurchaseController : Controller
    {
        private readonly Data _context;

        public CompanyPurchaseController(Data context)
        {
            _context = context;
        }


        public IActionResult Index()
        {
            ReportViewModel reportViewModel = new ReportViewModel();
            reportViewModel.Companies = _context.Companies.ToList();

            return View(reportViewModel);
        }


        [HttpPost]
        public IActionResult Submit(ReportViewModel model)
        {
            try
            {
                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;
                CompnayPurchaseReportViewModel p = new();

                if (model.StartDate != null && model.EndDate != null)
                {
                    if (model.StartDate > model.EndDate)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDate;
                        endDate = (DateTime)model.EndDate;
                    }
                }
                else if (model.SingleDate != null)
                {


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {
                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDate.Value.AddMonths(-2);
                            endDate = model.SingleDate.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }

                }

                if (model.SelectedCompanyId != 0)
                {

                    List<CompanyPurchaseEntry> purchaseEntries = new();


                    purchaseEntries = _context.CompanyPurchaseEntries
                    .Include(p => p.Supplier)
                    .Include(p => p.Company)
                 .Where(p => p.CompanyId == model.SelectedCompanyId && p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                    .ToList();






                    p.StartDate = startDate;
                    p.EndDate = endDate;

                    p.CompnayName = _context.Companies.FirstOrDefault(c => c.Id == model.SelectedCompanyId).Name;

                    List<CompanyPurchaseEntryDTO> purchaseEntryDtos = new List<CompanyPurchaseEntryDTO>();

                    foreach (var item in purchaseEntries)
                    {
                        purchaseEntryDtos.Add(new CompanyPurchaseEntryDTO()
                        {
                            CompnayName = item.Company.Name,
                             InvoiceNumber = item.InvoiceNumber
                             
                            ,
                            Date = item.Date
                            ,
                            SupplierName = item.Supplier.Name
                            ,
                            Gross = item.Gross,
                            Vat = item.Vat
                            ,
                            Net = item.Net,
                        });
                    }


                    p.PurchaseEntries = purchaseEntryDtos;


                    return View("CompanyPurchaseReport", p);
                }
                else //both compnay id and store id =0 
                {

                    List<CompanyPurchaseEntry> purchaseEntries = new();


                    purchaseEntries = _context.CompanyPurchaseEntries
                    .Include(p => p.Supplier)
                    .Include(p => p.Company)
                 .Where(p => p.Date.Date >= startDate.Date && p.Date.Date <= endDate.Date)
                    .ToList();






                    p.StartDate = startDate;
                    p.EndDate = endDate;

                    p.CompnayName = "All";

                    List<CompanyPurchaseEntryDTO> purchaseEntryDtos = new List<CompanyPurchaseEntryDTO>();

                    foreach (var item in purchaseEntries)
                    {
                        purchaseEntryDtos.Add(new CompanyPurchaseEntryDTO()
                        {
                            CompnayName = item.Company.Name,
                            InvoiceNumber = item.InvoiceNumber

                            ,
                            Date = item.Date
                            ,
                            SupplierName = item.Supplier.Name
                            ,
                            Gross = item.Gross,
                            Vat = item.Vat
                            ,
                            Net = item.Net,
                        });
                    }


                    p.PurchaseEntries = purchaseEntryDtos;


                    return View("CompanyPurchaseReport", p);
                }


            }
            catch (Exception ex)
            {

                return View("Index");
            }
        }

        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0)
                delta -= 7;
            return date.AddDays(delta);
        }
    }
}
