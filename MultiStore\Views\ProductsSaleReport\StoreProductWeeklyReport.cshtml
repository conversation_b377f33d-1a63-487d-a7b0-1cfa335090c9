﻿@model MultiStore.Controllers.ProductsSaleReportController.ProductSaleWeeklyReportViewModel

@{
    ViewData["Title"] = "Products Sales Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed
                                                     // Update the layout path as needed
                                                     // Update the layout path as needed
                                              // var saleAmountSum = Model.Sum(item => item.);

}

<h1>Products Sales Report</h1>

<div>
    <p><strong>Store:</strong> @ViewData["StoreName"]</p>

    <p><strong>Company:</strong> @ViewData["CompanyName"]</p>
    <p><strong>From:</strong> @ViewData["StartDate"] <strong>To:</strong> @ViewData["EndDate"]</p>
</div>


<table class="table">
    <thead>
        <tr>
            <th>Store</th>
            <th>Product</th>
            <th>Mon</th>
            <th>Tue</th>
            <th>Wed</th>
            <th>Thu</th>
            <th>Fri</th>
            <th>Sat</th>
            <th>Sun</th>
            <th>Total Amount</th>
            <th>Time Span</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model.Records)
        {
            <tr>
                <td>@item?.Store?.Name</td>
                <td>@item?.Product?.Name</td>
                <td>@item?.Monday.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@item?.Tuesday.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@item?.Wednesday.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@item?.Thursday.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@item?.Friday.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@item?.Saturday.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@item?.Sunday.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@item?.Total.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@item?.Day.ToShortDateString()</td>
            </tr>
        }
        @*<tr>
            <td colspan="2"><strong>Total</strong></td>
            <td><strong>@saleAmountSum.ToString("C", new System.Globalization.CultureInfo("en-GB"))</strong></td>
            <td></td>
        </tr>*@
    </tbody>
</table>
