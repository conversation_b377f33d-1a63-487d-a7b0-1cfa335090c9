﻿namespace MultiStore.Persistance
{
    public class StorePaidIn
    {
        public int Id { get; set; }
        public int TransferredFromStoreDayId { get; set; }
        public int TransferToStoreId { get; set; }
        public DateTime TransferredFromDate { get; set; }
        public DateTime TransfeffedToDate { get; set; }
        public decimal Amount { get; set; }

        public StorePaidInState StorePaidInState { get; set; }

    }

    public enum StorePaidInState
    {
        Pending,
        Accepted,
        Rejected
    }
}
