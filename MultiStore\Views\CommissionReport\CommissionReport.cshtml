﻿@model MultiStore.Models.CommisionReportDisplayViewModel

@{
    ViewData["Title"] = "Commission Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed
                                                     // Update the layout path as needed

    // Calculate totals for Income and Outcome
    var incomeGross = Model.CommisionEntries.Where(e => e.Type == "Income").Sum(e => e.Gross);
    var incomeVat = Model.CommisionEntries.Where(e => e.Type == "Income").Sum(e => e.Vat);
    var incomeNet = Model.CommisionEntries.Where(e => e.Type == "Income").Sum(e => e.Net);

    var outcomeGross = Model.CommisionEntries.Where(e => e.Type == "Outcome").Sum(e => e.Gross);
    var outcomeVat = Model.CommisionEntries.Where(e => e.Type == "Outcome").Sum(e => e.Vat);
    var outcomeNet = Model.CommisionEntries.Where(e => e.Type == "Outcome").Sum(e => e.Net);

    var netGross = incomeGross - outcomeGross;
    var netVat = incomeVat - outcomeVat;
    var netNet = incomeNet - outcomeNet;
}

<h2>Commission Report</h2>

<div>
    <p><strong>Company:</strong> @Model.CompnayName</p>
    <p><strong>Store:</strong> @Model.StoreName</p>
    <p><strong>From:</strong> @Model.StartDate?.ToString("MM/dd/yyyy") <strong>To:</strong> @Model.EndDate?.ToString("MM/dd/yyyy")</p>
    <p><strong>Type:</strong> @Model.TransactionType</p>
</div>

<table class="table table-bordered">
    <thead>
        <tr>
            <th>Date</th>
            <th>Type</th>
            <th>Store Name</th>
            <th>Invoice Number</th>
            <th>Supplier Name</th>
            <th>Gross</th>
            <th>Vat</th>
            <th>Net</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var entry in Model.CommisionEntries)
        {
            <tr>
                <td>@entry.Date.ToString("MM/dd/yyyy")</td>
                <td>@entry.Type</td>
                <td>@entry.StoreName</td>
                <td>@entry.InvoiceNumber</td>
                <td>@entry.SupplierName</td>
                <td>@entry.Gross.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@entry.Vat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@entry.Net.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            </tr>
        }
    </tbody>
    <tfoot>

        <tr>
            <td colspan="5" class="text-right"><strong>Income Total:</strong></td>
            <td>@incomeGross.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            <td>@incomeVat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            <td>@incomeNet.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
        </tr>
        <tr>
            <td colspan="5" class="text-right"><strong>Outcome Total:</strong></td>
            <td>@outcomeGross.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            <td>@outcomeVat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            <td>@outcomeNet.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
        </tr>
        <tr>
            <td colspan="5" class="text-right"><strong>Net Total (Income - Outcome):</strong></td>
            <td>@netGross.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            <td>@netVat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            <td>@netNet.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
        </tr>
    </tfoot>
</table>
