﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiStore.Models;
using MultiStore.Persistance;
using System;
using System.Collections.Generic;
using System.Linq;

namespace MultiStore.Controllers
{
    public class CardPaymentReportController : Controller
    {
        private readonly Data _context;

        public CardPaymentReportController(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            SaleReportSelectViewModal reportViewModel = new SaleReportSelectViewModal
            {
                Stores = _context.Stores.ToList(),
                Companies = _context.Companies.ToList()
            };

            return View(reportViewModel);
        }

        public class CardPayment
        {
            public DateTime Date { get; set; }
            public string StoreName { get; set; }
            public string CardType { get; set; }
            public decimal Store { get; set; }
            public decimal Account { get; set; }
        }

        [HttpPost]
        public IActionResult Submit(SaleReportSelectViewModal model)
        {
            try
            {
                DateTime startDate = DateTime.MinValue;
                DateTime endDate = DateTime.MinValue;

                if (model.StartDate != null && model.EndDate != null)
                {
                    if (model.StartDate > model.EndDate)
                    {
                        return View("Index");
                    }
                    else
                    {
                        startDate = (DateTime)model.StartDate;
                        endDate = (DateTime)model.EndDate;
                    }
                }
                else if (model.SingleDate != null)
                {


                    //calculate start and end date depening on single date 
                    //and period
                    switch (model.Period.ToLower())
                    {
                        case "week":
                            startDate = GetFirstDateOfWeek(model.SingleDate.Value);
                            endDate = startDate.AddDays(6);
                            break;
                        case "month":
                            startDate = new DateTime(model.SingleDate.Value.Year, model.SingleDate.Value.Month, 1);
                            endDate = startDate.AddMonths(1).AddDays(-1);
                            break;
                        case "3months":
                            startDate = model.SingleDate.Value.AddMonths(-2);
                            endDate = model.SingleDate.Value;
                            break;
                        case "year":
                            startDate = new DateTime(model.SingleDate.Value.Year, 1, 1);
                            endDate = new DateTime(model.SingleDate.Value.Year, 12, 31);
                            break;
                        default:
                            return View("Index");
                    }

                }

                List<CardPayment>? cardPayments = new();

                DateTime storesStartDay = _context.StoreDays.First().Day.Date;


                if (model.SelectedStoreId != 0)
                {

                    var storeDays = _context.StoreDays
                        .Where(s => s.StoreId == model.SelectedStoreId && s.Day.Date >= startDate.Date && s.Day.Date <= endDate.Date)
                        .ToList();


                    var stores = _context.Stores.Where(s => s.Id == model.SelectedStoreId).ToList();

                    var cardTypes = _context.CardTypes.ToList();


                    foreach (var storeday in storeDays)
                    {

                        var store = stores.First();

                        foreach (var cardtype in cardTypes)
                        {
                            //එක දවසක එක store එකක හැම card type එකටම අදාළ records
                            var cardTypesBackEntry = _context.BackOfficeCardPaymentEntries
                                 .FirstOrDefault(b => b.Date.Date == storeday.Day.Date && b.StoreId == store.Id && b.CardTypeId == cardtype.Id);

                            if (cardTypesBackEntry != null)
                            {

                                CardPayment cardPayment = new CardPayment
                                {
                                    Date = storeday.Day.Date,
                                    CardType = cardtype.Name,
                                    StoreName = store.Name,
                                    Account = cardTypesBackEntry.Amount,

                                };

                                List<int> cardIds = _context.PaymentCards.Where(pc => pc.CardTypeId == cardtype.Id).Select(c => c.Id).ToList();

                                if (cardIds.Any())
                                {
                                    var amount = _context.StorePaymentMethods
                                          .Where(sp => sp.PaymentCardId != null && cardIds.Contains((int)sp.PaymentCardId) &&
                                           sp.Date.Date == storeday.Day.Date &&
                                           sp.StoreId == store.Id).Sum(sp => sp.Amount);

                                    if (amount == 0) continue;

                                    cardPayment.Store = amount;
                                }

                                cardPayments.Add(cardPayment);

                            }

                            else
                            {
                                CardPayment cardPayment = new CardPayment
                                {
                                    Date = storeday.Day.Date,
                                    CardType = cardtype.Name,
                                    StoreName = store.Name,

                                };

                                List<int> cardIds = _context.PaymentCards.Where(pc => pc.CardTypeId == cardtype.Id).Select(c => c.Id).ToList();

                                if (cardIds.Any())
                                {
                                    var amount = _context.StorePaymentMethods
                                          .Where(sp => sp.PaymentCardId != null && cardIds.Contains((int)sp.PaymentCardId) &&
                                           sp.Date.Date == storeday.Day.Date &&
                                           sp.StoreId == store.Id).Sum(sp => sp.Amount);

                                    if (amount == 0) continue;

                                    cardPayment.Store = amount;
                                }

                                cardPayments.Add(cardPayment);

                            }

                        }



                    }


                }
                else if (model.SelectedCompanyId != 0)
                {

                    var stores = _context.Stores.Include(s => s.Company)
                        .Where(s => s.CompanyId == model.SelectedCompanyId).ToList();

                    List<StoreDay> storeDays = new List<StoreDay>();

                    foreach (var store in stores)
                    {
                        storeDays.AddRange(_context.StoreDays
                    .Where(s => s.StoreId == store.Id && s.Day.Date >= startDate.Date && s.Day.Date <= endDate.Date)
                    .ToList());
                    }

                    var cardTypes = _context.CardTypes.ToList();


                    foreach (var storeday in storeDays)
                    {

                        var store = stores.First(s => s.Id == storeday.StoreId);

                        foreach (var cardtype in cardTypes)
                        {
                            //එක දවසක එක store එකක හැම card type එකටම අදාළ records
                            var cardTypesBackEntry = _context.BackOfficeCardPaymentEntries
                                 .FirstOrDefault(b => b.Date.Date == storeday.Day.Date && b.StoreId == store.Id && b.CardTypeId == cardtype.Id);

                            if (cardTypesBackEntry != null)
                            {

                                CardPayment cardPayment = new CardPayment
                                {
                                    Date = storeday.Day.Date,
                                    CardType = cardtype.Name,
                                    StoreName = store.Name,
                                    Account = cardTypesBackEntry.Amount,

                                };

                                List<int> cardIds = _context.PaymentCards.Where(pc => pc.CardTypeId == cardtype.Id).Select(c => c.Id).ToList();

                                if (cardIds.Any())
                                {
                                    var amount = _context.StorePaymentMethods
                                          .Where(sp => sp.PaymentCardId != null && cardIds.Contains((int)sp.PaymentCardId) &&
                                           sp.Date.Date == storeday.Day.Date &&
                                           sp.StoreId == store.Id).Sum(sp => sp.Amount);

                                    if (amount == 0) continue;

                                    cardPayment.Store = amount;
                                }

                                cardPayments.Add(cardPayment);

                            }

                            else
                            {
                                CardPayment cardPayment = new CardPayment
                                {
                                    Date = storeday.Day.Date,
                                    CardType = cardtype.Name,
                                    StoreName = store.Name,

                                };

                                List<int> cardIds = _context.PaymentCards.Where(pc => pc.CardTypeId == cardtype.Id).Select(c => c.Id).ToList();

                                if (cardIds.Any())
                                {
                                    var amount = _context.StorePaymentMethods
                                          .Where(sp => sp.PaymentCardId != null && cardIds.Contains((int)sp.PaymentCardId) &&
                                           sp.Date.Date == storeday.Day.Date &&
                                           sp.StoreId == store.Id).Sum(sp => sp.Amount);

                                    if (amount == 0) continue;


                                    cardPayment.Store = amount;
                                }

                                cardPayments.Add(cardPayment);

                            }

                        }



                    }




                }
                else //both compnay id and store id =0 
                {
                    var storeDays = _context.StoreDays
                      .Where(s => s.Day.Date >= startDate.Date && s.Day.Date <= endDate.Date)
                      .ToList();





                    var stores = _context.Stores.ToList();

                    var cardTypes = _context.CardTypes.ToList();


                    foreach (var storeday in storeDays)
                    {

                        foreach (var store in stores)
                        {
                            foreach (var cardtype in cardTypes)
                            {
                                //එක දවසක එක store එකක හැම card type එකටම අදාළ records
                                var cardTypesBackEntry = _context.BackOfficeCardPaymentEntries
                                     .FirstOrDefault(b => b.Date.Date == storeday.Day.Date && b.StoreId == store.Id && b.CardTypeId == cardtype.Id);

                                if (cardTypesBackEntry != null)
                                {

                                    CardPayment cardPayment = new CardPayment
                                    {
                                        Date = storeday.Day.Date,
                                        CardType = cardtype.Name,
                                        StoreName = store.Name,
                                        Account = cardTypesBackEntry.Amount
                                    };

                                    List<int> cardIds = _context.PaymentCards.Where(pc => pc.CardTypeId == cardtype.Id).Select(c => c.Id).ToList();

                                    if (cardIds.Any())
                                    {
                                        var amount = _context.StorePaymentMethods
                                              .Where(sp => sp.PaymentCardId != null && cardIds.Contains((int)sp.PaymentCardId) &&
                                               sp.Date.Date == storeday.Day.Date &&
                                               sp.StoreId == store.Id).Sum(sp => sp.Amount);

                                        if (amount == 0) continue;


                                        cardPayment.Store = amount;
                                    }

                                    cardPayments.Add(cardPayment);

                                }



                            }

                        }




                    }
                }



                ViewData["StartDate"] = startDate.ToShortDateString();
                ViewData["EndDate"] = endDate.ToShortDateString();

                ViewData["StoreName"] = (model.SelectedStoreId != 0) ? _context.Stores
                 .Where(s => s.Id == model.SelectedStoreId)
                 .FirstOrDefault().Name : "All Stores";

                ViewData["CompanyName"] = (model.SelectedCompanyId != 0) ? _context.Companies
                    .FirstOrDefault(c => c.Id == model.SelectedCompanyId).Name : "All Companies";

                return View("CardPaymentReport", cardPayments);
            }
            catch (Exception ex)
            {

                return View("Index");
            }
        }



        private DateTime GetFirstDateOfWeek(DateTime date)
        {
            int delta = DayOfWeek.Monday - date.DayOfWeek;
            if (delta > 0) delta -= 7;
            return date.AddDays(delta);
        }
    }
}
