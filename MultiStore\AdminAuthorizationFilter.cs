﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace MultiStore
{
    public class AdminAuthorizationFilter : IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            if (context.HttpContext.Session.GetInt32("UserId") == null)
            {
                context.Result = new RedirectToActionResult("Login", "Home", null);
            }
            else if (context.HttpContext.Session.GetString("UserType") != "Admin")
            {
                context.Result = new RedirectToActionResult("Restricted", "Home", null);
            }
        }
    }
}
