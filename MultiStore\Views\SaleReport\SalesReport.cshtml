﻿@model MultiStore.Models.SaleReportDisplayViewModal

@{
    ViewData["Title"] = "Stores Total Sales Report";
    Layout = "~/Views/Shared/_ReportsLayout.cshtml"; // Update the layout path as needed
                                                     // Update the layout path as needed
}

<h2>Stores Total Sales Report</h2>

<div>
    <p><strong>Company Name:</strong> @Model.CompnayName</p>
    <p><strong>Start Date:</strong> @Model.StartDate?.ToString("yyyy-MM-dd")</p>
    <p><strong>End Date:</strong> @Model.EndDate?.ToString("yyyy-MM-dd")</p>
</div>

<table class="table table-bordered">
    <thead>
        <tr>
            <th>Store Name</th>

            @foreach (var productCat in Model.salesDtos.First().ProductCatagoriesSale.Keys)
            {
                <th>@productCat</th>
            }
            @foreach (var productCat in Model.salesDtos.First().ServiceCatagoriesSale.Keys)
            {
                <th>@productCat</th>
            }
            <th>Fuel Sale</th>
            <th>Scratch Cards Sale</th>
            <th>General Payout</th>
            <th>Specific Payout</th>
            <th>Day's Sale</th>
            <th>Day's Spendings</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var storeGroup in Model.salesDtos.GroupBy(s => s.StoreName))
        {
            var storeName = storeGroup.Key;
            var storeSales = storeGroup.ToList();
            <tr>
                <td>@storeName</td>

                @foreach (var productCat in storeSales.First().ProductCatagoriesSale.Keys)
                {
                    <td>@storeSales.Sum(s => s.ProductCatagoriesSale[productCat]).ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                }


                @foreach (var serviceCat in storeSales.First().ServiceCatagoriesSale.Keys)
                {
                    <td>@storeSales.Sum(s => s.ServiceCatagoriesSale[serviceCat]).ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                }

                <td>@storeSales.Sum(s => s.FuelSale).ToString("C", new System.Globalization.CultureInfo("en-GB"))</td> 


                <td>@storeSales.Sum(s => s.ScratchCardsSale).ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@storeSales.Sum(s => s.GeneralPayout).ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@storeSales.Sum(s => s.SpecificPayout).ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@storeSales.Sum(s => s.DaysSale).ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@storeSales.Sum(s => s.DaysSpendings).ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            </tr>
        }
    </tbody>
    <tfoot>
        <tr>
            <td><strong>Total:</strong></td>

            @foreach (var productCat in Model.salesDtos.First().ProductCatagoriesSale.Keys)
            {
                <td>
                    <strong>
                        @Model.salesDtos.Sum(s => s.ProductCatagoriesSale[productCat]).ToString("C", new System.Globalization.CultureInfo("en-GB"))
                    </strong>
                </td>
            }

            

            @foreach (var serviceCat in Model.salesDtos.First().ServiceCatagoriesSale.Keys)
            {
                <td>
                    <strong>
                        @Model.salesDtos.Sum(s => s.ServiceCatagoriesSale[serviceCat]).ToString("C", new System.Globalization.CultureInfo("en-GB"))
                    </strong>
                </td>
            }

            <td>
                <strong>
                    @Model.salesDtos.Sum(s => s.FuelSale).ToString("C", new System.Globalization.CultureInfo("en-GB"))
                </strong>
            </td>

            <td>
                <strong>
                    @Model.salesDtos.Sum(s => s.ScratchCardsSale).ToString("C", new System.Globalization.CultureInfo("en-GB"))
                </strong>
            </td>
            <td>
                <strong>
                    @Model.salesDtos.Sum(s => s.GeneralPayout).ToString("C", new System.Globalization.CultureInfo("en-GB"))
                </strong>
            </td>
            <td>
                <strong>
                    @Model.salesDtos.Sum(s => s.SpecificPayout).ToString("C", new System.Globalization.CultureInfo("en-GB"))
                </strong>
            </td>
            <td>
                <strong>
                    @Model.salesDtos.Sum(s => s.DaysSale).ToString("C", new System.Globalization.CultureInfo("en-GB"))
                </strong>
            </td>
            <td>
                <strong>
                    @Model.salesDtos.Sum(s => s.DaysSpendings).ToString("C", new System.Globalization.CultureInfo("en-GB"))
                </strong>
            </td>
        </tr>
    </tfoot>
</table>
