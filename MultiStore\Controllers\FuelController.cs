﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MultiStore.Persistance;
using System.Linq;
using System.Threading.Tasks;

namespace MultiStore.Controllers
{
    [TypeFilter(typeof(AdminAuthorizationFilter))]
    public class FuelController : Controller
    {
        private readonly Data _context;

        public FuelController(Data context)
        {
            _context = context;
        }

        // GET: Fuel
        public async Task<IActionResult> Index()
        {
            var fuels = _context.Fuels.Include(f => f.FuelType).Include(f => f.VatPercentage);
            return View(await fuels.ToListAsync());
        }

        // GET: Fuel/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var fuel = await _context.Fuels
                .Include(f => f.FuelType)
                .Include(f => f.VatPercentage)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (fuel == null)
            {
                return NotFound();
            }

            return View(fuel);
        }

        // GET: Fuel/Create
        public IActionResult Create()
        {
            ViewData["FuelTypeId"] = new SelectList(_context.FuelTypes, "Id", "Name");
            ViewData["VatPercentageId"] = new SelectList(_context.VatPercentages, "Id", "Percentage");
            return View();
        }

        // POST: Fuel/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Name,FuelTypeId,VatPercentageId")] Fuel fuel)
        {
            // if (ModelState.IsValid)
            // {
            try
            {
                _context.Add(fuel);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                ViewData["Message"] = ex.Message;

                return View(fuel);
            }
            // }
            // ViewData["FuelTypeId"] = new SelectList(_context.FuelTypes, "Id", "Name", fuel.FuelTypeId);
            // ViewData["VatPercentageId"] = new SelectList(_context.VatPercentages, "Id", "Percentage", fuel.VatPercentageId);
            // return View(fuel);
        }

        // GET: Fuel/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var fuel = await _context.Fuels.FindAsync(id);
            if (fuel == null)
            {
                return NotFound();
            }
            ViewData["FuelTypeId"] = new SelectList(_context.FuelTypes, "Id", "Name", fuel.FuelTypeId);
            ViewData["VatPercentageId"] = new SelectList(_context.VatPercentages, "Id", "Percentage", fuel.VatPercentageId);
            return View(fuel);
        }

        // POST: Fuel/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,FuelTypeId,VatPercentageId")] Fuel fuel)
        {
            if (id != fuel.Id)
            {
                return NotFound();
            }

            //  if (ModelState.IsValid)
            //  {
            try
            {
                _context.Update(fuel);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!FuelExists(fuel.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {

                ViewData["Message"] = ex.Message;

                return View(fuel);

            }

            return RedirectToAction(nameof(Index));
            //   }
            //   ViewData["FuelTypeId"] = new SelectList(_context.FuelTypes, "Id", "Name", fuel.FuelTypeId);
            //    ViewData["VatPercentageId"] = new SelectList(_context.VatPercentages, "Id", "Percentage", fuel.VatPercentageId);
            //    return View(fuel);
        }

        // GET: Fuel/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var fuel = await _context.Fuels
                .Include(f => f.FuelType)
                .Include(f => f.VatPercentage)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (fuel == null)
            {
                return NotFound();
            }

            return View(fuel);
        }

        // POST: Fuel/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var fuel = await _context.Fuels.FindAsync(id);
            _context.Fuels.Remove(fuel);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool FuelExists(int id)
        {
            return _context.Fuels.Any(e => e.Id == id);
        }
    }
}
