﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;
using MultiStore.Models;
using MultiStore.Persistance;

namespace MultiStore.Controllers
{
    public class AdminStoreFuelSalesController : Controller
    {
        private readonly Data _context;
        private readonly IUtilityService _utilityService;

        public AdminStoreFuelSalesController(Data context, IUtilityService utilityService)
        {
            _context = context;
            _utilityService = utilityService;
        }

        // GET: AdminStoreFuelSales
        public async Task<IActionResult> Index(DateTime startDate, DateTime endDate)
        {
            var sales = await _context.StoreFuelSales
                .Include(s => s.Store)
                .Include(s => s.Fuel)
                .Where(s => s.Day.Date >= startDate && s.Day.Date <= endDate)

                .ToListAsync();
            return View(sales);
        }



        // GET: AdminStoreFuelSales/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeFuelSales = await _context.StoreFuelSales.FindAsync(id);
            if (storeFuelSales == null)
            {
                return NotFound();
            }

            var viewModel = new StoreFuelSalesViewModel
            {
                StoreFuelSales = storeFuelSales,
                Stores = new SelectList(_context.Stores, "Id", "Name"),
                Fuels = new SelectList(_context.Fuels, "Id", "Name")
            };

            return View(viewModel);
        }

        // POST: AdminStoreFuelSales/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, StoreFuelSalesViewModel viewModel)
        {
            if (id != viewModel.StoreFuelSales.Id)
            {
                return NotFound();
            }

         //   if (ModelState.IsValid)
         //   {
                try
                {
                    _context.Update(viewModel.StoreFuelSales);
                    await _context.SaveChangesAsync();
                    
                  _utilityService. UpdateStoreDaysToReflectChanges(viewModel.StoreFuelSales.StoreId); 
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!StoreFuelSalesExists(viewModel.StoreFuelSales.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }

            return RedirectToAction(nameof(Index), "AdminStoreSales");

            return RedirectToAction(nameof(Index));
        //    }

        //    viewModel.Stores = new SelectList(_context.Stores, "Id", "Name");
            viewModel.Fuels = new SelectList(_context.Fuels, "Id", "Name");
        //    return View(viewModel);
        }

        // GET: AdminStoreFuelSales/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var storeFuelSales = await _context.StoreFuelSales
                .Include(s => s.Store)
                .Include(s => s.Fuel)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (storeFuelSales == null)
            {
                return NotFound();
            }

            return View(storeFuelSales);
        }

        // POST: AdminStoreFuelSales/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var storeFuelSales = await _context.StoreFuelSales.FindAsync(id);
            _context.StoreFuelSales.Remove(storeFuelSales);
            await _context.SaveChangesAsync();
           _utilityService. UpdateStoreDaysToReflectChanges(storeFuelSales.StoreId);

            return RedirectToAction(nameof(Index), "AdminStoreSales");

            return RedirectToAction(nameof(Index));
        }

        // POST: AdminStoreFuelSales/DeleteAll
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteAll()
        {
            var allSales = _context.StoreFuelSales.ToList();
            _context.StoreFuelSales.RemoveRange(allSales);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool StoreFuelSalesExists(int id)
        {
            return _context.StoreFuelSales.Any(e => e.Id == id);
        }



    }
}
