﻿@model MultiStore.Persistance.CompanyPurchaseEntry

@{
    ViewData["Title"] = "Edit Company Purchase Entry";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Edit Company Purchase Entry</h1>

<h4>Company Purchase Entry</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div class="form-group">
                <label asp-for="CompanyId" class="control-label"></label>
                <select asp-for="CompanyId" class="form-control" asp-items="ViewBag.CompanyId"></select>
            </div>

            <div class="form-group">
                <label asp-for="InvoiceNumber" class="control-label"></label>
                <input asp-for="InvoiceNumber" class="form-control" />
            </div>

            <div class="form-group">
                <label asp-for="SupplierId" class="control-label"></label>
                <select asp-for="SupplierId" class="form-control" asp-items="ViewBag.SupplierId"></select>
            </div>
            <div class="form-group">
                <label asp-for="Gross" class="control-label"></label>
                <input asp-for="Gross" class="form-control" />
            </div>
            <div class="form-group">
                <label asp-for="Vat" class="control-label"></label>
                <input asp-for="Vat" class="form-control" />
            </div>
            <div class="form-group">
                <label asp-for="Net" class="control-label"></label>
                <input asp-for="Net" class="form-control" />
            </div>
            <div class="form-group">
                <label asp-for="Date" class="control-label"></label>
                <input asp-for="Date" class="form-control" />
            </div>
            <input type="hidden" asp-for="Id" />
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>
