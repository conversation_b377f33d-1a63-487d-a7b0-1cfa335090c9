﻿@model IEnumerable<MultiStore.Persistance.Store>

@{
    ViewData["Title"] = "Stores";
    Layout = "~/Views/Shared/_Layout.cshtml"; // Update the layout path as needed

}

<h1>Stores</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>

<table class="table">
    <thead>
        <tr>
            <th>Name</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.Name</td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@item.Id">Delete</a> |
                    <a asp-action="EditProducts" asp-route-id="@item.Id">Edit Products</a>
                    <a asp-action="EditServices" asp-route-id="@item.Id">Edit Services</a>
                    <a asp-action="EditScratchCards" asp-route-id="@item.Id">Edit Scractch Cards</a>

                    @if(item.StoreType == MultiStore.Persistance.StoreType.store_with_fuel_station)
                    {
                    <a asp-action="EditFuel" asp-route-id="@item.Id">Edit Fuel</a>
                    }
                    @if(item.Active)
                    {
                        <a asp-action="Deactivate" asp-route-id="@item.Id">Deactivate</a>
                    }
                    else
                    {
                        <a asp-action ="Activate" asp-route-id="@item.Id">Activate</a>

                    }

                </td>
            </tr>
        }
    </tbody>
</table>
