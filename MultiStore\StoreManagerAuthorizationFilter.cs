﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace MultiStore
{
    public class StoreManagerAuthorizationFilter : IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            // Check if the user is logged in
            if (context.HttpContext.Session.GetInt32("UserId") == null)
            {
                context.Result = new RedirectToActionResult("Login", "Home", null);
                return;
            }

            if (context.HttpContext.Session.GetString("UserType") != "Admin")
            {


                // Check if the user is a StoreManager
                if (context.HttpContext.Session.GetString("UserType") != "StoreManager")
                {
                    context.Result = new RedirectToActionResult("Restricted", "Home", null);
                    return;
                }

                // Get the storeId from the query string
                var query = context.HttpContext.Request.Query;
                if (query.TryGetValue("storeId", out var storeIdValues) && int.TryParse(storeIdValues.FirstOrDefault(), out int storeId))
                {
                    // Retrieve the storeId from the session or database to check if it matches
                    int? userStoreId = context.HttpContext.Session.GetInt32("UserStoreId"); // assuming you store this information in the session

                    if (userStoreId == null || userStoreId != storeId)
                    {
                        context.Result = new RedirectToActionResult("Restricted", "Home", null);
                    }
                }
            }
            

        }
    }
}
