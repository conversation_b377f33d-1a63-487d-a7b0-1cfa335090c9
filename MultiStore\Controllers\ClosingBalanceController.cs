﻿using Microsoft.AspNetCore.Mvc;
using MultiStore.Persistance;
using System.Linq;
using System.Collections.Generic;

namespace MultiStore.Controllers
{
    public class ClosingBalanceController : Controller
    {
        private readonly Data _context;

        public ClosingBalanceController(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            var stores = _context.Stores.ToList();
            List<ClosingBalanceViewModel> result = new List<ClosingBalanceViewModel>();
            var yesterday = DateTime.Now.AddDays(-1); // Get yesterday's date

            foreach (var store in stores)
            {
                var item = _context.StoreDays
                                   .Where(s => s.StoreId == store.Id)
                                   .OrderByDescending(s => s.Day)
                                   .FirstOrDefault();

                if (item != null)
                {
                    var noOfDays = (yesterday - item.Day).Days; // Calculate days using yesterday's date

                    result.Add(new ClosingBalanceViewModel()
                    {
                        Date = item.Day,
                        StoreName = store.Name,
                        ClosingBalance = item.CarryForward,
                        NoOfDays = noOfDays // Set the number of days
                    });
                }
            }

            // Sort the result by Date in descending order before passing to the view
            var sortedResult = result.OrderByDescending(r => r.Date).ToList();

            return View(sortedResult);
        }

        public class ClosingBalanceViewModel
        {
            public DateTime Date { get; set; }
            public string StoreName { get; set; }
            public decimal ClosingBalance { get; set; }
            public int NoOfDays { get; set; } // Property for number of days
        }
    }
}
